<?php

use App\Http\Middleware\HandleInertiaRequests;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        using: function (Application $app, Illuminate\Routing\Router $router) {
            $router->middleware('api')
                ->prefix('api/v1')
                ->group(base_path('routes/api.v1.php'));

            $router->middleware('web')
                ->group(base_path('routes/web.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(append: [
            \App\Http\Middleware\HandleInertiaRequests::class,
            \Illuminate\Http\Middleware\AddLinkHeadersForPreloadedAssets::class,
        ]);

        $middleware->web(append: [
            HandleInertiaRequests::class,
        ]);

        $middleware->alias([
            'ajax.only' => \App\Http\Middleware\AjaxOnly::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
            'api_key' => \App\Http\Middleware\ApiKeyMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (Throwable $e, Request $request) {
            if ($request->is('api/v1/*') || $request->expectsJson()) {
                $data = ['success' => false];
                $status = Response::HTTP_INTERNAL_SERVER_ERROR;

                if ($e instanceof ValidationException) {
                    $status = $e->status;
                    $firstError = array_values($e->errors())[0][0] ?? $e->getMessage();

                    $data['message'] = $firstError;
                    $data['errors'] = $e->errors();
                } elseif ($e instanceof AuthenticationException) {
                    $status = Response::HTTP_UNAUTHORIZED; // 401
                    $data['message'] = $e->getMessage() ?: 'Unauthenticated.';
                } elseif ($e instanceof AuthorizationException) {
                    $status = Response::HTTP_FORBIDDEN; // 403
                    $data['message'] = $e->getMessage() ?: 'This action is unauthorized.';
                } elseif ($e instanceof HttpException) {
                    $status = $e->getStatusCode();
                    $data['message'] = $e->getMessage() ?: Response::$statusTexts[$status] ?? 'Error';
                } else {
                    $data['message'] = config('app.debug') ? $e->getMessage() : 'Server Error';
                }

                Log::info("[App Error]", [
                    'exception' => get_class($e),
                    'code'      => $status,
                    'message'   => $data['message'],
                    'errors'    => $data['errors'] ?? null,
                    'file'      => $e->getFile(),
                    'line'      => $e->getLine(),
                ]);

                if (config('app.debug')) {
                    $data['exception'] = get_class($e);
                    if (!($e instanceof ValidationException)) {
                        $data['trace'] = collect($e->getTrace())->map(function ($trace) {
                            return \Illuminate\Support\Arr::except($trace, ['args']);
                        })->all();
                    }
                }

                return response()->json([
                    'meta' => [
                        'code'      => $status,
                        'status'    => 'error',
                        'message'   => $data['message'] ?? 'An error occurred',
                    ],
                    'errors'    => $data['errors'] ?? null,
                    'exception' => $data['exception'] ?? null,
                    'trace'     => $data['trace'] ?? null,
                ], $status);
            }

            return null;
        });
    })->create();
