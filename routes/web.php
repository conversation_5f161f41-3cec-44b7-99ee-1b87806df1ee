<?php

use App\Http\Controllers\Backoffice\Auth\ProfileController;
use App\Http\Controllers\Backoffice\Consultation\ConsultationController;
use App\Http\Controllers\Backoffice\Customer\ChangeNameController;
use App\Http\Controllers\Backoffice\Customer\CustomerChannelController;
use App\Http\Controllers\Backoffice\Customer\CustomerController;
use App\Http\Controllers\Backoffice\Customer\CustomerRegistrationController;
use App\Http\Controllers\Backoffice\Dashboard\DashboardController;
use App\Http\Controllers\Backoffice\User\RoleController;
use App\Http\Controllers\Backoffice\User\UserController;
use App\Http\Controllers\Backoffice\Informations\NewsController;
use App\Http\Controllers\Backoffice\Informations\TpjEduController;
use App\Http\Controllers\Backoffice\Informations\PromotionsController;
use App\Http\Controllers\Backoffice\Informations\EventsController;
use App\Http\Controllers\Backoffice\Informations\ServiceImprovementController;
use App\Http\Controllers\Backoffice\Informations\SplashController;
use App\Http\Controllers\Backoffice\Master\ConsultationCategoryController;
use Illuminate\Support\Facades\Route;
use Laravel\Fortify\Http\Controllers\AuthenticatedSessionController;

Route::middleware('guest')->group(function () {
    Route::get('/', [AuthenticatedSessionController::class, 'create'])->name('my-login');
});

Route::middleware([
    'auth:web',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    Route::group(['prefix' => 'profile'], function () {
        Route::get('/', [ProfileController::class, 'profile'])->name('user.profile');
        Route::get('/edit', [ProfileController::class, 'editProfile'])->name('profile.edit');
        Route::post('/update', [ProfileController::class, 'updateProfile'])->name('profile.update');
        Route::get('/edit-password', [ProfileController::class, 'editPassword'])->name('profile.password');
        Route::post('/update-password', [ProfileController::class, 'updatePassword'])->name('profile.update-password');
    });

    Route::prefix('users')->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('users.index')->middleware('permission:view users');
        Route::get('/create', [UserController::class, 'create'])->name('users.create')->middleware('permission:create users');
        Route::post('/store', [UserController::class, 'store'])->name('users.store')->middleware('permission:create users');
        Route::get('/edit/{user}', [UserController::class, 'edit'])->name('users.edit')->middleware('permission:edit users');
        Route::put('/update/{user}', [UserController::class, 'update'])->name('users.update')->middleware('permission:edit users');
        Route::delete('/destroy/{user}', [UserController::class, 'destroy'])->name('users.destroy')->middleware('permission:delete users');
        Route::get('/{user}', [UserController::class, 'show'])->name('users.show')->middleware('permission:view users');
    });

    Route::prefix('roles')->group(function () {
        Route::get('/', [RoleController::class, 'index'])->name('roles.index')->middleware('permission:view roles');
        Route::get('/create', [RoleController::class, 'create'])->name('roles.create')->middleware('permission:create roles');
        Route::post('/store', [RoleController::class, 'store'])->name('roles.store')->middleware('permission:create roles');
        Route::get('/{role}', [RoleController::class, 'show'])->name('roles.show')->middleware('permission:view roles');
        Route::get('/edit/{role}', [RoleController::class, 'edit'])->name('roles.edit')->middleware('permission:edit roles');
        Route::put('/update/{role}', [RoleController::class, 'update'])->name('roles.update')->middleware('permission:edit roles');
        Route::delete('/destroy/{role}', [RoleController::class, 'destroy'])->name('roles.destroy')->middleware('permission:delete roles');
    });

    Route::prefix('news')->group(function () {
        Route::get('/', [NewsController::class, 'index'])->name('news.index')->middleware('permission:view news');
        Route::get('/create', [NewsController::class, 'create'])->name('news.create')->middleware('permission:create news');
        Route::post('/store', [NewsController::class, 'store'])->name('news.store')->middleware('permission:create news');
        Route::get('/{news}', [NewsController::class, 'show'])->name('news.show')->middleware('permission:view news');
        Route::get('/edit/{news}', [NewsController::class, 'edit'])->name('news.edit')->middleware('permission:edit news');
        Route::put('/update/{news}', [NewsController::class, 'update'])->name('news.update')->middleware('permission:edit news');
        Route::delete('/destroy/{news}', [NewsController::class, 'destroy'])->name('news.destroy')->middleware('permission:delete news');
    });

    Route::prefix('events')->group(function () {
        Route::get('/', [EventsController::class, 'index'])->name('events.index')->middleware('permission:view events');
        Route::get('/create', [EventsController::class, 'create'])->name('events.create')->middleware('permission:create events');
        Route::post('/store', [EventsController::class, 'store'])->name('events.store')->middleware('permission:create events');
        Route::get('/{event}', [EventsController::class, 'show'])->name('events.show')->middleware('permission:view events');
        Route::get('/edit/{event}', [EventsController::class, 'edit'])->name('events.edit')->middleware('permission:edit events');
        Route::put('/update/{event}', [EventsController::class, 'update'])->name('events.update')->middleware('permission:edit events');
        Route::delete('/destroy/{event}', [EventsController::class, 'destroy'])->name('events.destroy')->middleware('permission:delete events');
    });

    Route::prefix('service-improvements')->group(function () {
        Route::get('/', [ServiceImprovementController::class, 'index'])->name('service-improvements.index')->middleware('permission:view service-improvements');
        Route::get('/create', [ServiceImprovementController::class, 'create'])->name('service-improvements.create')->middleware('permission:create service-improvements');
        Route::get('/get-zones', [ServiceImprovementController::class, 'getZones'])->name('service-improvements.get-zones')->middleware('permission:view service-improvements');
        Route::post('/store', [ServiceImprovementController::class, 'store'])->name('service-improvements.store')->middleware('permission:create service-improvements');
        Route::get('/{service_improvement}', [ServiceImprovementController::class, 'show'])->name('service-improvements.show')->middleware('permission:view service-improvements');
        Route::post('/{service_improvement}', [ServiceImprovementController::class, 'sendNotification'])->name('service-improvements.send-notification')->middleware('permission:view service-improvements');
        Route::get('/edit/{service_improvement}', [ServiceImprovementController::class, 'edit'])->name('service-improvements.edit')->middleware('permission:edit service-improvements');
        Route::put('/update/{service_improvement}', [ServiceImprovementController::class, 'update'])->name('service-improvements.update')->middleware('permission:edit service-improvements');
        Route::delete('/destroy/{service_improvement}', [ServiceImprovementController::class, 'destroy'])->name('service-improvements.destroy')->middleware('permission:delete service-improvements');
    });

    Route::prefix('tpj-edu')->group(function () {
        Route::get('/', [TpjEduController::class, 'index'])->name('tpj-edu.index')->middleware('permission:view tpj-edu');
        Route::get('/create', [TpjEduController::class, 'create'])->name('tpj-edu.create')->middleware('permission:create tpj-edu');
        Route::post('/store', [TpjEduController::class, 'store'])->name('tpj-edu.store')->middleware('permission:create tpj-edu');
        Route::get('/{edu}', [TpjEduController::class, 'show'])->name('tpj-edu.show')->middleware('permission:view tpj-edu');
        Route::get('/edit/{edu}', [TpjEduController::class, 'edit'])->name('tpj-edu.edit')->middleware('permission:edit tpj-edu');
        Route::put('/update/{edu}', [TpjEduController::class, 'update'])->name('tpj-edu.update')->middleware('permission:edit tpj-edu');
        Route::delete('/destroy/{edu}', [TpjEduController::class, 'destroy'])->name('tpj-edu.destroy')->middleware('permission:delete tpj-edu');
    });

    Route::prefix('promotions')->group(function () {
        Route::get('/', [PromotionsController::class, 'index'])->name('promotions.index')->middleware('permission:view promotions');
        Route::get('/create', [PromotionsController::class, 'create'])->name('promotions.create')->middleware('permission:create promotions');
        Route::post('/store', [PromotionsController::class, 'store'])->name('promotions.store')->middleware('permission:create promotions');
        Route::get('/{promotion}', [PromotionsController::class, 'show'])->name('promotions.show')->middleware('permission:view promotions');
        Route::get('/edit/{promotion}', [PromotionsController::class, 'edit'])->name('promotions.edit')->middleware('permission:edit promotions');
        Route::put('/update/{promotion}', [PromotionsController::class, 'update'])->name('promotions.update')->middleware('permission:edit promotions');
        Route::delete('/destroy/{promotion}', [PromotionsController::class, 'destroy'])->name('promotions.destroy')->middleware('permission:delete promotions');
    });

    Route::prefix('events')->group(function () {
        Route::get('/', [EventsController::class, 'index'])->name('events.index')->middleware('permission:view events');
        Route::get('/create', [EventsController::class, 'create'])->name('events.create')->middleware('permission:create events');
        Route::post('/store', [EventsController::class, 'store'])->name('events.store')->middleware('permission:create events');
        Route::get('/{event}', [EventsController::class, 'show'])->name('events.show')->middleware('permission:view events');
        Route::get('/edit/{event}', [EventsController::class, 'edit'])->name('events.edit')->middleware('permission:edit events');
        Route::put('/update/{event}', [EventsController::class, 'update'])->name('events.update')->middleware('permission:edit events');
        Route::delete('/destroy/{event}', [EventsController::class, 'destroy'])->name('events.destroy')->middleware('permission:delete events');
    });

    Route::prefix('splashes')->group(function () {
        Route::get('/', [SplashController::class, 'index'])->name('splashes.index')->middleware('permission:view splashes');
        Route::get('/create', [SplashController::class, 'create'])->name('splashes.create')->middleware('permission:create splashes');
        Route::post('/store', [SplashController::class, 'store'])->name('splashes.store')->middleware('permission:create splashes');
        Route::get('/{splash}', [SplashController::class, 'show'])->name('splashes.show')->middleware('permission:view splashes');
        Route::get('/edit/{splash}', [SplashController::class, 'edit'])->name('splashes.edit')->middleware('permission:edit splashes');
        Route::put('/update/{splash}', [SplashController::class, 'update'])->name('splashes.update')->middleware('permission:edit splashes');
        Route::delete('/destroy/{splash}', [SplashController::class, 'destroy'])->name('splashes.destroy')->middleware('permission:delete splashes');
    });

    Route::prefix('consultation-categories')->group(function () {
        Route::get('/', [ConsultationCategoryController::class, 'index'])->name('consultation-categories.index')->middleware('permission:view consultation-categories');
        Route::get('/get-all', [ConsultationCategoryController::class, 'getConsultationCategories'])->name('consultation-categories.get-all')->middleware('permission:view consultation-categories');
        Route::get('/create', [ConsultationCategoryController::class, 'create'])->name('consultation-categories.create')->middleware('permission:create consultation-categories');
        Route::post('/store', [ConsultationCategoryController::class, 'store'])->name('consultation-categories.store')->middleware('permission:create consultation-categories');
        Route::get('/{consultationCategory}', [ConsultationCategoryController::class, 'show'])->name('consultation-categories.show')->middleware('permission:view consultation-categories');
        Route::get('/edit/{consultationCategory}', [ConsultationCategoryController::class, 'edit'])->name('consultation-categories.edit')->middleware('permission:edit consultation-categories');
        Route::put('/update/{consultationCategory}', [ConsultationCategoryController::class, 'update'])->name('consultation-categories.update')->middleware('permission:edit consultation-categories');
        Route::delete('/destroy/{consultationCategory}', [ConsultationCategoryController::class, 'destroy'])->name('consultation-categories.destroy')->middleware('permission:delete consultation-categories');
    });

    Route::prefix('customer-channels')->group(function () {
        Route::get('/', [CustomerChannelController::class, 'index'])->name('customer-channels.index')->middleware('permission:view customer-channels');
        Route::get('/{customerChannel}', [CustomerChannelController::class, 'show'])->name('customer-channels.show')->middleware('permission:view customer-channels');
    });

    Route::prefix('customers')->group(function () {
        Route::get('/', [CustomerController::class, 'index'])->name('customers.index')->middleware('permission:view customers');
        Route::get('/{customer}', [CustomerController::class, 'show'])->name('customers.show')->middleware('permission:view customers');
        Route::put('/{customer}/update-verification', [CustomerController::class, 'updateVerificationStatus'])->name('customers.update-verification')->middleware('permission:edit customers');
    });

    Route::prefix('consultations')->group(function () {
        Route::get('/', [ConsultationController::class, 'index'])->name('consultations.index')->middleware('permission:view consultations');
        Route::get('/{consultation}', [ConsultationController::class, 'show'])->name('consultations.show')->middleware('permission:view consultations');
        Route::put('/{consultation}/update-status', [ConsultationController::class, 'updateStatus'])->name('consultations.update-status')->middleware('permission:edit consultations');
    });

    Route::prefix('change-names')->group(function () {
        Route::get('/', [ChangeNameController::class, 'index'])->name('change-names.index')->middleware('permission:view change-names');
        Route::get('/{changeName}', [ChangeNameController::class, 'show'])->name('change-names.show')->middleware('permission:view change-names');
        Route::get('/get-tracking/{externalCustomerId}', [ChangeNameController::class, 'getTracking'])->name('change-names.get-tracking')->middleware('permission:view change-names');
        Route::post('/sync/{id}', [ChangeNameController::class, 'sync'])->name('change-names.sync')->middleware('permission:view change-names');
    });

    Route::prefix('customer-registrations')->group(function () {
        Route::get('/', [CustomerRegistrationController::class, 'index'])->name('customer-registrations.index')->middleware('permission:view customer-registrations');
        Route::get('/{activity}', [CustomerRegistrationController::class, 'show'])->name('customer-registrations.show')->middleware('permission:view customer-registrations');
        Route::get('/get-payment/{id}', [CustomerRegistrationController::class, 'getPayment'])->name('customer-registrations.get-payment')->middleware('permission:view customer-registrations');
        Route::get('/get-tracking/{id}', [CustomerRegistrationController::class, 'getTracking'])->name('customer-registrations.get-tracking')->middleware('permission:view customer-registrations');
        Route::post('/sync/{id}', [CustomerRegistrationController::class, 'sync'])->name('customer-registrations.sync')->middleware('permission:view customer-registrations');
    });
});
