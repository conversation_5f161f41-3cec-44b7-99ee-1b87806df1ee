<?php

use App\Http\Controllers\Api\V1\Activity\ActivityController;
use App\Http\Controllers\Api\V1\Auth\LoginController;
use App\Http\Controllers\Api\V1\Consultation\ConsultationCategoryController;
use App\Http\Controllers\Api\V1\Consultation\ConsultationController;
use App\Http\Controllers\Api\V1\CustomerChannel\ChangeNameController;
use App\Http\Controllers\Api\V1\CustomerChannel\CustomerChannelController;
use App\Http\Controllers\Api\V1\CustomerChannel\CustomerRegistrationController;
use App\Http\Controllers\Api\V1\Informations\EventController;
use App\Http\Controllers\Api\V1\Informations\NewsController;
use App\Http\Controllers\Api\V1\Informations\PromotionController;
use App\Http\Controllers\Api\V1\Informations\ServiceImprovementController;
use App\Http\Controllers\Api\V1\Informations\SplashController;
use App\Http\Controllers\Api\V1\Informations\TpjEduController;
use App\Http\Controllers\Api\V1\Location\LocationController;
use App\Http\Controllers\Api\V1\Notification\NotificationController;
use App\Http\Controllers\Api\V1\Profile\CustomerController;
use Illuminate\Support\Facades\Route;

Route::get('/health', function () {
    return response()->json(['status' => 'ok']);
})->name('api.health');

Route::middleware('web')->group(function () {
    Route::get('/login', [LoginController::class, 'loginProvider']);
    Route::get('/login/callback', [LoginController::class, 'callback']);
});

Route::middleware(['api_key'])->group(function () {
    Route::post('/login', [LoginController::class, 'login']);

    Route::middleware(['guest'])->group(function () {
        Route::prefix('/events')->group(function () {
            Route::get('/', [EventController::class, 'index']);
            Route::get('/{id}', [EventController::class, 'show']);
        });

        Route::prefix('/promotions')->group(function () {
            Route::get('/', [PromotionController::class, 'index']);
            Route::get('/current', [PromotionController::class, 'current']);
            Route::get('/{id}', [PromotionController::class, 'show']);
        });

        Route::prefix('/news')->group(function () {
            Route::get('/', [NewsController::class, 'index']);
            Route::get('/{id}', [NewsController::class, 'show']);
        });

        Route::prefix('/edus')->group(function () {
            Route::get('/', [TpjEduController::class, 'index']);
            Route::get('/{id}', [TpjEduController::class, 'show']);
        });

        Route::prefix('/service-improvements')->group(function () {
            Route::get('/', [ServiceImprovementController::class, 'index']);
            Route::get('/{id}', [ServiceImprovementController::class, 'show']);
        });

        Route::get('/splash', [SplashController::class, 'index']);

        Route::prefix('/customer-channels')->group(function () {
            Route::post('/sync/{externalCustomerId}', [CustomerChannelController::class, 'sync']);
            Route::post('/change-name/sync/{externalCustomerId}', [ChangeNameController::class, 'sync']);
        });
    });

    Route::middleware(['auth:api'])->group(function () {
        Route::post('/logout', [LoginController::class, 'logout']);

        Route::prefix('/profile')->group(function () {
            Route::get('/', [CustomerController::class, 'show']);
            Route::get('/point', [CustomerController::class, 'getPoint']);
            Route::post('/', [CustomerController::class, 'update']);
            Route::post('/check-verification-form', [CustomerController::class, 'checkVerificationForm']);
            Route::post('/update-verification', [CustomerController::class, 'updateVerification']);
            Route::post('/re-update-verification', [CustomerController::class, 'reUpdateVerification']);
            Route::post('/update-player-id', [CustomerController::class, 'updatePlayerId']);
        });

        Route::prefix('/notifications')->group(function () {
            Route::get('/', [NotificationController::class, 'index']);
            Route::put('/mark-all-as-read', [NotificationController::class, 'updateAll']);
            Route::put('/{notification}', [NotificationController::class, 'update']);
        });

        Route::get('/provinces', [LocationController::class, 'provinces']);
        Route::get('/regencies/{province}', [LocationController::class, 'regencies']);
        Route::get('/districts/{regency}', [LocationController::class, 'districts']);
        Route::get('/villages/{district}', [LocationController::class, 'villages']);
        Route::prefix('/locations')->group(function () {
            Route::get('/simbio-sub-districts', [LocationController::class, 'simbioSubDistricts']);
            Route::get('/simbio-villages/{sub_district}', [LocationController::class, 'simbioVillages']);
        });

        Route::prefix('/customer-channels')->group(function () {
            Route::get('/', [CustomerChannelController::class, 'index']);
            Route::post('/', [CustomerChannelController::class, 'store']);
            Route::delete('/{channel}', [CustomerChannelController::class, 'destroy']);

            Route::prefix('/registration')->group(function () {
                Route::post('/', [CustomerRegistrationController::class, 'store']);
                Route::get('/{id}', [CustomerRegistrationController::class, 'show']);
                Route::post('/payment', [CustomerRegistrationController::class, 'storePayment']);
                Route::get('/payment/{id}', [CustomerRegistrationController::class, 'getPayment']);
                Route::get('/tracking/{id}', [CustomerRegistrationController::class, 'getTracking']);
            });

            Route::prefix('/change-name')->group(function () {
                Route::get('/categories', [ChangeNameController::class, 'getCategories']);
                Route::get('/{id}', [ChangeNameController::class, 'show']);
                Route::post('/', [ChangeNameController::class, 'store']);
                Route::post('/payment', [ChangeNameController::class, 'storePayment']);
                Route::get('/payment/{id}', [ChangeNameController::class, 'getPayment']);
                Route::get('/payment-status/{id}', [ChangeNameController::class, 'getPaymentStatus']);
                Route::get('/tracking/{id}', [ChangeNameController::class, 'getTracking']);
            });
        });

        Route::prefix('/consultations')->group(function () {
            Route::get('/categories', [ConsultationCategoryController::class, 'index']);
            Route::get('/', [ConsultationController::class, 'index']);
            Route::post('/', [ConsultationController::class, 'store']);
            Route::get('/{id}', [ConsultationController::class, 'show']);
            Route::put('/{id}/cancel', [ConsultationController::class, 'cancel']);
        });

        Route::prefix('/activities')->group(function () {
            Route::get('/', [ActivityController::class, 'index']);
            Route::get('/status-list', [ActivityController::class, 'getStatusList']);
        });
    });
});
