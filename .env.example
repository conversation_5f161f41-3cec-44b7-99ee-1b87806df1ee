APP_NAME="TPJ Simpel"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=Asia/Jakarta
APP_URL=https://simpel-tpj.dafidea.xyz

APP_LOCALE=id
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
LOG_VIEWER_ENABLED=true

#DB_CONNECTION=sqlite
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=simpel_dev
DB_USERNAME=simpel_dev
DB_PASSWORD=dafideaa

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=pusher
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_SCHEME=null
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=42c7dfd895cbf7
MAIL_PASSWORD=0fb327e0028e36
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# PRODUCTION
# MAIL_MAILER=smtp
# MAIL_HOST=smtp-relay.brevo.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=gyKcnbCdVaTDs5zR
# MAIL_ENCRYPTION=tls

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

GOOGLE_CLIENT_ID = 133973647769-bu8ugi4quv8j7enomtb983t5sr8ua9gk.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET = 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
GOOGLE_REDIRECT = https://simpel-tpj.dafidea.xyz/api/v1/login/callback

# SIMBIO_URL=http://simbio.tirtapandalungan.id/api
SIMBIO_URL=http://*************:9669/api
SIMBIO_USERNAME="<EMAIL>"
SIMBIO_PASSWORD="RY{4K#PFxxVyDvLbq13w"

SIMPEG_URL=http://simpeg.tirtapandalungan.id/api
SIMPEG_USERNAME="<EMAIL>"
SIMPEG_PASSWORD="RY{4K#PFxxVyDvLbq13w"

API_KEYS="tpj-1a2b3c4dTpJ5e6fInsiDe7gtpJ"

ONE_SIGNAL_APP_ID=5fcf27a0-f3bc-4904-ab98-ccfaf87f58a8
ONE_SIGNAL_CHANNEL_ID=53724743-914a-4930-8019-b5f665f65452

ONE_SIGNAL_AUTHORIZE=os_v2_app_l7hspihtxreqjk4yzt5pq72yvdwskn2fayke3e4ia4zfp2oo2pkfxsupizzlg3filfkxsbdd6wlrt2v6dss3mcajdfzigbulkv3f7ua
ONE_SIGNAL_AUTH_KEY=wskn2fayke3e4ia4zfp2oo2pk

# PRODUCTION
# ONE_SIGNAL_AUTHORIZE=os_v2_app_l7hspihtxreqjk4yzt5pq72yvcu2ub25yi2ebmnus4665pfim6jql3wvhwoy26jqkrgd2v5ipp4gjcxx5ss2fyiiwz2nggyp2vml65a
# ONE_SIGNAL_AUTH_KEY=u2ub25yi2ebmnus4665pfim6j

# START PUSHER

# LOCAL DEV
# PUSHER_APP_ID=*******
# PUSHER_APP_KEY=09be9eeb948fd5f7b856
# PUSHER_APP_SECRET=1e028de430fda041fdd5
# PUSHER_APP_CLUSTER=ap1

# STAGING
PUSHER_APP_ID=*******
PUSHER_APP_KEY=4b01e8a5e31509acc52a
PUSHER_APP_SECRET=44ea3fe1f5586a8b342d
PUSHER_APP_CLUSTER=ap1

# PRODUCTION
# PUSHER_APP_ID=*******
# PUSHER_APP_KEY=ce30efeb5249612733c7
# PUSHER_APP_SECRET=2fb3213f3516347e9129
# PUSHER_APP_CLUSTER=ap1

PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME="https"
 
VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
