{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@iconify-json/hugeicons": "^1.2.0", "@iconify/tailwind": "^1.1.3", "@inertiajs/vue3": "^1.0.14", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.16", "axios": "^1.6.4", "laravel-echo": "^2.2.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.32", "pusher-js": "^8.4.0", "tailwindcss": "^3.4.0", "vite": "^5.0", "vue": "^3.3.13"}, "dependencies": {"@inertiajs/vue3": "^1.2.0", "@tinymce/tinymce-vue": "^5.1.1", "@vueform/multiselect": "^2.6.9", "@vuepic/vue-datepicker": "^5.3.0", "flowbite": "^2.4.1", "lodash": "^4.17.21", "moment": "^2.30.1", "remixicon": "^4.3.0", "vue-draggable-next": "^2.2.1", "vuedraggable": "^4.1.0"}}