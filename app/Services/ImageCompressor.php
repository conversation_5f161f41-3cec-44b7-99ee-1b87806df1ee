<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Encoders\JpegEncoder;
use Intervention\Image\Laravel\Facades\Image;
use InvalidArgumentException;

class ImageCompressor
{
    private const SUPPORTED_MIME_TYPES = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp'
    ];

    private const DEFAULT_QUALITY = 85;

    public function compressAndStore(
        UploadedFile $file,
        string $path = 'uploads/images',
        int $maxWidth = 2560,
        int $quality = self::DEFAULT_QUALITY
    ): array {
        $this->validateFile($file);

        $filename = $file->hashName();
        $originalSize = $file->getSize();
        $originalSizeKB = round($originalSize / 1024, 2);

        $image = Image::read($file->getRealPath());

        $resizedImage = $this->resizeWithAspectRatio($image, $maxWidth);
        $compressed = $this->encodeImage($resizedImage, $quality);

        $storagePath = "{$path}/{$filename}";
        Storage::disk('public')->put($storagePath, $compressed);

        $compressedSize = strlen($compressed);
        $compressedSizeKB = round($compressedSize / 1024, 2);
        $compressionRatio = $originalSize > 0
            ? round((($originalSize - $compressedSize) / $originalSize) * 100, 2)
            : 0;

        $this->logResult($path, $filename, $originalSizeKB, $compressedSizeKB, $compressionRatio, $quality, $resizedImage);

        return [
            'path' => $storagePath,
            'filename' => $filename,
            'url' => Storage::url($storagePath),
            'original_size_kb' => $originalSizeKB,
            'compressed_size_kb' => $compressedSizeKB,
            'compression_ratio' => $compressionRatio,
            'quality' => $quality,
            'dimensions' => [
                'width' => $resizedImage->width(),
                'height' => $resizedImage->height()
            ]
        ];
    }

    private function validateFile(UploadedFile $file): void
    {
        if (!$file->isValid()) {
            throw new InvalidArgumentException('Invalid file upload');
        }

        if (!in_array($file->getMimeType(), self::SUPPORTED_MIME_TYPES)) {
            throw new InvalidArgumentException('Unsupported image format');
        }
    }

    private function resizeWithAspectRatio($image, int $maxWidth)
    {
        if ($image->width() <= $maxWidth) {
            return $image;
        }

        $aspectRatio = $image->width() / $image->height();
        $newHeight = (int) ($maxWidth / $aspectRatio);

        return $image->resize($maxWidth, $newHeight);
    }

    private function encodeImage($image, int $quality): string
    {
        return $image->encode(new JpegEncoder($quality))->toString();
    }

    private function logResult(
        string $path,
        string $filename,
        float $originalSizeKB,
        float $compressedSizeKB,
        float $compressionRatio,
        int $quality,
        $image
    ): void {
        Log::info('[ImageCompressor] Image processed', [
            'path' => $path,
            'filename' => $filename,
            'original_size_kb' => $originalSizeKB,
            'compressed_size_kb' => $compressedSizeKB,
            'compression_ratio' => $compressionRatio,
            'quality' => $quality,
            'dimensions' => [
                'width' => $image->width(),
                'height' => $image->height()
            ]
        ]);
    }
}
