<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class FileUploadSizeRule implements ValidationRule
{
    private $rule;
    private $maxSize;
    private $minSize;
    private $maxSizeInKb;
    private $minSizeInKb;
    private $translatedAttribute;

    public function __construct(string $rule, int $maxSize = 1012000, int $minSize = 100000, $translatedAttribute = null)
    {
        $this->rule         = $rule;
        $this->maxSize      = $maxSize;
        $this->minSize      = $minSize;
        $this->maxSizeInKb  = $maxSize / 1000;
        $this->minSizeInKb  = $minSize / 1000;
        $this->translatedAttribute = $translatedAttribute;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $imageValidator = Validator::make([$attribute => $value], [
            $attribute => $this->rule,
        ]);

        if ($imageValidator->passes()) {
            $file = $this->getFileFromValue($value, $attribute);
            $attributeName = $this->translatedAttribute ?? $attribute;

            if ($file !== null) {
                $size = $file->getSize();

                if ($size > $this->maxSize) {
                    $fail("$attributeName tidak boleh lebih besar dari $this->maxSizeInKb kilobytes.");
                }

                if ($size < $this->minSize) {
                    $fail("$attributeName tidak boleh lebih kecil dari $this->minSizeInKb kilobytes.");
                }
            }
        }
    }

    private function getFileFromValue(mixed $value, string $attribute): ?UploadedFile
    {
        if (is_array($value)) {
            $keys = explode('.', $attribute);
            $file = Arr::get($value, end($keys));
        } else {
            $file = $value;
        }

        return $file instanceof UploadedFile ? $file : null;
    }
}
