<?php

namespace App\Notifications\Customer\Customer;

use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Enum\NotificationType;
use App\Models\Customer\Enum\ChangeNameStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomerChannelChangeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private $customerChannelChange;
    private $subject;
    private $message;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(CustomerChannelChange $customerChannelChange)
    {
        $this->customerChannelChange = $customerChannelChange;
        $this->subject = 'Layanan Balik Nama';
        $this->message = ChangeNameStatus::getNotificationMessage($customerChannelChange->current_status);
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line($this->subject)
            ->line($this->message);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'type'      => NotificationType::CHANGE_NAME->value,
            'title'     => $this->subject,
            'message'   => $this->message,
            'data'      => [
                'id' => $this->customerChannelChange->id,
                'payment_status' => $this->customerChannelChange->payment_status,
                'current_status' => $this->customerChannelChange->current_status,
            ]
        ];
    }
}
