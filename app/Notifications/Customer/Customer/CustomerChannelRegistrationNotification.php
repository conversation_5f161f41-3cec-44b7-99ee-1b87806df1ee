<?php

namespace App\Notifications\Customer\Customer;

use App\Models\Customer\CustomerChannel;
use App\Models\Customer\Enum\NotificationType;
use App\Models\Customer\Enum\RegistrationStatus;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CustomerChannelRegistrationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private $customerChannel;
    private $subject;
    private $message;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(CustomerChannel $customerChannel)
    {
        $this->customerChannel = $customerChannel;
        $this->subject = 'Layanan <PERSON> Baru';
        $this->message = RegistrationStatus::getNotificationMessage($customerChannel->status_registration);
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line($this->subject)
            ->line($this->message);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'type'      => NotificationType::REGISTRATION->value,
            'title'     => $this->subject,
            'message'   => $this->message,
            'data'      => [
                'id' => $this->customerChannel->id,
                'status' => $this->customerChannel->status,
                'status_registration' => $this->customerChannel->status_registration,
            ]
        ];
    }
}
