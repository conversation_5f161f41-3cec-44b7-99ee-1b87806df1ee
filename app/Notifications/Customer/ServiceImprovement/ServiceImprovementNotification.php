<?php

namespace App\Notifications\Customer\ServiceImprovement;

use App\Models\Customer\Enum\NotificationType;
use App\Models\Informations\ServiceImprovement;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class ServiceImprovementNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private $serviceImprovement;
    private $subject;
    private $message;
    private $date;

    public function __construct(ServiceImprovement $serviceImprovement)
    {
        $this->date = Carbon::parse($serviceImprovement->date)->locale('id')->isoFormat('D MMMM Y');
        $this->serviceImprovement = $serviceImprovement;
        $this->subject = 'Penyempurnaan Layanan';
        $this->message = 'Kami melakukan penyempurnaan layanan di wilayah kamu pada tanggal ' . $this->date . '. Klik untuk melihat detail informasinya.';
    }

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toMail($notifiable)
    {
        Log::info('[ServiceImprovementNotification] Sending email notification', [
            'service_improvement_id' => $this->serviceImprovement->id,
            'customer_id' => $notifiable->id,
        ]);

        return (new MailMessage)
            ->subject($this->subject)
            ->view('emails.service-improvement.notification', [
                'serviceImprovement' => $this->serviceImprovement,
                'subject' => $this->subject,
                'msg' => $this->message,
            ]);
    }

    public function toArray($notifiable)
    {
        return [
            'type'      => NotificationType::SERVICE_IMPROVEMENT->value,
            'title'     => $this->subject,
            'message'   => $this->message,
            'data'      => [
                'id' => $this->serviceImprovement->id,
                'zone_id' => $this->serviceImprovement->zone_id,
                'zone_name' => $this->serviceImprovement->zone_name,
                'date' => $this->date,
                'image_url' => $this->serviceImprovement->image,
            ]
        ];
    }
}
