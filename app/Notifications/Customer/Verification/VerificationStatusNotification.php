<?php

namespace App\Notifications\Customer\Verification;

use App\Models\Customer\Customer;
use App\Models\Customer\Enum\NotificationType;
use App\Models\Customer\Enum\VerificationStatus;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class VerificationStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private $customer;
    private $subject;
    private $message;
    private $verificationStatus;
    private $verifiedAt;

    public function __construct(Customer $customer)
    {
        $this->customer = $customer;
        $this->verificationStatus = $customer->verification_status;
        $this->verifiedAt = Carbon::parse($customer->verified_at)->format('d M Y H:i');

        $this->subject = match ($this->verificationStatus) {
            VerificationStatus::VERIFIED->value => 'Akun Terverifikasi',
            VerificationStatus::REJECTED->value => 'Verifikasi Ditolak',
            VerificationStatus::WAITING->value => 'Verifikasi Sedang Diproses',
            default => 'Status Verifikasi Diperbarui',
        };

        $this->message = match ($this->verificationStatus) {
            VerificationStatus::VERIFIED->value => 'Selamat! Akun Anda telah berhasil diverifikasi. Anda mendapatkan poin sebagai bonus verifikasi.',
            VerificationStatus::REJECTED->value => 'Maaf, verifikasi akun Anda ditolak. Silakan periksa kembali data yang Anda kirimkan dan ajukan ulang verifikasi.',
            VerificationStatus::WAITING->value => 'Verifikasi akun Anda sedang dalam proses peninjauan. Mohon tunggu konfirmasi dari tim kami.',
            default => 'Status verifikasi akun Anda telah diperbarui menjadi ' . VerificationStatus::getDescription($this->verificationStatus),
        };
    }

    public function via($notifiable)
    {
        return ['database'];
    }

    public function toMail($notifiable)
    {
        Log::info('[VerificationStatusNotification] Sending email notification', [
            'customer_id' => $this->customer->id,
            'verification_status' => $this->verificationStatus,
        ]);

        return (new MailMessage)
            ->subject($this->subject)
            ->view('emails.verification.notification', [
                'customer' => $this->customer,
                'subject' => $this->subject,
                'msg' => $this->message,
                'verificationStatus' => $this->verificationStatus,
            ]);
    }

    public function toArray($notifiable)
    {
        return [
            'type'      => NotificationType::ACCOUNT_VERIFICATION->value,
            'title'     => $this->subject,
            'message'   => $this->message,
            'data'      => [
                'customer_id' => $this->customer->id,
                'verification_status' => $this->verificationStatus,
                'verified_at' => $this->verifiedAt,
            ]
        ];
    }
}
