<?php

namespace App\Notifications\Customer\Consultation;

use App\Models\Consultation\Consultation;
use App\Models\Consultation\Enum\ConsultationStatus;
use App\Models\Customer\Enum\NotificationType;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class ConsultationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private $consultation;
    private $subject;
    private $message;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Consultation $consultation)
    {
        $this->consultation = $consultation;
        $this->subject = 'Layanan Konsultasi';
        $this->message = $consultation->status === ConsultationStatus::WAITING->value
            ? 'Konsultasi berhasil diajukan'
            : 'Status konsultasi Anda telah diperbarui menjadi ' . $consultation->status_description;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        Log::info('[ConsultationNotification] Sending email notification', [
            'consultation_id' => $this->consultation->id,
            'customer_id' => $notifiable->id,
        ]);
        return (new MailMessage)
            ->subject($this->subject)
            ->view('emails.consultation.notification', [
                'consultation' => $this->consultation,
                'subject' => $this->subject,
                'msg' => $this->message,
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'type'      => NotificationType::CONSULTATION->value,
            'title'     => $this->subject,
            'message'   => $this->message,
            'data'      => [
                'id' => $this->consultation->id,
                'status' => $this->consultation->status,
            ]
        ];
    }
}
