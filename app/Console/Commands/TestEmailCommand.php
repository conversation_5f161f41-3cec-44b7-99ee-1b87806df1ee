<?php

namespace App\Console\Commands;

use App\Mail\TestMail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            Mail::to('<EMAIL>')
                ->cc('<EMAIL>')
                ->queue(new TestMail("TPJ Simpel - Operation completed successfully!"));

            Log::info('[TestEmailCommand] Email sent successfully');
        } catch (\Exception $e) {
            Log::error('[TestEmailCommand] Error sending email', [
                'error' => $e->getMessage(),
            ]);
        }
    }
}
