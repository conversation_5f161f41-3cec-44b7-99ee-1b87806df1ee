<?php

namespace App\Actions\Notification;

use App\Models\Customer\CustomerChannel;
use App\Models\Customer\Enum\NotificationType;
use App\Models\Customer\Enum\RegistrationStatus;
use App\Notifications\Customer\Customer\CustomerChannelRegistrationNotification;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Ladumor\OneSignal\OneSignal;
use Lorisleiva\Actions\Concerns\AsAction;

class CustomerChannelRegistrationNotificationAction
{
    use AsAction;

    public function handle(CustomerChannel $customerChannel)
    {
        $headings = [
            'en' => 'Layanan Pasang Baru',
            'id' => 'Layanan Pasang Baru'
        ];
        $contents = [
            'en' => RegistrationStatus::getNotificationMessage($customerChannel->status_registration),
            'id' => RegistrationStatus::getNotificationMessage($customerChannel->status_registration)
        ];

        $message = RegistrationStatus::getNotificationMessage($customerChannel->status_registration);

        try {
            $customers = $customerChannel->customers;

            $allPlayerIds = [];
            foreach ($customers as $customer) {
                if ($customer->player_ids && is_array($customer->player_ids)) {
                    $allPlayerIds = array_merge($allPlayerIds, $customer->player_ids);
                }
            }

            $validPlayerIds = collect($allPlayerIds)->filter(function ($id) {
                $validator = Validator::make(
                    ['id' => $id],
                    ['id' => 'uuid']
                );
                return !$validator->fails();
            })->values()->toArray();

            if (!empty($validPlayerIds)) {
                OneSignal::sendPush(
                    [
                        'target_channel'        => 'push',
                        'android_channel_id'    => config('one-signal.channel_id'),
                        'include_player_ids'    => $validPlayerIds,
                        'headings'              => $headings,
                        'contents'              => $contents,
                        'data'                  => [
                            'type'      => NotificationType::REGISTRATION->value,
                            'id'        => $customerChannel->id,
                            'status'    => $customerChannel->status,
                            'registration_status' => $customerChannel->registration_status
                        ]
                    ],
                    $message
                );

                Log::info('[CustomerChannelRegistrationNotificationAction] Push notification sent', [
                    'customer_channel_id' => $customerChannel->id,
                    'customer_ids' => $customers->pluck('id')->toArray(),
                ]);
            }

            foreach ($customers as $customer) {
                Notification::send($customer, new CustomerChannelRegistrationNotification($customerChannel));
            }

            Log::info('[CustomerChannelRegistrationNotificationAction] Notification sent', [
                'customer_channel_id' => $customerChannel->id,
                'customer_ids' => $customers->pluck('id')->toArray(),
            ]);
        } catch (Exception $e) {
            Log::error("[CustomerChannelRegistrationNotificationAction] Error send notif", [
                'class'     => 'CustomerChannelRegistrationNotificationAction',
                'message'   => $e->getMessage(),
                'trace'     => $e->getTraceAsString(),
            ]);
        }
    }
}
