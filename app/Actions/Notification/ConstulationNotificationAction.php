<?php

namespace App\Actions\Notification;

use App\Models\Consultation\Consultation;
use App\Models\Consultation\Enum\ConsultationStatus;
use App\Models\Customer\Enum\NotificationType;
use App\Notifications\Customer\Consultation\ConsultationNotification;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Ladumor\OneSignal\OneSignal;
use Lorisleiva\Actions\Concerns\AsAction;

class ConstulationNotificationAction
{
    use AsAction;

    public function handle(Consultation $consultation)
    {
        try {
            $message = 'Status Konsultasi ' . $consultation->status_description;
            if ($consultation->status === ConsultationStatus::WAITING->value) {
                $message = 'Konsultasi berhasil diajukan';
            }

            $headings = [
                'en' => 'Layanan Konsultasi',
                'id' => 'Layanan Konsultasi'
            ];

            $contents = [
                'en' => $message,
                'id' => $message
            ];

            if ($consultation->status === ConsultationStatus::WAITING->value) {
                $contents = [
                    'en' => 'Konsultasi ' . $consultation->code . ' berhasil diajukan',
                    'id' => 'Konsultasi ' . $consultation->code . ' berhasil diajukan'
                ];
            } elseif ($consultation->status === ConsultationStatus::PROGRESS->value) {
                $contents = [
                    'en' => 'Konsultasi ' . $consultation->code . ' sedang diproses',
                    'id' => 'Konsultasi ' . $consultation->code . ' sedang diproses'
                ];
            } elseif ($consultation->status === ConsultationStatus::DONE->value) {
                $contents = [
                    'en' => 'Konsultasi ' . $consultation->code . ' telah selesai',
                    'id' => 'Konsultasi ' . $consultation->code . ' telah selesai'
                ];
            } elseif ($consultation->status === ConsultationStatus::CANCELED_BY_ADMIN->value) {
                $contents = [
                    'en' => 'Konsultasi ' . $consultation->code . ' dibatalkan oleh admin',
                    'id' => 'Konsultasi ' . $consultation->code . ' dibatalkan oleh admin'
                ];
            }

            $customer = $consultation->customer;
            $customerPlayerIds = $customer->player_ids ?? [];
            $validPlayerIds = collect($customerPlayerIds)->filter(function ($id) {
                $validator = Validator::make(
                    ['id' => $id],
                    ['id' => 'uuid']
                );
                return !$validator->fails();
            })->values()->toArray();

            if (!empty($validPlayerIds)) {
                OneSignal::sendPush(
                    [
                        'target_channel'        => 'push',
                        'android_channel_id'    => config('one-signal.channel_id'),
                        'include_player_ids'    => $validPlayerIds,
                        'headings'              => $headings,
                        'contents'              => $contents,
                        'data'                  => [
                            'type'      => NotificationType::CONSULTATION->value,
                            'id'        => $consultation->id,
                            'status'    => $consultation->status
                        ]
                    ],
                    $message
                );

                Log::info('[ConstulationNotificationAction] Push notification sent', [
                    'consultation_id' => $consultation->id,
                    'customer_id' => $customer->id,
                ]);
            }

            Notification::send($customer, new ConsultationNotification($consultation));

            Log::info('[ConstulationNotificationAction] Notification sent', [
                'consultation_id' => $consultation->id,
                'consultation_code' => $consultation->code,
                'customer_id' => $customer->id,
            ]);
        } catch (Exception $e) {
            Log::error("[ConstulationNotificationAction] Error send notif", [
                'class'     => 'ConstulationNotificationAction',
                'message'   => $e->getMessage(),
                'trace'     => $e->getTraceAsString(),
            ]);
        }
    }
}
