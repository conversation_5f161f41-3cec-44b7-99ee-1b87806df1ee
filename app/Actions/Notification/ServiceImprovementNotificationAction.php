<?php

namespace App\Actions\Notification;

use App\Mail\ServiceImprovementBulkMail;
use App\Models\Customer\Customer;
use App\Models\Customer\Enum\NotificationType;
use App\Models\Informations\ServiceImprovement;
use App\Notifications\Customer\ServiceImprovement\ServiceImprovementNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Ladumor\OneSignal\OneSignal;
use Lorisleiva\Actions\Concerns\AsAction;

class ServiceImprovementNotificationAction
{
    use AsAction;

    public function handle(ServiceImprovement $serviceImprovement)
    {
        Log::info('[ServiceImprovementNotificationAction] started', [
            'service_improvement_id' => $serviceImprovement->id,
        ]);

        if (!$serviceImprovement->is_active || Carbon::parse($serviceImprovement->date . ' 23:59:59')->isPast()) {
            Log::info('[ServiceImprovementNotificationAction] Service improvement is not active or in the past', [
                'service_improvement_id' => $serviceImprovement->id,
            ]);
            return;
        }

        try {
            $date = Carbon::parse($serviceImprovement->date)->locale('id')->isoFormat('D MMMM Y');
            $message = 'Kami melakukan penyempurnaan layanan di wilayah kamu pada tanggal ' . $date . '. Klik untuk melihat detail informasinya.';

            $headings = [
                'en' => 'Penyempurnaan Layanan',
                'id' => 'Penyempurnaan Layanan'
            ];

            $contents = [
                'en' => $message,
                'id' => $message
            ];

            $customers = Customer::select('id', 'email', 'player_ids')
                ->whereNotNull('player_ids')
                ->whereHas('channels', function ($query) use ($serviceImprovement) {
                    $query->where('id_service_zone', $serviceImprovement->zone_id);
                })->get();

            if ($customers->isEmpty()) {
                Log::info('[ServiceImprovementNotificationAction] No customers found for zone', [
                    'service_improvement_id' => $serviceImprovement->id,
                    'zone_id' => $serviceImprovement->zone_id,
                ]);
                return;
            }

            $customerEmails = $customers->whereNotNull('email')->pluck('email')->filter()->unique()->toArray();

            // if (!empty($customerEmails)) {
            //     Mail::to($customerEmails[0])
            //         ->bcc($customerEmails)
            //         ->send(new ServiceImprovementBulkMail($serviceImprovement));

            //     Log::info('[ServiceImprovementNotificationAction] Bulk email sent', [
            //         'service_improvement_id' => $serviceImprovement->id,
            //         'email_count' => count($customerEmails),
            //     ]);
            // }

            $allPlayerIds = [];
            foreach ($customers as $customer) {
                if ($customer->player_ids && is_array($customer->player_ids)) {
                    $allPlayerIds = array_merge($allPlayerIds, $customer->player_ids);
                }
            }

            $allPlayerIds = array_filter(array_unique($allPlayerIds));

            $validPlayerIds = collect($allPlayerIds)->filter(function ($id) {
                $validator = Validator::make(
                    ['id' => $id],
                    ['id' => 'uuid']
                );
                return !$validator->fails();
            })->values()->toArray();

            Log::info('[ServiceImprovementNotificationAction] All player ids', [
                'service_improvement_id' => $serviceImprovement->id,
                'all_player_ids' => $validPlayerIds,
            ]);

            if (!empty($validPlayerIds)) {
                $imageUrl = $serviceImprovement->image ?? asset('images/logo.png');

                $sendPush = OneSignal::sendPush(
                    [
                        'target_channel'        => 'push',
                        'android_channel_id'    => config('one-signal.channel_id'),
                        'include_player_ids'    => $validPlayerIds,
                        'headings'              => $headings,
                        'contents'              => $contents,
                        'big_picture'           => $imageUrl,
                        'large_icon'            => $imageUrl,
                        'chrome_web_image'      => $imageUrl,
                        'mutable_content'       => true,
                        'ios_attachments'       => [
                            'image' => $imageUrl
                        ],
                        'data'                  => [
                            'type'      => NotificationType::SERVICE_IMPROVEMENT->value,
                            'id'        => $serviceImprovement->id,
                            'zone_id'   => $serviceImprovement->zone_id,
                            'zone_name' => $serviceImprovement->zone_name,
                            'date'      => $date,
                            'image_url' => $imageUrl
                        ]
                    ],
                    $message
                );

                Log::info('[ServiceImprovementNotificationAction] Bulk push notification sent', [
                    'service_improvement_id' => $serviceImprovement->id,
                    'player_ids_count' => count($validPlayerIds),
                    'onesignal_response' => $sendPush,
                    'imageUrl' => $imageUrl,
                ]);
            }

            $databaseNotificationCount = 0;
            foreach ($customers as $customer) {
                $customer->notify(new ServiceImprovementNotification($serviceImprovement));
                $databaseNotificationCount++;
            }

            Log::info('[ServiceImprovementNotificationAction] Notifications sent', [
                'service_improvement_id' => $serviceImprovement->id,
                'bulk_push_notification_sent' => !empty($validPlayerIds),
                'total_player_ids' => count($validPlayerIds),
                'invalid_player_ids' => count($allPlayerIds) - count($validPlayerIds),
                'database_notifications_sent' => $databaseNotificationCount,
            ]);

            Log::info('[ServiceImprovementNotificationAction] completed', [
                'service_improvement_id' => $serviceImprovement->id,
                'customers_notified' => $customers->count(),
            ]);
        } catch (Exception $e) {
            Log::error("[ServiceImprovementNotificationAction] Error send notif", [
                'class'     => 'ServiceImprovementNotificationAction',
                'message'   => $e->getMessage(),
                'trace'     => $e->getTraceAsString(),
            ]);
        }
    }
}
