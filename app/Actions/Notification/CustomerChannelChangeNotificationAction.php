<?php

namespace App\Actions\Notification;

use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Enum\ChangeNameStatus;
use App\Models\Customer\Enum\NotificationType;
use App\Notifications\Customer\Customer\CustomerChannelChangeNotification;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Ladumor\OneSignal\OneSignal;
use Lorisleiva\Actions\Concerns\AsAction;

class CustomerChannelChangeNotificationAction
{
    use AsAction;

    public function handle(CustomerChannelChange $customerChannelChange)
    {
        $headings = [
            'en' => 'Layanan Balik Nama',
            'id' => 'Layanan Balik Nama'
        ];
        $contents = [
            'en' => ChangeNameStatus::getNotificationMessage($customerChannelChange->current_status),
            'id' => ChangeNameStatus::getNotificationMessage($customerChannelChange->current_status)
        ];

        $message = ChangeNameStatus::getNotificationMessage($customerChannelChange->current_status);

        try {
            $customer = $customerChannelChange->customer;

            $allPlayerIds = [];
            if ($customer->player_ids && is_array($customer->player_ids)) {
                $allPlayerIds = array_merge($allPlayerIds, $customer->player_ids);
            }

            $validPlayerIds = collect($allPlayerIds)->filter(function ($id) {
                $validator = Validator::make(
                    ['id' => $id],
                    ['id' => 'uuid']
                );
                return !$validator->fails();
            })->values()->toArray();

            if (!empty($validPlayerIds)) {
                OneSignal::sendPush(
                    [
                        'target_channel'        => 'push',
                        'android_channel_id'    => config('one-signal.channel_id'),
                        'include_player_ids'    => $validPlayerIds,
                        'headings'              => $headings,
                        'contents'              => $contents,
                        'data'                  => [
                            'type'      => NotificationType::CHANGE_NAME->value,
                            'id'        => $customerChannelChange->id,
                            'payment_status'    => $customerChannelChange->payment_status,
                            'current_status'    => $customerChannelChange->current_status
                        ]
                    ],
                    $message
                );

                Log::info('[CustomerChannelChangeNotificationAction] Push notification sent', [
                    'customer_channel_change_id' => $customerChannelChange->id,
                    'customer_id' => $customer->id
                ]);
            }

            Notification::send($customer, new CustomerChannelChangeNotification($customerChannelChange));

            Log::info('[CustomerChannelChangeNotificationAction] Notification sent', [
                'customer_channel_change_id' => $customerChannelChange->id,
                'customer_id' => $customer->id
            ]);
        } catch (Exception $e) {
            Log::error("[CustomerChannelChangeNotificationAction] Error send notif", [
                'class'     => 'CustomerChannelChangeNotificationAction',
                'message'   => $e->getMessage(),
                'trace'     => $e->getTraceAsString(),
            ]);
        }
    }
}
