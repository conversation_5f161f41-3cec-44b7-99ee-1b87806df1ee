<?php

namespace App\Actions\Notification;

use App\Models\Customer\Customer;
use App\Models\Customer\Enum\NotificationType;
use App\Models\Customer\Enum\VerificationStatus;
use App\Notifications\Customer\Verification\VerificationStatusNotification;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Ladumor\OneSignal\OneSignal;
use Lorisleiva\Actions\Concerns\AsAction;

class VerificationStatusNotificationAction
{
    use AsAction;

    public function handle(Customer $customer)
    {
        Log::info('[VerificationStatusNotificationAction] started', [
            'customer_id' => $customer->id,
            'verification_status' => $customer->verification_status,
        ]);

        try {
            $statusDescription = VerificationStatus::getDescription($customer->verification_status);

            $message = match ($customer->verification_status) {
                VerificationStatus::VERIFIED->value => 'Selamat! Akun Anda telah berhasil diverifikasi dan mendapatkan poin bonus.',
                VerificationStatus::REJECTED->value => 'Verifikasi akun Anda ditolak. Silakan ajukan ulang dengan data yang benar.',
                VerificationStatus::WAITING->value => 'Verifikasi akun Anda sedang dalam proses peninjauan.',
                default => 'Status verifikasi akun Anda telah diperbarui menjadi ' . $statusDescription,
            };

            $headings = [
                'en' => 'Verifikasi Akun',
                'id' => 'Verifikasi Akun'
            ];

            $contents = [
                'en' => $message,
                'id' => $message
            ];

            $customerPlayerIds = $customer->player_ids ?? [];
            $validPlayerIds = collect($customerPlayerIds)->filter(function ($id) {
                $validator = Validator::make(
                    ['id' => $id],
                    ['id' => 'uuid']
                );
                return !$validator->fails();
            })->values()->toArray();

            if (!empty($validPlayerIds)) {
                OneSignal::sendPush(
                    [
                        'target_channel'        => 'push',
                        'android_channel_id'    => config('one-signal.channel_id'),
                        'include_player_ids'    => $validPlayerIds,
                        'headings'              => $headings,
                        'contents'              => $contents,
                        'data'                  => [
                            'type'      => NotificationType::ACCOUNT_VERIFICATION->value,
                            'customer_id' => $customer->id,
                            'verification_status' => $customer->verification_status,
                            'verified_at' => $customer->verified_at?->format('Y-m-d H:i:s'),
                        ]
                    ],
                    $message
                );

                Log::info('[VerificationStatusNotificationAction] Push notification sent', [
                    'customer_id' => $customer->id,
                    'verification_status' => $customer->verification_status,
                ]);
            }

            $customer->notify(new VerificationStatusNotification($customer));

            Log::info('[VerificationStatusNotificationAction] Notification sent', [
                'customer_id' => $customer->id,
                'verification_status' => $customer->verification_status,
            ]);

            Log::info('[VerificationStatusNotificationAction] completed', [
                'customer_id' => $customer->id,
                'verification_status' => $customer->verification_status,
            ]);
        } catch (Exception $e) {
            Log::error("[VerificationStatusNotificationAction] Error send notif", [
                'class'     => 'VerificationStatusNotificationAction',
                'customer_id' => $customer->id,
                'verification_status' => $customer->verification_status,
                'message'   => $e->getMessage(),
                'trace'     => $e->getTraceAsString(),
            ]);
        }
    }
}
