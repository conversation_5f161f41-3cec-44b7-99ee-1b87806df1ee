<?php

namespace App\Actions\Informations;

use App\Models\Informations\ServiceImprovement;
use App\Services\ImageCompressor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreServiceImprovementAction
{
    use AsAction;

    public function handle(Request $request)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();
            $serviceImprovement->fill($request->validated());
            $serviceImprovement->slug = $this->generateSlug($serviceImprovement->title);
            $serviceImprovement->updated_by = auth()->id();

            if ($request->hasFile('image')) {
                if ($serviceImprovement->getRawOriginal('image') && Storage::disk('public')->exists($serviceImprovement->getRawOriginal('image'))) {
                    Storage::disk('public')->delete($serviceImprovement->getRawOriginal('image'));

                    Log::info('[ServiceImprovementController.update] Service improvement image deleted', [
                        'id' => $serviceImprovement->id,
                        'image' => $serviceImprovement->getRawOriginal('image'),
                    ]);
                }

                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'service-improvements-image'
                );
                $serviceImprovement->image = $result['path'];
            } else {
                $serviceImprovement->image = RouteHelper::ImageUrlToDb($request->image);
            }


            $serviceImprovement->zone_id = $request->zone_id;

            $zone = $this->getZoneById($request->zone_id);
            $serviceImprovement->zone_name = $zone ? $zone['name'] : null;
            $serviceImprovement->service_area_name = $zone ? $zone['service_area_name'] : null;

            $serviceImprovement->save();
            DB::commit();

            return redirect()->route('service-improvements.index')->with('success', 'Penyempurnaan Layanan berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error update Penyempurnaan Layanan', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('service-improvements.index')->with('error', 'Penyempurnaan Layanan gagal diubah');
        }
    }

    private function generateSlug($title)
    {
        $isSlugExists = ServiceImprovement::where('slug', Str::slug($title, '-'))->exists();
        if ($isSlugExists) {
            $title .= '-' . time();
        }

        return Str::slug($title, '-');
    }
}
