<?php

namespace App\Actions\Customer;

use App\Events\Customer\CustomerVerificationEvent;
use App\Http\Controllers\Api\ApiResponse;
use App\Http\Resources\Customer\CustomerResource;
use App\Models\Customer\Enum\VerificationStatus;
use App\Services\ImageCompressor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateVerificationAction
{
    use AsAction, ApiResponse;

    public function handle(Request $request)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();

            $customer = auth('api')->user();

            if ($customer->verification_status === VerificationStatus::VERIFIED->value) {
                return $this->errorResponse('Customer sudah terverifikasi', 400);
            }

            $customer->fill($request->validated());
            if ($request->hasFile('id_card_photo')) {
                $result = $compressor->compressAndStore(
                    $request->file('id_card_photo'),
                    'customer-id_card_photo'
                );
                $customer->id_card_photo = $result['path'];
            }
            if ($customer->verification_status !== VerificationStatus::VERIFIED->value) {
                $customer->verification_status = VerificationStatus::WAITING->value;
                $customer->verified_at = null;
                $customer->verified_by = null;
            }

            $customer->save();

            DB::commit();

            $customer->load([
                'province',
                'regency',
                'district',
                'village',
            ]);

            return $this->singleResponse(
                'Pengajuan verifikasi customer berhasil dikirim',
                new CustomerResource($customer)
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Gagal mengajukan verifikasi customer: ' . $e->getMessage(), 500);
        } finally {
            CustomerVerificationEvent::dispatch();
        }
    }
}
