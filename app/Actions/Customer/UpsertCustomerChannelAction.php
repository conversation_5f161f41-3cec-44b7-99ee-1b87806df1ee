<?php

namespace App\Actions\Customer;

use App\Actions\CustomerChannel\Registration\UpdateActivityRegistrationAction;
use App\DTO\UpsertCustomerChannelDTO;
use App\Libraries\SimbioApi\GetCustomerByChannel;
use App\Libraries\SimbioApi\GetCustomerById;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerChannel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpsertCustomerChannelAction
{
    use AsAction;

    public function handle(UpsertCustomerChannelDTO $dto): ResponseDTO
    {
        if (!$dto->hasValidIdentifier()) {
            return new ResponseDTO(
                status: 'error',
                message: 'Nomor saluran atau pelanggan tidak valid',
                data: null,
                code: 400,
                isUpdate: false,
            );
        }
        $externalCustomerId = $dto->externalCustomerId;
        $channelId          = $dto->channelId;
        $customerId         = $dto->customerId;

        try {
            DB::beginTransaction();

            Log::info('[UpsertCustomerChannelAction] started', [
                'externalCustomerId'    => $externalCustomerId,
                'channelId'             => $channelId,
                'customerId'            => $customerId,
            ]);

            if ($customerId) {
                $customer = Customer::find($customerId);
                if (!$customer) {
                    Log::error('[UpsertCustomerChannelAction] Customer not found', [
                        'customerId' => $customerId,
                    ]);
                    return new ResponseDTO(
                        status: 'error',
                        message: 'Data pelanggan tidak ditemukan',
                        data: null,
                        code: 404,
                        isUpdate: false,
                    );
                }

                $customerChannels = CustomerChannel::select('id')->whereHas('customers', function ($query) use ($customerId) {
                    $query->where('customer_id', $customerId);
                })->get();

                if ($customerChannels->count() >= 5) {
                    Log::error('[UpsertCustomerChannelAction] Jumlah saluran sudah mencapai maksimal', [
                        'externalCustomerId'    => $externalCustomerId,
                        'customerId'            => $customerId,
                        'currentCount'          => $customerChannels->count(),
                    ]);
                    return new ResponseDTO(
                        status: 'error',
                        message: 'Jumlah saluran sudah mencapai maksimal',
                        data: null,
                        code: 400,
                        isUpdate: false,
                    );
                }
            }

            if ($externalCustomerId) {
                Log::info('[UpsertCustomerChannelAction] Fetching customer channel by external customer ID', [
                    'externalCustomerId' => $externalCustomerId,
                ]);
                $getCustomerChannel = new GetCustomerById();
                $customerChannel    = $getCustomerChannel->run($externalCustomerId);
            } elseif ($channelId) {
                Log::info('[UpsertCustomerChannelAction] Fetching customer channel by channel ID', [
                    'channelId' => $channelId,
                ]);
                $getCustomerChannel = new GetCustomerByChannel();
                $customerChannel    = $getCustomerChannel->run($channelId);
            } else {
                Log::error('[UpsertCustomerChannelAction] No valid identifier provided for customer channel', [
                    'externalCustomerId' => $externalCustomerId,
                    'channelId' => $channelId
                ]);

                return new ResponseDTO(
                    status: 'error',
                    message: 'Nomor saluran tidak valid',
                    code: 400,
                    data: null,
                    isUpdate: false,
                );
            }

            if (count($customerChannel) === 0) {
                Log::error('[UpsertCustomerChannelAction] Customer channel not found', [
                    'externalCustomerId'    => $externalCustomerId,
                    'channelId'             => $channelId,
                    'customerId'            => $customerId,
                ]);
                return new ResponseDTO(
                    status: 'error',
                    message: 'Saluran tidak ditemukan',
                    code: 404,
                    data: null,
                    isUpdate: false,
                );
            }

            $upsert     = $this->upsert($customerChannel, $customerId);
            $channel    = $upsert['channel'];
            $isUpdate   = $upsert['isUpdate'];

            Log::info('[UpsertCustomerChannelAction] Successfull', [
                'channelId'         => $channel->id,
                'customerId'        => $customerId,
                'externalCustomerId' => $externalCustomerId,
                'isUpdate'          => $isUpdate,
                'status'            => $customerChannel['status'] ?? null,
                'statusRegistration' => $customerChannel['status_registration'] ?? null,
            ]);

            return new ResponseDTO(
                status: 'success',
                message: $isUpdate ? 'Saluran sudah tersedia' : 'Data saluran berhasil ditambahkan',
                data: $channel,
                code: 201,
                isUpdate: $isUpdate,
            );
        } catch (\Exception $e) {
            Log::error('[UpsertCustomerChannelAction] Error', [
                'error'         => $e->getMessage(),
                'channel'       => $externalCustomerId,
                'customerId'    => $customerId,
            ]);

            DB::rollBack();
            return new ResponseDTO(
                status: 'error',
                message: 'Gagal menambahkan saluran, nomor saluran tidak ditemukan.',
                code: 500,
                data: null,
                isUpdate: false,
            );
        } finally {
            DB::commit();

            if ($externalCustomerId) {
                UpdateActivityRegistrationAction::run($externalCustomerId);
            }

            Log::info('[UpsertCustomerChannelAction] Finished', [
                'customerId' => $customerId,
            ]);
        }
    }

    private function upsert($customerChannel, $customerId = null)
    {
        $groupCategory  = $customerChannel['group_category'] ?? null;
        $region         = $customerChannel['regions'] ?? null;
        $unit           = $customerChannel['units'] ?? null;
        $waterMeter     = $customerChannel['water_meter'] ?? null;
        $size           = $customerChannel['sizes'] ?? null;
        $subDistrict    = $customerChannel['sub_districts'] ?? null;
        $village        = $customerChannel['villages'] ?? null;

        $isCustomerChannelExists = false;
        if ($customerId) {
            $isCustomerChannelExists = CustomerChannel::where('external_customer_id', $customerChannel['id'])
                ->whereHas('customers', function ($query) use ($customerId) {
                    $query->where('customer_id', $customerId);
                })
                ->exists();
        }

        $channel = CustomerChannel::firstOrCreate(
            [
                'external_customer_id'  => $customerChannel['id'],
            ]
        );
        $channel->fill(
            [
                'channel'           => $customerChannel['channel'] ?? null,
                'name'              => $customerChannel['name'] ?? null,
                'address'           => $customerChannel['address'] ?? null,
                'sequence_number'   => $customerChannel['sequence_number'] ?? null,
                'telephone'         => $customerChannel['telephone'] ?? null,
                'telephone_2'       => $customerChannel['telephone_2'] ?? null,
                'fullname'          => $customerChannel['fullname'] ?? null,
                'address_ktp'       => $customerChannel['address_ktp'] ?? null,
                'no_ktp'            => $customerChannel['no_ktp'] ?? null,
                'profession'        => $customerChannel['profession'] ?? null,
                'land_status'       => $customerChannel['land_status'] ?? null,
                'land_document'     => $customerChannel['land_document'] ?? null,
                'occupant'          => $customerChannel['occupant'] ?? null,
                'used_for'          => $customerChannel['used_for'] ?? null,
                'etc'               => $customerChannel['etc'] ?? null,
                'electrical_power'  => $customerChannel['electrical_power'] ?? null,
                'building_area'     => $customerChannel['building_area'] ?? null,
                'id_baseline'       => $customerChannel['id_baseline'] ?? null,
                'no_spl'            => $customerChannel['no_spl'] ?? null,
                'id_card_photo'     => $customerChannel['id_card_photo'] ?? null,
                'family_card_photo' => $customerChannel['family_card_photo'] ?? null,
                'house_image_registration' => $customerChannel['house_image_registration'] ?? null,
                'long_registration' => $customerChannel['long_registration'] ?? null,
                'lat_registration'  => $customerChannel['lat_registration'] ?? null,
                'house_image'       => $customerChannel['house_image'] ?? null,
                'long'              => $customerChannel['long'] ?? null,
                'lat'               => $customerChannel['lat'] ?? null,
                'meter_number'      => $customerChannel['meter_number'] ?? null,
                'protected_plastic' => $customerChannel['protected_plastic'] ?? null,
                'meter_seal'        => $customerChannel['meter_seal'] ?? null,
                'meter_then'        => $customerChannel['meter_then'] ?? null,
                'meter_then_past'   => $customerChannel['meter_then_past'] ?? null,
                'use_then'          => $customerChannel['use_then'] ?? null,
                'use_then_past'     => $customerChannel['use_then_past'] ?? null,
                'cost'              => $customerChannel['cost'] ?? null,
                'fare_cost'         => $customerChannel['fare_cost'] ?? null,
                'load_cost'         => $customerChannel['load_cost'] ?? null,
                'meter_image'       => $customerChannel['meter_image'] ?? null,
                'status'            => $customerChannel['status'] ?? null,
                'status_registration'   => $customerChannel['status_registration'] ?? null,
                'decline_reason'        => $customerChannel['decline_reason'] ?? null,
                'registration_source'   => $customerChannel['registration_source'] ?? null,
                'disconnect_date'       => $customerChannel['disconnect_date'] ?? null,
                'register_date'         => $customerChannel['register_date'] ?? null,
                'install_date'          => $customerChannel['install_date'] ?? null,
                'pay_date'              => $customerChannel['pay_date'] ?? null,
                'admit_date'            => $customerChannel['admit_date'] ?? null,
                'temporary_disconnect_date' => $customerChannel['temporary_disconnect_date'] ?? null,
                'new_installation_cost'     => $customerChannel['new_installation_cost'] ?? null,
                'tertiary_cost'             => $customerChannel['tertiary_cost'] ?? null,
                'new_installation_category' => $customerChannel['new_installation_category'] ?? null,
                'collective_name'           => $customerChannel['collective_name'] ?? null,
                'is_hankam'                 => $customerChannel['is_hankam'] ?? null,

                'id_region'             => $customerChannel['id_region'] ?? null,
                'unit_code'             => $customerChannel['unit_code'] ?? null,
                'id_group'              => $customerChannel['id_group'] ?? null,
                'id_meter_condition'    => $customerChannel['id_meter_condition'] ?? null,
                'id_brand'              => $customerChannel['id_brand'] ?? null,
                'id_size'               => $customerChannel['id_size'] ?? null,
                'id_transaction'        => $customerChannel['id_transaction'] ?? null,
                'id_reduction'          => $customerChannel['id_reduction'] ?? null,
                'id_sub_district'       => $customerChannel['id_sub_district'] ?? null,
                'name_sub_district'     => $subDistrict['name_sub_district'] ?? null,
                'id_village'            => $customerChannel['id_village'] ?? null,
                'name_village'          => $village['name_village'] ?? null,
                'created_by'            => $customerChannel['created_by'] ?? null,
                'updated_by'            => $customerChannel['updated_by'] ?? null,
                'deleted_by'            => $customerChannel['deleted_by'] ?? null,
                'pts'                   => $customerChannel['pts'] ?? null,

                'region_code'               => $region['region_code'] ?? null,
                'region_name'               => $region['region_name'] ?? null,
                'id_service_zone'           => $region['id_service_zone'] ?? null,
                'zone_name'                 => $region['service_zones']['name'] ?? null,
                'zone_service_area_name'    => $region['service_zones']['service_area_name'] ?? null,
                'unit_name'                 => $unit['unit_name'] ?? null,
                'group_name'                => $groupCategory['group'] ?? null,
                'group_category'            => $groupCategory['category'] ?? null,
                'group_description'         => $groupCategory['description'] ?? null,
                'group_customer_type'       => $groupCategory['customer_type'] ?? null,
                'water_meter_brand'         => $waterMeter['brand_meter'] ?? null,
                'water_meter_size'          => $size['size'] ?? null,
            ]
        );
        $channel->save();

        if ($customerId) {
            $channel->customers()->syncWithoutDetaching([$customerId]);
        }

        return [
            'channel'   => $channel,
            'isUpdate'  => $isCustomerChannelExists,
        ];
    }
}

class ResponseDTO
{
    public function __construct(
        public string $status,
        public string $message,
        public ?CustomerChannel $data,
        public int $code,
        public bool $isUpdate,
    ) {}
}
