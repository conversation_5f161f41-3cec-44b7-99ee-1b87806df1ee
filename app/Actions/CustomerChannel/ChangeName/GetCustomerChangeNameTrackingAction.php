<?php

namespace App\Actions\CustomerChannel\ChangeName;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNameTrackingApi;
use App\Models\Customer\CustomerChannelChange;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCustomerChangeNameTrackingAction
{
    use AsAction, ApiResponse;

    public function handle($id)
    {
        try {
            $customerChangeName = CustomerChannelChange::where('id', $id)
                ->where('customer_id', auth('api')->id())
                ->first();

            if (!$customerChangeName) {
                Log::error('[GetCustomerChangeNameTrackingAction] Customer channel not found', [
                    'id' => $id,
                ]);
                return $this->errorResponse(
                    'Customer channel not found',
                    404
                );
            }

            $externalCustomerId = $customerChangeName->external_customer_id;
            $getCustomerChangeNameTracking = new GetCustomerChangeNameTrackingApi();
            $trackings = $getCustomerChangeNameTracking->run($externalCustomerId);

            $transformedTrackings = $this->transformIdCustomerFields($trackings);

            return $this->singleResponse(
                'Change name tracking',
                $transformedTrackings
            );
        } catch (\Exception $e) {
            Log::error('[GetCustomerChangeNameTrackingAction] Error fetching change name tracking', [
                'error' => $e->getMessage(),
            ]);
            return $this->errorResponse(
                'Failed to fetch change name tracking: ' . $e->getMessage(),
                500
            );
        }
    }

    private function transformIdCustomerFields($data)
    {
        if (is_array($data)) {
            $transformed = [];
            foreach ($data as $key => $value) {
                if ($key === 'id_customer') {
                    $transformed['external_customer_id'] = $this->transformIdCustomerFields($value);
                } else {
                    $transformed[$key] = $this->transformIdCustomerFields($value);
                }
            }
            return $transformed;
        }
        return $data;
    }
}
