<?php

namespace App\Actions\CustomerChannel\ChangeName;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\ChangeName\CustomerChangeNamePaymentApi;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNamePaymentStatusApi;
use App\Models\Customer\CustomerChannelChange;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreChangeNamePaymentAction
{
    use AsAction, ApiResponse;

    public function handle(Request $request)
    {
        try {
            $changeNamePayment = new CustomerChangeNamePaymentApi();
            $changeNamePayment = $changeNamePayment->run($request);

            $customerChannelChange = CustomerChannelChange::where('id_transaction', $request->id_transaction)->first();

            if ($changeNamePayment && $changeNamePayment['id_transaction']) {
                $customerChannelChange->payment_photo = $request->payment_photo->store('customer-change-name-payment', 'public');
                $customerChannelChange->save();

                $this->updateStatus($customerChannelChange);

                Log::info('[StoreChangeNamePaymentAction] Change name payment submitted successfully', [
                    'id_transaction' => $request->id_transaction,
                    'payment_status' => $changeNamePayment['status'] ?? null,
                ]);
            } else {
                throw new \Exception('Failed to submit payment');
            }

            return $this->singleResponse(
                'Payment submitted successfully',
                $customerChannelChange
            );
        } catch (\Exception $e) {
            Log::error('[StoreChangeNamePaymentAction] Error submitting payment', [
                'error' => $e->getMessage(),
            ]);
            return $this->errorResponse(
                'Failed to submit payment: ' . $e->getMessage(),
                500
            );
        }
    }

    private function updateStatus(CustomerChannelChange $customerChannelChange)
    {
        try {
            $getCustomerChangeNamePayment = new GetCustomerChangeNamePaymentStatusApi();
            $paymentInfo = $getCustomerChangeNamePayment->run($customerChannelChange->id_transaction);

            Log::info('[StoreChangeNamePaymentAction] updateStatus - Payment info', [
                'paymentInfo' => $paymentInfo,
            ]);

            $customerChannelChange->current_status = $paymentInfo['current_status'] ?? $customerChannelChange->current_status;

            if (isset($paymentInfo['payment'])) {
                $customerChannelChange->payment_status = $paymentInfo['payment']['status'] ?? $customerChannelChange->payment_status;
            }

            $customerChannelChange->save();
        } catch (\Exception $e) {
            Log::error('[StoreChangeNamePaymentAction] updateStatus - Error fetching payment info', [
                'error' => $e->getMessage(),
            ]);
        }
    }
}
