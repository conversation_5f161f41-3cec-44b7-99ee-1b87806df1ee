<?php

namespace App\Actions\CustomerChannel\ChangeName;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNamePaymentApi;
use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Enum\PaymentStatus;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class GetChangeNamePaymentAction
{
    use AsAction, ApiResponse;

    public function handle($id)
    {
        try {
            $customerChangeName = CustomerChannelChange::where('id', $id)
                ->where('customer_id', auth('api')->id())
                ->first();
            if (!$customerChangeName) {
                Log::error('[GetChangeNamePaymentAction] Change name not found', [
                    'id' => $id,
                ]);
                return $this->errorResponse(
                    'Change name not found',
                    404
                );
            }

            $getCustomerChangeNamePayment = new GetCustomerChangeNamePaymentApi();
            $paymentInfo = $getCustomerChangeNamePayment->run($customerChangeName->id_transaction);

            unset($paymentInfo['id']);
            $paymentInfo['external_customer_id'] = $customerChangeName->external_customer_id;
            if (isset($paymentInfo['status'])) {
                $paymentInfo['status_description'] = PaymentStatus::getDescription($paymentInfo['status']);
            }

            return $this->singleResponse(
                'Payment info',
                $paymentInfo
            );
        } catch (\Exception $e) {
            Log::error('[GetChangeNamePaymentAction] Error fetching payment info', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->errorResponse(
                'Failed to fetch payment info: ' . $e->getMessage(),
                500
            );
        }
    }
}
