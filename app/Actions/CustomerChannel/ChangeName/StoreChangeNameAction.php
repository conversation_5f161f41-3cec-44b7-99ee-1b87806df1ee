<?php

namespace App\Actions\CustomerChannel\ChangeName;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\ChangeName\CustomerChangeNameApi;
use App\Libraries\SimbioApi\ChangeName\GetChangeNameCategoryApi;
use App\Models\Activity\Activity;
use App\Models\Activity\Enum\ActivityStatus;
use App\Models\Customer\CustomerChannelChange;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreChangeNameAction
{
    use AsAction, ApiResponse;

    public function handle(Request $request)
    {
        DB::beginTransaction();
        try {
            $getChangeNameCategory = new GetChangeNameCategoryApi();
            $changeNameCategories = $getChangeNameCategory->run();

            if (!in_array($request->change_name_category_id, array_column($changeNameCategories, 'id'))) {
                return $this->errorResponse(
                    'Invalid change name category',
                    400
                );
            }

            $selectedCategory = collect($changeNameCategories)->firstWhere('id', $request->change_name_category_id);

            $reason = $selectedCategory['name'] ?? null;
            $simbioRequest = new Request();
            $simbioRequest->merge([
                'id_customer'   => $request->external_customer_id,
                'new_name'      => $request->new_name,
                'reason'        => $reason,
                'description'   => $request->description
            ]);
            $changeName = new CustomerChangeNameApi();
            $changeName = $changeName->run($simbioRequest);

            if ($changeName) {
                $customerChannelChange                          = new CustomerChannelChange();
                $customerChannelChange->customer_id             = auth('api')->id();
                $customerChannelChange->external_customer_id    = $request->external_customer_id;
                $customerChannelChange->old_name                = $changeName['old_name'] ?? null;
                $customerChannelChange->new_name                = $request->new_name;
                $customerChannelChange->reason                  = $reason;
                $customerChannelChange->description             = $request->description;
                $customerChannelChange->id_transaction          = $changeName['id_transaction'] ?? null;
                $customerChannelChange->cost                    = $changeName['cost'] ?? 0;
                $customerChannelChange->current_status          = $changeName['current_status'] ?? null;
                $customerChannelChange->save();

                $activity = new Activity();
                $activity->subject()->associate($customerChannelChange);
                $activity->code         = $customerChannelChange->id_transaction;
                $activity->title        = 'Permohonan Balik Nama Pelanggan';
                $activity->category     = 'Balik Nama Pelanggan';
                $activity->description  = $request->new_name;
                $activity->status       = ActivityStatus::WAITING->value;
                $activity->customer_id  = auth('api')->user()->id;
                $activity->save();

                DB::commit();

                Log::info('[StoreChangeNameAction] Change name submitted successfully', [
                    'customer_channel_change_id' => $customerChannelChange->id,
                    'customer_id' => auth('api')->user()->id,
                    'external_customer_id' => $request->external_customer_id,
                ]);

                return $this->singleResponse(
                    'Change name submitted successfully',
                    $customerChannelChange
                );
            }

            Log::error('[StoreChangeNameAction] Change name failed', [
                'request'   => $request->all(),
            ]);

            return $this->errorResponse(
                'Change name failed',
                400
            );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('[StoreChangeNameAction] Error submit change name', [
                'request'   => $request->all(),
                'error'     => $e->getMessage(),
            ]);
            return $this->errorResponse(
                'Failed to fetch change name: ' . $e->getMessage(),
                500
            );
        }
    }
}
