<?php

namespace App\Actions\CustomerChannel\ChangeName;

use App\Http\Controllers\Api\ApiResponse;
use App\Jobs\Customer\SyncCustomerChannelJob;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNamePaymentStatusApi;
use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Enum\ChangeNameStatus;
use App\Models\Customer\Enum\PaymentStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateChangeNameAction
{
    use AsAction, ApiResponse;

    public function handle(string $idTransaction)
    {
        DB::beginTransaction();
        try {
            $paymentStatus  = new GetCustomerChangeNamePaymentStatusApi();
            $payment        = $paymentStatus->run($idTransaction);
            $customerChannelChange = CustomerChannelChange::where('id_transaction', $idTransaction)->first();

            if ($payment && $customerChannelChange) {
                $isUpdated = false;
                if ($payment['current_status'] && $customerChannelChange->current_status !== $payment['current_status']) {
                    $isUpdated = true;
                    $customerChannelChange->current_status  = $payment['current_status'];
                }
                if ($payment['payment'] && $payment['payment']['status'] && $customerChannelChange->payment_status !== $payment['payment']['status']) {
                    $isUpdated = true;
                    $customerChannelChange->payment_status  = $payment['payment']['status'];
                }

                $customerChannelChange->save();

                DB::commit();

                $msg = 'Nothing to update';
                if ($isUpdated) {
                    $msg = 'Change name updated.';

                    Log::info('[UpdateChangeNameAction] Change name updated', [
                        'id_transaction'    => $idTransaction,
                        'current_status'    => $customerChannelChange->current_status,
                        'payment_status'    => $customerChannelChange->payment_status,
                    ]);
                }

                if (
                    $customerChannelChange->payment_status === PaymentStatus::APPROVED->value ||
                    $customerChannelChange->current_status === ChangeNameStatus::PAID->value ||
                    $customerChannelChange->current_status === ChangeNameStatus::FINISHED->value
                ) {
                    SyncCustomerChannelJob::dispatch($customerChannelChange->external_customer_id);
                    Log::info('[UpdateChangeNameAction] Sync Customer Channel Job dispatched', [
                        'external_customer_id' => $customerChannelChange->external_customer_id,
                    ]);
                }

                return [
                    'status' => 'success',
                    'message' => $msg,
                    'code' => 200,
                ];
            }

            return [
                'status' => 'success',
                'message' => 'Change name update failed',
                'code' => 400,
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('[UpdateChangeNameAction] Error fetching change name status', [
                'id_transaction'    => $idTransaction,
                'error'             => $e->getMessage(),
            ]);
            return [
                'status' => 'success',
                'message' => 'Failed to fetch change name',
                'code' => 500,
            ];
        }
    }
}
