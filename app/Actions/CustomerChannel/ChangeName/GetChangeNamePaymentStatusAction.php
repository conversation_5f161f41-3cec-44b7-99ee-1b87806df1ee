<?php

namespace App\Actions\CustomerChannel\ChangeName;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNamePaymentStatusApi;
use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Enum\ChangeNameStatus;
use App\Models\Customer\Enum\PaymentStatus;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class GetChangeNamePaymentStatusAction
{
    use AsAction, ApiResponse;

    public function handle($id)
    {
        try {
            $customerChangeName = CustomerChannelChange::where('id', $id)
                ->where('customer_id', auth('api')->id())
                ->first();
            if (!$customerChangeName) {
                Log::error('[GetChangeNamePaymentStatusAction] Change name not found', [
                    'id' => $id,
                ]);
                return $this->errorResponse(
                    'Change name not found',
                    404
                );
            }

            $getCustomerChangeNamePayment = new GetCustomerChangeNamePaymentStatusApi();
            $paymentInfo = $getCustomerChangeNamePayment->run($customerChangeName->id_transaction);

            if (isset($paymentInfo['current_status'])) {
                $paymentInfo['current_status_description'] = ChangeNameStatus::getDescription($paymentInfo['current_status']);
            }
            unset($paymentInfo['id']);

            return $this->singleResponse(
                'Payment info',
                $paymentInfo
            );
        } catch (\Exception $e) {
            Log::error('[GetChangeNamePaymentStatusAction] Error fetching payment info', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->errorResponse(
                'Failed to fetch payment info: ' . $e->getMessage(),
                500
            );
        }
    }
}
