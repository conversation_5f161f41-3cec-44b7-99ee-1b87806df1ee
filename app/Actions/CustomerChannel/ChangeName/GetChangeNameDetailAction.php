<?php

namespace App\Actions\CustomerChannel\ChangeName;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNamePaymentApi;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNamePaymentStatusApi;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNameTrackingApi;
use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Enum\ChangeNameStatus;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class GetChangeNameDetailAction
{
    use AsAction, ApiResponse;

    public function handle($id)
    {
        $customerChangeName = CustomerChannelChange::where('id', $id)
            ->where('customer_id', auth('api')->id())
            ->first();

        if (!$customerChangeName) {
            return $this->errorResponse(
                'Balik Nama tidak ditemukan',
                404
            );
        }

        $paymentAccount = null;
        $payment = null;
        $tracking = null;
        try {
            $getPayment = new GetCustomerChangeNamePaymentApi();
            $paymentAccount = $getPayment->run($customerChangeName->id_transaction);
        } catch (\Exception $e) {
            Log::error('[GetChangeNameDetailAction] Error calling GetCustomerChangeNamePaymentApi', [
                'error' => $e->getMessage(),
            ]);
        }

        try {
            $getPaymentInfo = new GetCustomerChangeNamePaymentStatusApi();
            $payment = $getPaymentInfo->run($customerChangeName->id_transaction);

            if (isset($payment['current_status'])) {
                $payment['current_status_description'] = ChangeNameStatus::getDescription($payment['current_status']);
            }
            unset($payment['id']);
        } catch (\Exception $e) {
            Log::error('[GetChangeNameDetailAction] Error calling GetCustomerChangeNamePaymentStatusApi', [
                'error' => $e->getMessage(),
            ]);
        }

        $payment['channel'] = $paymentAccount['channel'] ?? null;
        $payment['name'] = $paymentAccount['name'] ?? null;
        $payment['cost'] = $paymentAccount['cost'] ?? null;
        $payment['bank_account_number'] = $paymentAccount['bank_account_number'] ?? null;

        try {
            $getTracking = new GetCustomerChangeNameTrackingApi();
            $tracking = $getTracking->run($customerChangeName->external_customer_id);
            unset($tracking['id']);
        } catch (\Exception $e) {
            Log::error('[GetChangeNameDetailAction] Error calling GetCustomerChangeNameTrackingApi', [
                'error' => $e->getMessage(),
            ]);
        }

        return $this->singleResponse(
            'Detail Balik Nama',
            [
                'customer_change_name'  => $customerChangeName,
                'payment'               => $payment ?? null,
                'tracking'              => $tracking ?? null,
            ]
        );
    }
}
