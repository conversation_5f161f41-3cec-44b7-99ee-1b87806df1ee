<?php

namespace App\Actions\CustomerChannel\Registration;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\Registration\CustomerRegistrationPaymentApi;
use App\Models\Customer\CustomerChannel;
use App\Models\Customer\Enum\RegistrationStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreCustomerRegistrationPaymentAction
{
    use AsAction, ApiResponse;

    public function handle(Request $request)
    {
        try {
            $registrationPayment = new CustomerRegistrationPaymentApi();
            $registrationPayment = $registrationPayment->run($request);

            if ($registrationPayment && $registrationPayment['id_transaction']) {
                $this->updateStatus($request->external_customer_id);

                Log::info('[StoreCustomerRegistrationPaymentAction] Registration payment submitted successfully', [
                    'external_customer_id' => $request->external_customer_id,
                    'id_transaction' => $registrationPayment['id_transaction'],
                ]);
            } else {
                throw new \Exception('Failed to submit payment');
            }

            return $this->singleResponse(
                'Payment submitted successfully',
                $registrationPayment
            );
        } catch (\Exception $e) {
            Log::error('[StoreCustomerRegistrationPaymentAction] Error submitting payment', [
                'error' => $e->getMessage(),
            ]);
            return $this->errorResponse(
                'Failed to submit payment: ' . $e->getMessage(),
                500
            );
        }
    }

    private function updateStatus($externalCustomerId)
    {
        try {
            DB::beginTransaction();

            $customerChannel = CustomerChannel::select('id')
                ->where('external_customer_id', $externalCustomerId)->first();

            $customerChannel->status_registration = RegistrationStatus::PAYMENT_REVIEW->value;
            $customerChannel->save();

            UpdateActivityRegistrationAction::run($customerChannel);

            DB::commit();
        } catch (\Exception $e) {
            Log::error('[StoreCustomerRegistrationPaymentAction] updateStatus - Error updating activity', [
                'error' => $e->getMessage(),
            ]);
        }
    }
}
