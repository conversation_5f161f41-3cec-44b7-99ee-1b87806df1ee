<?php

namespace App\Actions\CustomerChannel\Registration;

use App\Actions\Notification\CustomerChannelRegistrationNotificationAction;
use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\Registration\CustomerRegistrationApi;
use App\Models\Activity\Activity;
use App\Models\Activity\Enum\ActivityStatus;
use App\Models\Customer\CustomerChannel;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class StoreCustomerRegistrationAction
{
    use AsAction, ApiResponse;

    public function handle(Request $request)
    {
        $customerId = auth('api')->id();
        $customer = auth('api')->user();

        if (!$customer->is_profile_complete) {
            Log::error('[StoreCustomerRegistrationAction] Profile customer belum lengkap', [
                'customerId' => $customerId,
            ]);
            return $this->errorResponse('Profile customer belum lengkap, lakukan verifikasi terlebih dahulu', 400);
        }

        $isOngoingRegistrationExist = Activity::where('customer_id', $customerId)
            ->where('subject_type', CustomerChannel::class)
            ->whereHasMorph(
                'subject',
                [CustomerChannel::class]
            )
            ->whereNotIn('status', [ActivityStatus::CANCELED->value, ActivityStatus::FINISHED->value])
            ->exists();

        if ($isOngoingRegistrationExist) {
            Log::error('[StoreCustomerRegistrationAction] Terdapat permohonan pendaftaran yang belum selesai', [
                'customerId' => $customerId,
                'isOngoingRegistrationExist' => $isOngoingRegistrationExist,
            ]);
            return $this->errorResponse('Terdapat permohonan pendaftaran yang belum selesai, silakan selesaikan terlebih dahulu', 400);
        }

        try {
            DB::beginTransaction();

            $simbioRequest = clone $request;
            $customerRegistrationApi = new CustomerRegistrationApi();
            if (!empty($customer->id_card_photo)) {
                $urlPath = parse_url($customer->id_card_photo, PHP_URL_PATH);
                $storagePath = str_replace('/storage/', '', $urlPath);
                $filePath = storage_path("app/public/{$storagePath}");

                $customerRegistrationApi->setStorageFile('id_card_photo', $filePath);
            }
            $apiResponse = $customerRegistrationApi->run($simbioRequest);

            if (empty($apiResponse)) {
                throw new \Exception('Failed to submit registration to external API');
            }

            $data = $this->prepareSaveData($request, $customer, $apiResponse);

            $customerChannel = CustomerChannel::create($data);

            $customer->channels()->attach($customerChannel->id);

            $activity = new Activity();
            $activity->subject()->associate($customerChannel);
            $activity->code         = $customerChannel->no_spl ?? null;
            $activity->title        = $customerChannel->address ?? null;
            $activity->category     = 'Pendaftaran Pasang Baru';
            $activity->description  = $customerChannel->name;
            $activity->status       = ActivityStatus::WAITING->value;
            $activity->customer_id  = auth('api')->user()->id;
            $activity->save();

            Log::info('[StoreCustomerRegistrationAction] Customer registration created successfully', [
                'customer_channel_id' => $customerChannel->id,
                'customer_id' => $customer->id,
                'external_customer_id' => $data['external_customer_id'] ?? null,
            ]);

            DB::commit();

            CustomerChannelRegistrationNotificationAction::run($customerChannel);

            return $this->successCreatedResponse('Registration submitted successfully', $customerChannel);
        } catch (\Exception $e) {
            Log::error('[StoreCustomerRegistrationAction] Failed to create customer registration', [
                'error' => $e->getMessage(),
                'request' => $request->except(['id_card_photo', 'family_card_photo', 'house_image_registration']),
            ]);
            DB::rollBack();
            return $this->errorResponse('Failed to submit registration: ' . $e->getMessage(), 500);
        }
    }

    private function prepareSaveData(Request $request, $customer,  $apiResponse): array
    {
        if (isset($apiResponse['id'])) {
            $data['external_customer_id'] = $apiResponse['id'];
            $data['register_date'] = isset($apiResponse['register_date'])
                ? Carbon::parse($apiResponse['register_date'])->format('Y-m-d')
                : null;
            $data['fullname']                   = $apiResponse['fullname'] ?? null;
            $data['address_ktp']                = $apiResponse['address_ktp'] ?? null;
            $data['no_ktp']                     = $apiResponse['no_ktp'] ?? null;
            $data['profession']                 = $apiResponse['profession'] ?? null;
            $data['telephone']                  = $apiResponse['telephone'] ?? null;
            $data['name']                       = $apiResponse['name'] ?? null;
            $data['address']                    = $apiResponse['address'] ?? null;
            $data['id_sub_district']            = $apiResponse['id_sub_district'] ?? null;
            $data['id_village']                 = $apiResponse['id_village'] ?? null;
            $data['name_sub_district']          = $apiResponse['name_sub_district'] ?? null;
            $data['name_village']               = $apiResponse['name_village'] ?? null;
            $data['land_status']                = $apiResponse['land_status'] ?? null;
            $data['land_document']              = $apiResponse['land_document'] ?? null;
            $data['occupant']                   = $apiResponse['occupant'] ?? null;
            $data['used_for']                   = $apiResponse['used_for'] ?? null;
            $data['etc']                        = $apiResponse['etc'] ?? null;
            $data['electrical_power']           = $apiResponse['electrical_power'] ?? null;
            $data['building_area']              = $apiResponse['building_area'] ?? null;
            $data['family_card_photo']          = $apiResponse['family_card_photo'] ?? null;
            $data['house_image_registration']   = $apiResponse['house_image_registration'] ?? null;
            $data['long_registration']          = $apiResponse['long_registration'] ?? null;
            $data['lat_registration']           = $apiResponse['lat_registration'] ?? null;
            $data['status_registration']        = $apiResponse['status_registration'] ?? null;
            $data['id_transaction']             = $apiResponse['id_transaction'] ?? null;
            $data['no_spl']                     = $apiResponse['no_spl'] ?? null;
            $data['registration_source']        = $apiResponse['registration_source'] ?? null;
        }

        $data['id_card_photo']       = $customer->id_card_photo;
        $data['district_id']         = $request->id_sub_district;
        $data['village_id']          = $request->id_village;
        $data['status_registration'] = 'NEED_REVIEW';
        $data['registration_source'] = 'TPJ_INSIDE';

        if ($request->hasFile('family_card_photo')) {
            $data['family_card_photo'] = $this->uploadFile($request->file('family_card_photo'), 'customer-channels/family-cards');
        }

        if ($request->hasFile('house_image_registration')) {
            $data['house_image_registration'] = $this->uploadFile($request->file('house_image_registration'), 'customer-channels/house-images');
        }

        return $data;
    }

    private function uploadFile($file, string $path): string
    {
        $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
        return $file->storeAs($path, $filename, 'public');
    }
}
