<?php

namespace App\Actions\CustomerChannel\Registration;

use App\Actions\Notification\CustomerChannelRegistrationNotificationAction;
use App\Models\Activity\Activity;
use App\Models\Customer\CustomerChannel;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateActivityRegistrationAction
{
    use AsAction;

    public function handle($externalCustomerId)
    {
        $isStatusUpdated = false;
        $customerChannel = CustomerChannel::select('id', 'status', 'status_registration', 'registration_source')
            ->where('external_customer_id', $externalCustomerId)
            ->first();
        try {
            if (!$customerChannel) {
                Log::error('[UpdateActivityRegistrationAction] Customer channel not found', [
                    'externalCustomerId' => $externalCustomerId,
                ]);
                return;
            }

            $activity = Activity::where('subject_type', CustomerChannel::class)
                ->where('subject_id', $customerChannel->id)
                ->first();

            if (!$activity) {
                Log::error('[UpdateActivityRegistrationAction] Activity not found', [
                    'customerChannelId' => $customerChannel->id,
                ]);
                return;
            }

            $activityStatus = $customerChannel->status_registration;

            if ($activityStatus === $activity->status) {
                Log::info('[UpdateActivityRegistrationAction] Activity status not changed', [
                    'customerChannelId' => $customerChannel->id,
                    'activityStatus' => $activity->status,
                    'registrationStatus' => $customerChannel->status_registration_description,
                ]);

                return;
            }

            $isStatusUpdated = true;

            $activity->status = $activityStatus;
            $activity->save();

            $activity->timelines()->create([
                'status'        => $activityStatus,
                'description'   => 'Status Pendaftaran Saluran diperbarui ke ' . $customerChannel->status_registration_description
            ]);

            Log::info('[UpdateActivityRegistrationAction] Activity updated', [
                'customerChannelId' => $customerChannel->id,
                'activityStatus' => $activityStatus,
                'isStatusUpdated' => $isStatusUpdated,
                'customerChannel' => $customerChannel,
            ]);
        } catch (\Exception $e) {
            Log::error('[UpdateActivityRegistrationAction] Error updating activity', [
                'error' => $e->getMessage(),
            ]);
        } finally {
            if ($customerChannel && $customerChannel->registration_source === 'TPJ_INSIDE' && $isStatusUpdated) {
                CustomerChannelRegistrationNotificationAction::run($customerChannel);
                Log::info('[UpdateActivityRegistrationAction] Notification sent', [
                    'customerChannelId' => $customerChannel->id,
                ]);
            }
        }
    }
}
