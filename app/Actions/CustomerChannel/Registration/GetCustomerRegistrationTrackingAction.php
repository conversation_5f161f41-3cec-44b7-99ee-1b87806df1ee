<?php

namespace App\Actions\CustomerChannel\Registration;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\Registration\GetCustomerRegistrationTrackingApi;
use App\Models\Customer\CustomerChannel;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCustomerRegistrationTrackingAction
{
    use AsAction, ApiResponse;

    public function handle($id)
    {
        try {
            $customerChannel = CustomerChannel::where('id', $id)->first();
            if (!$customerChannel) {
                Log::error('[GetCustomerRegistrationTrackingAction] Customer channel not found', [
                    'id' => $id,
                ]);
                return $this->errorResponse(
                    'Customer channel not found',
                    404
                );
            }
            $externalCustomerId = $customerChannel->external_customer_id;

            $getRegistrationTracking = new GetCustomerRegistrationTrackingApi();
            $paymentInfo = $getRegistrationTracking->run($externalCustomerId);

            unset($paymentInfo['id']);
            $paymentInfo['external_customer_id'] = $externalCustomerId;

            return $this->singleResponse(
                'Payment info',
                $paymentInfo
            );
        } catch (\Exception $e) {
            Log::error('[GetCustomerRegistrationTrackingAction] Error fetching tracking info', [
                'error' => $e->getMessage(),
            ]);
            return $this->errorResponse(
                'Failed to fetch tracking info: ' . $e->getMessage(),
                500
            );
        }
    }
}
