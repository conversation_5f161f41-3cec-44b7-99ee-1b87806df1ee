<?php

namespace App\Actions\CustomerChannel\Registration;

use App\Http\Controllers\Api\ApiResponse;
use App\Libraries\SimbioApi\Registration\GetCustomerRegistrationPaymentApi;
use App\Models\Customer\CustomerChannel;
use App\Models\Customer\Enum\PaymentStatus;
use Illuminate\Support\Facades\Log;
use Lorisleiva\Actions\Concerns\AsAction;

class GetCustomerRegistrationPaymentAction
{
    use AsAction, ApiResponse;

    public function handle($id)
    {
        try {
            $customerChannel = CustomerChannel::where('id', $id)->first();
            if (!$customerChannel) {
                Log::error('[GetCustomerRegistrationPaymentAction] Customer channel not found', [
                    'id' => $id,
                ]);
                return $this->errorResponse(
                    'Customer channel not found',
                    404
                );
            }
            $externalCustomerId = $customerChannel->external_customer_id;

            $getRegistrationPayment = new GetCustomerRegistrationPaymentApi();
            $paymentInfo = $getRegistrationPayment->run($externalCustomerId);

            unset($paymentInfo['id']);
            $paymentInfo['external_customer_id'] = $externalCustomerId;

            foreach ($paymentInfo['registration_payments'] as $key => $payment) {
                $paymentInfo['registration_payments'][$key]['status_description'] = PaymentStatus::getDescription($payment['status']);
            }

            return $this->singleResponse(
                'Payment info',
                $paymentInfo
            );
        } catch (\Exception $e) {
            Log::error('[GetCustomerRegistrationPaymentAction] Error fetching payment info', [
                'error' => $e->getMessage(),
            ]);
            return $this->errorResponse(
                'Failed to fetch payment info: ' . $e->getMessage(),
                500
            );
        }
    }
}
