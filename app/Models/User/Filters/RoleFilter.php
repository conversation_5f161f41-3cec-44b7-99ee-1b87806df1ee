<?php

namespace App\Models\User\Filters;

use App\Libraries\Filters\DropdownFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Models\User\Enum\UserRole;
use Spatie\Permission\Models\Role;

class RoleFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'role';
    protected $label    = 'Role';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.role';

    /**
     * @return array
     */
    protected function options(): array
    {
        $roles = Role::all();
        $options = [];
        foreach ($roles as $key => $value) {
            $options[$value->id] = $value->name;
        }

        return $options;
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        if($value!='' && $value!=null){
            return $query->whereHas('roles', function ($query) use ($value) {
                $query->where('name', $value);
            });
        }
        return $query;
    }
}
