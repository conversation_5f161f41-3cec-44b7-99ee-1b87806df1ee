<?php

namespace App\Models\User\Enum;

enum Gender: string
{
    const MALE      = "MALE";
    const FEMALE    = "FEMALE";
    const ALL       = "ALL";

    public static function getDescription($value): string
    {
        $result = '';
        if ($value === self::MALE) {
            $result = 'Laki - laki';
        } elseif ($value === self::FEMALE) {
            $result = 'Perempuan';
        } elseif ($value === self::ALL) {
            $result = 'Semua';
        }
        return $result;
    }
}
