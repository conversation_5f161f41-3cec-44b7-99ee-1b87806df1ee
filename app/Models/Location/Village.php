<?php

namespace App\Models\Location;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Village extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'id',
        'unique_id',
        'district_unique_id',
        'name'
    ];

    public function district()
    {
        return $this->belongsTo(District::class, 'district_unique_id', 'unique_id');
    }
}
