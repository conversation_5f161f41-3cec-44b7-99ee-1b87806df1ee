<?php

namespace App\Models\Location;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Regency extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'id',
        'unique_id',
        'province_unique_id',
        'name'
    ];

    public function province()
    {
        return $this->belongsTo(Province::class, 'province_unique_id', 'unique_id');
    }

    public function districts()
    {
        return $this->hasMany(District::class, 'regency_unique_id', 'unique_id');
    }
}
