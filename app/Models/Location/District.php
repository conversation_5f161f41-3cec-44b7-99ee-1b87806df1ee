<?php

namespace App\Models\Location;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class District extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'id',
        'unique_id',
        'regency_unique_id',
        'name'
    ];

    public function regency()
    {
        return $this->belongsTo(Regency::class, 'regency_unique_id', 'unique_id');
    }

    public function villages()
    {
        return $this->hasMany(Village::class, 'district_unique_id', 'unique_id');
    }
}
