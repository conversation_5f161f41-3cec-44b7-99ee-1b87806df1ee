<?php

namespace App\Models\Customer\Enum;

enum VerificationStatus: string
{
    case UNVERIFIED = 'UNVERIFIED';
    case WAITING    = 'WAITING';
    case VERIFIED   = 'VERIFIED';
    case REJECTED   = 'REJECTED';

    public static function getDescription(string $status): string
    {
        return match ($status) {
            self::UNVERIFIED->value => 'Belum diverifikasi',
            self::WAITING->value    => 'Menunggu verifikasi',
            self::VERIFIED->value   => 'Terverifikasi',
            self::REJECTED->value   => 'Di<PERSON><PERSON>',
            default                 => 'Status tidak dikenal',
        };
    }
}
