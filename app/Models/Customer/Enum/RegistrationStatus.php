<?php

namespace App\Models\Customer\Enum;

enum RegistrationStatus: string
{
    case WAITING = 'WAITING';
    case NEED_REVIEW = 'NEED_REVIEW';
    case RAB_RECEIVED = 'RAB_RECEIVED';
    case TERTIARY = 'TERTIARY';
    case PAYMENT = 'PAYMENT';
    case PAYMENT_REVIEW = 'PAYMENT_REVIEW';
    case PAYMENT_DECLINED = 'PAYMENT_DECLINED';
    case PAID = 'PAID';
    case VALIDATED = 'VALIDATED';
    case INSTALL = 'INSTALL';
    case CANCELED = 'CANCELED';
    case FINISHED = 'FINISHED';

    public static function getDescription(string $value): string
    {
        return match ($value) {
            self::WAITING->value => 'Menunggu Konfirmasi',
            self::NEED_REVIEW->value => 'Menunggu Tinjauan',
            self::RAB_RECEIVED->value => 'RAB Sudah Terbit',
            self::TERTIARY->value => 'Tersier',
            self::PAYMENT->value => 'Menunggu Pembayaran',
            self::PAYMENT_REVIEW->value => 'Menunggu Tinjauan Pembayaran',
            self::PAYMENT_DECLINED->value => 'Pembayaran Ditolak',
            self::VALIDATED->value => 'Tervalidasi',
            self::INSTALL->value => 'Pemasangan',
            self::PAID->value => 'Pembayaran Berhasil',
            self::CANCELED->value => 'Dibatalkan',
            self::FINISHED->value => 'Selesai',
            default => '',
        };
    }

    public static function getDescriptionEn(string $value): string
    {
        return match ($value) {
            self::WAITING->value => 'Waiting',
            self::NEED_REVIEW->value => 'Need Review',
            self::RAB_RECEIVED->value => 'RAB Received',
            self::TERTIARY->value => 'Tertiary',
            self::PAYMENT->value => 'Waiting for Payment',
            self::PAYMENT_REVIEW->value => 'Payment Review',
            self::PAYMENT_DECLINED->value => 'Payment Declined',
            self::VALIDATED->value => 'Validated',
            self::INSTALL->value => 'Installation',
            self::PAID->value => 'Paid',
            self::CANCELED->value => 'Canceled',
            self::FINISHED->value => 'Finished',
            default => '',
        };
    }

    public static function getNotificationMessage(string $value): string
    {
        return match ($value) {
            self::NEED_REVIEW->value => 'Pengajuan Pasang Baru berhasil diajukan dan sedang menunggu review Admin',
            self::WAITING->value => 'Pengajuanmu dalam tahap survey petugas',
            self::RAB_RECEIVED->value => 'Invoice Pasang Baru sudah terbit, silahkan melakukan pembayaran',
            self::TERTIARY->value => 'Invoice Pasang Baru sudah terbit, silahkan melakukan pembayaran',
            self::PAYMENT->value => 'Silahkan lakukan pembayaran untuk melanjutkan proses Pasang Baru',
            self::PAYMENT_REVIEW->value => 'Pembayaranmu berhasil dan sedang direview Admin',
            self::PAYMENT_DECLINED->value => 'Mohon maaf, review pembayaranmu ditolak, silahkan upload ulang foto bukti transfermu',
            self::PAID->value => 'Pembayaranmu berhasil dikonfirmasi',
            self::CANCELED->value => 'Pengajuan Pasang Baru dibatalkan oleh Admin',
            self::VALIDATED->value => 'Pengajuan Pasang Baru berhasil divalidasi',
            self::INSTALL->value => 'Pemasangan water meter akan dilakukan oleh petugas',
            self::FINISHED->value => 'Layanan Pasang Baru selesai, selamat menikmati layanan air bersih',
            default => '',
        };
    }

    public static function getNotificationMessageEn(string $value): string
    {
        return match ($value) {
            self::NEED_REVIEW->value => 'Your New Installation application has been successfully submitted and is awaiting Admin review',
            self::WAITING->value => 'Your application is currently being reviewed by an officer',
            self::RAB_RECEIVED->value => 'The New Installation Invoice has been issued. Please make payment',
            self::TERTIARY->value => 'The New Installation Invoice has been issued. Please make payment',
            self::PAYMENT->value => 'Please make payment to continue the New Installation process',
            self::PAYMENT_REVIEW->value => 'Your payment was successful and is being reviewed by the Admin',
            self::PAYMENT_DECLINED->value => 'We apologize, but your payment review has been rejected. Please re-upload your proof of transfer',
            self::PAID->value => 'Your payment has been successfully confirmed',
            self::CANCELED->value => 'The New Installation Application has been canceled by the Admin',
            self::VALIDATED->value => 'The New Installation Application has been successfully validated',
            self::INSTALL->value => 'The water meter installation will be carried out by an officer',
            self::FINISHED->value => 'The New Installation Service is complete. Enjoy your clean water service',
            default => '',
        };
    }
}
