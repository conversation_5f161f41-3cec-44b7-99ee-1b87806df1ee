<?php

namespace App\Models\Customer\Enum;

enum PaymentStatus: string
{
    case NEED_REVIEW = 'NEED_REVIEW';
    case APPROVED = 'APPROVED';
    case DECLINED = 'DECLINED';
    case CANCELED = 'CANCELED';

    public static function getDescription(string $value): string
    {
        return match ($value) {
            self::NEED_REVIEW->value => 'Pembayaran direview oleh Admin',
            self::APPROVED->value => 'Pembayaran diterima oleh Admin',
            self::DECLINED->value => 'Pembayaran ditolak oleh Admin',
            self::CANCELED->value => 'Pembayaran dibatalkan',
            default => '',
        };
    }
}
