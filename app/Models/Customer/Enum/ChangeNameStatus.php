<?php

namespace App\Models\Customer\Enum;

enum ChangeNameStatus: string
{
    case DRAFT = 'DRAFT';
    case PAYMENT = 'PAYMENT';
    case PAYMENT_REVIEW = 'PAYMENT_REVIEW';
    case PAYMENT_DECLINED = 'PAYMENT_DECLINED';
    case PAID = 'PAID';
    case CANCELED = 'CANCELED';
    case FINISHED = 'FINISHED';

    public static function getDescription(string $value): string
    {
        return match ($value) {
            self::DRAFT->value => 'Permohonan Layanan Berhasil Diajukan',
            self::PAYMENT->value => 'Proses Pembayaran',
            self::PAYMENT_REVIEW->value => 'Pembayaran sedang direview oleh Admin',
            self::PAYMENT_DECLINED->value => 'Pembayaran ditolak oleh Admin',
            self::PAID->value => 'Pembayaran Berhasil',
            self::CANCELED->value => 'Permohonan dibatalkan oleh Admin',
            self::FINISHED->value => 'Layanan <PERSON>',
            default => '',
        };
    }

    public static function getDescriptionEn(string $value): string
    {
        return match ($value) {
            self::DRAFT->value => 'Service Submitted',
            self::PAYMENT->value => 'Waiting for Payment',
            self::PAYMENT_REVIEW->value => 'Payment being reviewed by Admin',
            self::PAYMENT_DECLINED->value => 'Payment declined by Admin',
            self::PAID->value => 'Payment Successful',
            self::CANCELED->value => 'Payment canceled by Admin',
            self::FINISHED->value => 'Service Complete',
            default => '',
        };
    }

    public static function getNotificationMessage(string $value): string
    {
        return match ($value) {
            self::DRAFT->value => 'Pengajuan Balik Nama berhasil diajukan',
            self::PAYMENT->value => 'Silahkan melakukan pembayaran Layanan Balik Nama',
            self::PAYMENT_REVIEW->value => 'Pembayaranmu berhasil dan sedang direview Admin',
            self::PAYMENT_DECLINED->value => 'Mohon maaf, review pembayaranmu ditolak, silahkan upload ulang foto bukti transfermu',
            self::PAID->value => 'Pembayaran Balik Nama telah terkonfirmasi',
            self::CANCELED->value => 'Pengajuan Balik Nama dibatalkan',
            self::FINISHED->value => 'Layanan Balik Nama selesai',
            default => '',
        };
    }

    public static function getNotificationMessageEn(string $value): string
    {
        return match ($value) {
            self::DRAFT->value => 'Your Change Name application has been successfully submitted',
            self::PAYMENT->value => 'Please make payment to continue the Change Name process',
            self::PAYMENT_REVIEW->value => 'Your payment was successful and is being reviewed by the Admin',
            self::PAYMENT_DECLINED->value => 'We apologize, but your payment review has been rejected. Please re-upload your proof of transfer',
            self::PAID->value => 'Your payment has been successfully confirmed',
            self::CANCELED->value => 'The Change Name Application has been canceled by the Admin',
            self::FINISHED->value => 'The Change Name Service is complete. Enjoy your clean water service',
            default => '',
        };
    }
}
