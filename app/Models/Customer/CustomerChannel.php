<?php

namespace App\Models\Customer;

use App\Models\Activity\Activity;
use App\Models\Consultation\Consultation;
use App\Models\Customer\Enum\CustomerChannelStatus;
use App\Models\Customer\Enum\RegistrationStatus;
use App\Models\Traits\Filterable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class CustomerChannel extends Model
{
    use Filterable, SoftDeletes;

    protected $fillable = [
        'external_customer_id',
        'channel',
        'name',
        'address',
        'sequence_number',
        'telephone',
        'telephone_2',
        'fullname',
        'address_ktp',
        'no_ktp',
        'profession',
        'land_status',
        'land_document',
        'occupant',
        'used_for',
        'etc',
        'electrical_power',
        'building_area',
        'id_baseline',
        'no_spl',
        'id_card_photo',
        'family_card_photo',
        'house_image_registration',
        'long_registration',
        'lat_registration',
        'house_image',
        'long',
        'lat',
        'meter_number',
        'protected_plastic',
        'meter_seal',
        'meter_then',
        'meter_then_past',
        'use_then',
        'use_then_past',
        'cost',
        'fare_cost',
        'load_cost',
        'meter_image',
        'status',
        'status_registration',
        'decline_reason',
        'registration_source',
        'disconnect_date',
        'register_date',
        'install_date',
        'pay_date',
        'admit_date',
        'temporary_disconnect_date',
        'new_installation_cost',
        'tertiary_cost',
        'new_installation_category',
        'collective_name',
        'is_hankam',

        'id_region',
        'unit_code',
        'id_group',
        'id_meter_condition',
        'id_brand',
        'id_size',
        'id_transaction',
        'id_reduction',
        'id_sub_district',
        'name_sub_district',
        'id_village',
        'name_village',
        'created_by',
        'updated_by',
        'deleted_by',
        'pts',

        'region_code',
        'region_name',
        'id_service_zone',
        'zone_name',
        'zone_service_area_name',
        'unit_name',
        'group_name',
        'group_category',
        'group_description',
        'group_customer_type',
        'water_meter_brand',
        'water_meter_size',
    ];

    protected $appends = [
        'status_description',
        'status_registration_description'
    ];

    public function getStatusDescriptionAttribute()
    {
        return $this->status ? CustomerChannelStatus::getDescription($this->status) : null;
    }

    public function getStatusRegistrationDescriptionAttribute()
    {
        return $this->status_registration ? RegistrationStatus::getDescription($this->status_registration) : null;
    }

    public function getIdCardPhotoAttribute($value)
    {
        return $value ? (Str::substr($value, 0, 4) == 'http' ? $value : URL::to('storage/' . $value)) : null;
    }

    public function getFamilyCardPhotoAttribute($value)
    {
        return $value ? (Str::substr($value, 0, 4) == 'http' ? $value : URL::to('storage/' . $value)) : null;
    }

    public function getHouseImageRegistrationAttribute($value)
    {
        return $value ? (Str::substr($value, 0, 4) == 'http' ? $value : URL::to('storage/' . $value)) : null;
    }

    public function getMeterImageAttribute($value)
    {
        return $value ? (Str::substr($value, 0, 4) == 'http' ? $value : URL::to('storage/' . $value)) : null;
    }

    public function customers()
    {
        return $this->belongsToMany(Customer::class, 'customer_customer_channel', 'customer_channel_id', 'customer_id')
            ->withTimestamps();
    }

    public function activity()
    {
        return $this->morphMany(Activity::class, 'subject');
    }

    public function changeName()
    {
        return $this->hasOne(CustomerChannelChange::class, 'external_customer_id', 'external_customer_id');
    }

    public function consultations()
    {
        return $this->hasMany(Consultation::class, 'external_customer_id', 'external_customer_id');
    }
}
