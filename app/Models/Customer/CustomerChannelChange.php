<?php

namespace App\Models\Customer;

use App\Models\Activity\Activity;
use App\Models\Customer\Enum\ChangeNameStatus;
use App\Models\Customer\Enum\PaymentStatus;
use App\Models\Traits\Filterable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerChannelChange extends Model
{
    use SoftDeletes, Filterable;

    protected $fillable = [
        'customer_id',
        'external_customer_id',
        'id_history_name',
        'id_transaction',
        'old_name',
        'new_name',
        'reason',
        'description',
        'cost',
        'payment_photo',
        'payment_status',
        'current_status'
    ];

    public $timestamps = true;

    protected $casts = [
        'submitted_at' => 'datetime',
    ];

    protected $appends = [
        'payment_status_description',
        'current_status_description',
    ];

    public function getPaymentPhotoAttribute($value)
    {
        return $value ? url('storage/' . $value) : null;
    }

    public function getPaymentStatusDescriptionAttribute()
    {
        return $this->payment_status ? PaymentStatus::getDescription($this->payment_status) : null;
    }

    public function getCurrentStatusDescriptionAttribute()
    {
        return $this->current_status ? ChangeNameStatus::getDescription($this->current_status) : null;
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function customerChannel()
    {
        return $this->belongsTo(CustomerChannel::class, 'external_customer_id', 'external_customer_id');
    }

    public function activity()
    {
        return $this->morphOne(Activity::class, 'subject');
    }
}
