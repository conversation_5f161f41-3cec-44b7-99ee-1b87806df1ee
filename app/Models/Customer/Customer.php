<?php

namespace App\Models\Customer;

use App\Models\Customer\Enum\VerificationStatus;
use App\Models\Location\District;
use App\Models\Location\Province;
use App\Models\Location\Regency;
use App\Models\Location\Village;
use App\Models\Traits\Filterable;
use App\Models\User\Enum\Gender;
use Carbon\Carbon;
use Illuminate\Container\Attributes\Auth;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Laravel\Passport\HasApiTokens;

class Customer extends User
{
    use HasFactory, HasApiTokens, Notifiable, Filterable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'gender',
        'birth_date',
        'profession',
        'no_ktp',
        'id_card_photo',
        'verification_status',
        'verified_at',
        'verified_by',
        'is_active',
        'address',
        'avatar',
        'last_login',
        'last_login_ip',
        'last_login_useragent',
        'last_login_url',
        'settings',
        'player_ids',
        'province_id',
        'regency_id',
        'district_id',
        'village_id',
        'point'
    ];

    protected $appends = [
        'gender_description',
        'is_active_description',
        'verification_status_description',
        'age',
        'is_profile_complete',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'player_ids' => 'array',
    ];

    protected $statuses = array(
        0 => 'Inactive',
        1 => 'Active'
    );

    public function getIsActiveDescriptionAttribute()
    {
        return $this->statuses[$this->is_active] ?? 'Unknown';
    }

    public function getAvatarAttribute($value)
    {
        return $value ? (Str::substr($value, 0, 4) == 'http' ? $value : URL::to('storage/' . $value))
            : null;
    }

    public function getIdCardPhotoAttribute($value)
    {
        return $value ? (Str::substr($value, 0, 4) == 'http' ? $value : URL::to('storage/' . $value))
            : null;
    }

    public function getGenderDescriptionAttribute()
    {
        return Gender::getDescription($this->gender);
    }

    public function getVerificationStatusAttribute()
    {
        return $this->attributes['verification_status'] ?? 'UNVERIFIED';
    }

    public function getVerificationStatusDescriptionAttribute()
    {
        return VerificationStatus::getDescription($this->verification_status);
    }

    public function getAgeAttribute()
    {
        if (!$this->birth_date) {
            return null;
        }
        $birthDate = Carbon::parse($this->birth_date);
        return $birthDate->age;
    }

    public function getIsProfileCompleteAttribute()
    {
        return !empty($this->name) &&
            !empty($this->birth_date) &&
            !empty($this->profession) &&
            !empty($this->phone) &&
            !empty($this->address) &&
            !empty($this->province_id) &&
            !empty($this->regency_id) &&
            !empty($this->district_id) &&
            !empty($this->village_id) &&
            !empty($this->no_ktp) &&
            !empty($this->id_card_photo) &&
            $this->verification_status == VerificationStatus::VERIFIED->value;
    }

    public function province()
    {
        return $this->belongsTo(Province::class);
    }

    public function regency()
    {
        return $this->belongsTo(Regency::class);
    }

    public function district()
    {
        return $this->belongsTo(District::class);
    }

    public function village()
    {
        return $this->belongsTo(Village::class);
    }

    public function channels()
    {
        return $this->belongsToMany(CustomerChannel::class, 'customer_customer_channel', 'customer_id', 'customer_channel_id')
            ->orderBy('customer_channels.external_customer_id', 'asc')
            ->withTimestamps();
    }

    public function verificator()
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    public function pointHistories()
    {
        return $this->hasMany(PointHistory::class, 'customer_id');
    }
}
