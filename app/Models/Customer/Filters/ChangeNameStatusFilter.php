<?php

namespace App\Models\Customer\Filters;

use App\Libraries\Filters\DropdownFilter;
use App\Models\Customer\Enum\ChangeNameStatus;
use Illuminate\Database\Eloquent\Builder;

class ChangeNameStatusFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'verification_status';
    protected $label    = 'Status Verifikasi';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.verification_status';

    /**
     * @return array
     */
    protected function options(): array
    {
        $options = [
            ChangeNameStatus::DRAFT->value => ChangeNameStatus::getDescription(ChangeNameStatus::DRAFT->value),
            ChangeNameStatus::PAYMENT->value => ChangeNameStatus::getDescription(ChangeNameStatus::PAYMENT->value),
            ChangeNameStatus::PAYMENT_REVIEW->value => ChangeNameStatus::getDescription(ChangeNameStatus::PAYMENT_REVIEW->value),
            ChangeNameStatus::PAYMENT_DECLINED->value => ChangeNameStatus::getDescription(ChangeNameStatus::PAYMENT_DECLINED->value),
            ChangeNameStatus::PAID->value => ChangeNameStatus::getDescription(ChangeNameStatus::PAID->value),
            ChangeNameStatus::CANCELED->value => ChangeNameStatus::getDescription(ChangeNameStatus::CANCELED->value),
            ChangeNameStatus::FINISHED->value => ChangeNameStatus::getDescription(ChangeNameStatus::FINISHED->value),
        ];

        return $options;
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        if ($value != '' && $value != null) {
            return $query->where('current_status', $value);
        }
        return $query;
    }
}
