<?php

namespace App\Models\Customer\Filters;

use App\Libraries\Filters\DropdownFilter;
use App\Models\Customer\Enum\VerificationStatus;
use Illuminate\Database\Eloquent\Builder;

class VerificationStatusFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'verification_status';
    protected $label    = 'Status Verifikasi';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.verification_status';

    /**
     * @return array
     */
    protected function options(): array
    {
        $options = [
            VerificationStatus::UNVERIFIED->value => VerificationStatus::getDescription(VerificationStatus::UNVERIFIED->value),
            VerificationStatus::WAITING->value => VerificationStatus::getDescription(VerificationStatus::WAITING->value),
            VerificationStatus::VERIFIED->value => VerificationStatus::getDescription(VerificationStatus::VERIFIED->value),
            VerificationStatus::REJECTED->value => VerificationStatus::getDescription(VerificationStatus::REJECTED->value),
        ];

        return $options;
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        if ($value != '' && $value != null) {
            return $query->where('verification_status', $value);
        }
        return $query;
    }
}
