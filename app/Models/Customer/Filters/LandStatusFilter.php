<?php

namespace App\Models\Customer\Filters;

use App\Libraries\Filters\DropdownFilter;
use App\Libraries\SimbioApi\LandDocStatus\GetLandStatusApi;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use App\Models\Customer\CustomerChannel;
use Illuminate\Support\Facades\Log;

class LandStatusFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'land_status';
    protected $label    = 'Status Tanah';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.land_status';

    /**
     * @return array
     */
    protected function options(): array
    {
        $landStatuses = [];
        try {
            $landStatuses = new GetLandStatusApi();
            $landStatuses = $landStatuses->run();
        } catch (\Exception $e) {
            Log::error('[LandStatusFilter] Error fetching land status', [
                'error' => $e->getMessage(),
            ]);
        }

        $options = [];
        foreach ($landStatuses as $key => $value) {
            $options[$value['name']] = $value['name'];
        }

        return $options;
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        return $query;
    }
}
