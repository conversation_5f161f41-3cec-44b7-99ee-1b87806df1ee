<?php

namespace App\Models\Customer\Filters;

use App\Libraries\Filters\DropdownFilter;
use App\Models\Customer\Enum\RegistrationStatus;
use Illuminate\Database\Eloquent\Builder;

class RegistrationStatusFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'status_registration';
    protected $label    = 'Status Registrasi';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.status_registration';

    /**
     * @return array
     */
    protected function options(): array
    {
        $options = [
            RegistrationStatus::NEED_REVIEW->value => RegistrationStatus::getDescription(RegistrationStatus::NEED_REVIEW->value),
            RegistrationStatus::PAYMENT->value => RegistrationStatus::getDescription(RegistrationStatus::PAYMENT->value),
            RegistrationStatus::PAYMENT_REVIEW->value => RegistrationStatus::getDescription(RegistrationStatus::PAYMENT_REVIEW->value),
            RegistrationStatus::PAYMENT_DECLINED->value => RegistrationStatus::getDescription(RegistrationStatus::PAYMENT_DECLINED->value),
            RegistrationStatus::PAID->value => RegistrationStatus::getDescription(RegistrationStatus::PAID->value),
            RegistrationStatus::CANCELED->value => RegistrationStatus::getDescription(RegistrationStatus::CANCELED->value),
            RegistrationStatus::FINISHED->value => RegistrationStatus::getDescription(RegistrationStatus::FINISHED->value),
        ];

        return $options;
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        return $query;
    }
}
