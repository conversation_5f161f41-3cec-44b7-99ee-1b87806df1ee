<?php

namespace App\Models\Informations\Enum;

enum NewsCategory: string
{
    const NEWS      = "news";
    const ACTIVITY  = "activity";

    public static function getDescription($value): string
    {
        $result = '';
        if ($value === self::NEWS) {
            $result = 'Berita';
        } elseif ($value === self::ACTIVITY) {
            $result = 'Kegiatan';
        }
        return $result;
    }

    public static function getValues(): array
    {
        return [
            self::NEWS,
            self::ACTIVITY,
        ];
    }
}
