<?php

namespace App\Models\Informations;

use Illuminate\Database\Eloquent\Model;

class EduImage extends Model
{
    protected $fillable = [
        'edu_id',
        'image',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    public function edu()
    {
        return $this->belongsTo(Edu::class, 'edu_id');
    }

    public function getImageAttribute($value)
    {
        return $value ? url('storage/' . $value) : null;
    }
}
