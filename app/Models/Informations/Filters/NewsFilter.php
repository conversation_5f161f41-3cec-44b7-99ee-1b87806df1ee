<?php

namespace App\Models\Informations\Filters;

use App\Libraries\Filters\DropdownFilter;
use App\Models\Room\Room;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Informations\Enum\NewsCategory;
class NewsFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'category';
    protected $label    = 'Category';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.category';

    /**
     * @return array
     */
    protected function options(): array
    {
        $data = NewsCategory::getValues();

        $options = [];
        foreach ($data as $category) {
            $options[$category] = NewsCategory::getDescription($category);
        }

        return $options;
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        if ($value != '' && $value != null) {
            return $query->where('category', $value);
        }
        return $query;
    }
}
