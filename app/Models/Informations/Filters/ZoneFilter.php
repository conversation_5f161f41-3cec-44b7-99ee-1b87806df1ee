<?php

namespace App\Models\Informations\Filters;

use App\Libraries\Filters\DropdownFilter;
use Illuminate\Database\Eloquent\Builder;

class ZoneFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'zone';
    protected $label    = 'Zona Wilayah';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.zone';

    /**
     * @return array
     */
    protected function options(): array
    {
        return [];
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        if ($value) {
            return $query->where('zone_id', $value);
        }
        return $query;
    }
}
