<?php

namespace App\Models\Informations\Filters;

use App\Libraries\Filters\DropdownFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Informations\Enum\NewsCategory;

class StatusFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'status';
    protected $label    = 'Status';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.status';

    /**
     * @return array
     */
    protected function options(): array
    {
        $options = [
            0 => 'Inactive',
            1 => 'Active'
        ];

        return $options;
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        if ($value != '' && $value != null) {
            return $query->where('is_active', $value);
        }
        return $query;
    }
}
