<?php

namespace App\Models\Informations\Filters;

use App\Libraries\Filters\DropdownFilterDate;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class DateFilter extends DropdownFilterDate
{
    /**
     * @var string
     */
    protected $name     = 'date';
    protected $label    = 'Tanggal Publish';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.date';

    public function applyFilter(Builder $query, $value): Builder
    {
        $start = Carbon::parse($value[0]);
        $end = Carbon::parse($value[1]);
        if ($value[0] && $value[1]) {
            return $query->whereBetween('date', [$start->format('Y-m-d'), $end->format('Y-m-d')]);;
        }
        return $query;
    }
}
