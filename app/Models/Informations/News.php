<?php

namespace App\Models\Informations;

use App\Models\Informations\Enum\NewsCategory;
use App\Models\Traits\Filterable;
use Illuminate\Container\Attributes\Auth;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\SoftDeletes;

class News extends User
{
    use HasFactory, Filterable, SoftDeletes;

    protected $fillable = [
        'title',
        'content',
        'category',
        'image',
        'image_banner',
        'slug',
        'date',
        'is_active',
        'created_by',
        'updated_by',
    ];
    protected $appends = [
        'is_active_description',
        'category_description',
    ];

    protected $hidden = [
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected $statuses = [
        0 => 'Inactive',
        1 => 'Active',
    ];

    public function getIsActiveDescriptionAttribute()
    {
        return $this->statuses[$this->is_active] ?? 'Unknown';
    }

    public function getCategoryDescriptionAttribute()
    {
        return NewsCategory::getDescription($this->category);
    }

    public function getImageAttribute($value)
    {
        return $value ? URL::to('storage/' . $value) : null;
    }

    public function getImageBannerAttribute($value)
    {
        return $value ? URL::to('storage/' . $value) : null;
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

}
