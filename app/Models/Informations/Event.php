<?php

namespace App\Models\Informations;

use App\Models\Traits\Filterable;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\URL;

class Event extends Model
{
    use Filterable, SoftDeletes;

    protected $fillable = [
        'title',
        'content',
        'date',
        'image',
        'slug',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $appends = [
        'is_active_description',
    ];

    protected $hidden = [
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected $statuses = [
        0 => 'Inactive',
        1 => 'Active',
    ];

    public function getIsActiveDescriptionAttribute()
    {
        return $this->statuses[$this->is_active] ?? 'Unknown';
    }

    public function getImageAttribute($value)
    {
        return $value ? URL::to('storage/' . $value) : null;
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
