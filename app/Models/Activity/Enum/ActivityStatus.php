<?php

namespace App\Models\Activity\Enum;

enum ActivityStatus: string
{
    case WAITING           = "WAITING";
    case PROGRESS          = "PROGRESS";
    case CANCELED_BY_CUSTOMER = "CANCELED_BY_CUSTOMER";
    case CANCELED_BY_ADMIN = "CANCELED_BY_ADMIN";
    case DONE              = "DONE";

    case DRAFT              = 'DRAFT';
    case PAYMENT            = 'PAYMENT';
    case PAYMENT_REVIEW     = 'PAYMENT_REVIEW';
    case PAYMENT_DECLINED   = 'PAYMENT_DECLINED';
    case PAID               = 'PAID';
    case CANCELED           = 'CANCELED';
    case FINISHED           = 'FINISHED';

    case NEED_REVIEW        = 'NEED_REVIEW';
    case RAB_RECEIVED       = 'RAB_RECEIVED';
    case TERTIARY           = 'TERTIARY';
    case VALIDATED          = 'VALIDATED';
    case INSTALL            = 'INSTALL';

    public static function getDescription($value): string
    {
        return match ($value) {
            self::WAITING->value => 'Menunggu Konfirmasi',
            self::PROGRESS->value => 'Sedang Diproses',
            self::CANCELED_BY_CUSTOMER->value => 'Dibatalkan oleh Pelanggan',
            self::CANCELED_BY_ADMIN->value => 'Dibatalkan oleh Admin',
            self::DONE->value => 'Selesai',

            self::DRAFT->value => 'Permohonan Layanan Berhasil Diajukan',
            self::PAYMENT->value => 'Proses Pembayaran',
            self::PAYMENT_REVIEW->value => 'Pembayaran sedang direview oleh Admin',
            self::PAYMENT_DECLINED->value => 'Pembayaran ditolak oleh Admin',
            self::PAID->value => 'Pembayaran Berhasil',
            self::CANCELED->value => 'Permohonan dibatalkan oleh Admin',
            self::FINISHED->value => 'Layanan Selesai',

            self::NEED_REVIEW->value => 'Menunggu Tinjauan',
            self::RAB_RECEIVED->value => 'RAB Sudah Terbit',
            self::TERTIARY->value => 'Tersier',
            self::VALIDATED->value => 'Tervalidasi',
            self::INSTALL->value => 'Pemasangan',

            default => '',
        };
    }

    public static function getOptions(): array
    {
        return [
            [
                'value' => self::WAITING->value,
                'label' => self::getDescription(self::WAITING->value)
            ],
            [
                'value' => self::PROGRESS->value,
                'label' => self::getDescription(self::PROGRESS->value)
            ],
            [
                'value' => self::DRAFT->value,
                'label' => self::getDescription(self::DRAFT->value)
            ],
            [
                'value' => self::PAYMENT->value,
                'label' => self::getDescription(self::PAYMENT->value)
            ],
            [
                'value' => self::PAYMENT_REVIEW->value,
                'label' => self::getDescription(self::PAYMENT_REVIEW->value)
            ],
            [
                'value' => self::PAYMENT_DECLINED->value,
                'label' => self::getDescription(self::PAYMENT_DECLINED->value)
            ],
            [
                'value' => self::NEED_REVIEW->value,
                'label' => self::getDescription(self::NEED_REVIEW->value)
            ],
            [
                'value' => self::RAB_RECEIVED->value,
                'label' => self::getDescription(self::RAB_RECEIVED->value)
            ],
            [
                'value' => self::TERTIARY->value,
                'label' => self::getDescription(self::TERTIARY->value)
            ],
            [
                'value' => self::VALIDATED->value,
                'label' => self::getDescription(self::VALIDATED->value)
            ],
            [
                'value' => self::INSTALL->value,
                'label' => self::getDescription(self::INSTALL->value)
            ],
            [
                'value' => self::PAID->value,
                'label' => self::getDescription(self::PAID->value)
            ],
            [
                'value' => self::CANCELED_BY_CUSTOMER->value,
                'label' => self::getDescription(self::CANCELED_BY_CUSTOMER->value)
            ],
            [
                'value' => self::CANCELED_BY_ADMIN->value,
                'label' => self::getDescription(self::CANCELED_BY_ADMIN->value)
            ],
            [
                'value' => self::DONE->value,
                'label' => self::getDescription(self::DONE->value)
            ],
            [
                'value' => self::CANCELED->value,
                'label' => self::getDescription(self::CANCELED->value)
            ],
            [
                'value' => self::FINISHED->value,
                'label' => self::getDescription(self::FINISHED->value)
            ],
        ];
    }
}
