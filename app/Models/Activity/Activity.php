<?php

namespace App\Models\Activity;

use App\Models\Activity\Enum\ActivityStatus;
use App\Models\Customer\Customer;
use App\Models\Traits\Filterable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Activity extends Model
{
    use SoftDeletes, Filterable;

    protected $fillable = [
        'subject_type',
        'subject_id',
        'customer_id',
        'code',
        'title',
        'category',
        'description',
        'status',
    ];

    protected $appends = [
        'subject_type_name',
        'status_description',
    ];

    public function getStatusDescriptionAttribute()
    {
        return ActivityStatus::getDescription($this->status);
    }

    public function getSubjectTypeNameAttribute()
    {
        return class_basename($this->subject_type);
    }

    /**
     * Polymorphic relationship to the subject (e.g. Consultation, CustomerChannel).
     */
    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function timelines()
    {
        return $this->hasMany(ActivityTimeline::class, 'activity_id')->orderBy('created_at', 'desc');
    }
}
