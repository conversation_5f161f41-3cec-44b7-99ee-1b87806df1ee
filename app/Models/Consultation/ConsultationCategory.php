<?php

namespace App\Models\Consultation;

use App\Models\Traits\Filterable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ConsultationCategory extends Model
{
    use SoftDeletes, Filterable;

    protected $fillable = [
        'name',
        'is_active',
    ];

    protected $appends = [
        'is_active_description',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected $statuses = [
        0 => 'Inactive',
        1 => 'Active',
    ];

    public function getIsActiveDescriptionAttribute()
    {
        return $this->statuses[$this->is_active] ?? 'Unknown';
    }

    public function consultations()
    {
        return $this->hasMany(Consultation::class, 'consultation_category_id');
    }
}
