<?php

namespace App\Models\Consultation\Filters;

use App\Libraries\Filters\DropdownFilter;
use App\Models\Consultation\Enum\ConsultationStatus;
use Illuminate\Database\Eloquent\Builder;

class ConsultationStatusFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'status';
    protected $label    = 'Status';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.status';

    /**
     * @return array
     */
    protected function options(): array
    {
        $options = [
            ConsultationStatus::WAITING->value => ConsultationStatus::getDescription(ConsultationStatus::WAITING->value),
            ConsultationStatus::PROGRESS->value => ConsultationStatus::getDescription(ConsultationStatus::PROGRESS->value),
            ConsultationStatus::CANCELED_BY_CUSTOMER->value => ConsultationStatus::getDescription(ConsultationStatus::CANCELED_BY_CUSTOMER->value),
            ConsultationStatus::CANCELED_BY_ADMIN->value => ConsultationStatus::getDescription(ConsultationStatus::CANCELED_BY_ADMIN->value),
            ConsultationStatus::DONE->value => ConsultationStatus::getDescription(ConsultationStatus::DONE->value),
        ];

        return $options;
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        if ($value != '' && $value != null) {
            return $query->where('consultations.status', $value);
        }
        return $query;
    }
}
