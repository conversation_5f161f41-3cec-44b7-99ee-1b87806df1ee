<?php

namespace App\Models\Consultation\Filters;

use App\Libraries\Filters\DropdownFilter;
use Illuminate\Database\Eloquent\Builder;

class ConsultationCategoryFilter extends DropdownFilter
{
    /**
     * @var string
     */
    protected $name     = 'category';
    protected $label    = 'Category';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.category';

    /**
     * @return array
     */
    protected function options(): array
    {
        return [];
    }

    /**
     * @param Builder $builder
     * @param $value
     * @return Builder
     */
    public function applyFilter(Builder $query, $value): Builder
    {
        if ($value) {
            return $query->where('consultations.consultation_category_id', $value);
        }
        return $query;
    }
}
