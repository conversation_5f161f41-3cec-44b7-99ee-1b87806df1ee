<?php

namespace App\Models\Consultation\Filters;

use App\Libraries\Filters\DropdownFilterDate;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class CreatedFilter extends DropdownFilterDate
{
    /**
     * @var string
     */
    protected $name     = 'created_at';
    protected $label    = 'Tanggal Diajukan';

    /**
     * @var string
     */
    protected $vModel = 'query.filters.created_at';

    public function applyFilter(Builder $query, $value): Builder
    {
        $start = Carbon::parse($value[0]);
        $end = Carbon::parse($value[1]);
        if ($value[0] && $value[1]) {
            return $query->whereBetween('consultations.created_at', [$start->startOfDay(), $end->endOfDay()]);
        }
        return $query;
    }
}
