<?php

namespace App\Models\Consultation\Enum;

enum ConsultationStatus: string
{
    case WAITING           = "WAITING";
    case PROGRESS          = "PROGRESS";
    case CANCELED_BY_CUSTOMER = "CANCELED_BY_CUSTOMER";
    case CANCELED_BY_ADMIN = "CANCELED_BY_ADMIN";
    case DONE              = "DONE";

    public static function getDescription($value): string
    {
        return match ($value) {
            self::WAITING->value => 'Menunggu Konfirmasi',
            self::PROGRESS->value => 'Sedang Diproses',
            self::CANCELED_BY_CUSTOMER->value => 'Dibatalkan oleh Pelanggan',
            self::CANCELED_BY_ADMIN->value => 'Dibatalkan oleh Admin',
            self::DONE->value => 'Selesai',
            default => '',
        };
    }
}
