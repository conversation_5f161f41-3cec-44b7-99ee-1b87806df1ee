<?php

namespace App\Models\Consultation;

use App\Models\Activity\Activity;
use App\Models\Consultation\Enum\ConsultationStatus;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerChannel;
use App\Models\Traits\Filterable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Consultation extends Model
{
    use SoftDeletes, Filterable;

    protected $fillable = [
        'customer_id',
        'external_customer_id',
        'consultation_category_id',
        'code',
        'title',
        'description',
        'consultation_time',
        'approved_consultation_time',
        'status',
        'cancel_reason',
        'media',
        'note',
    ];

    protected $casts = [
        'consultation_time' => 'datetime',
        'approved_consultation_time' => 'datetime',
    ];

    protected $appends = [
        'status_description',
    ];

    public function getStatusDescriptionAttribute()
    {
        return ConsultationStatus::getDescription($this->status);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function customerChannel()
    {
        return $this->belongsTo(CustomerChannel::class, 'external_customer_id', 'external_customer_id');
    }

    public function category()
    {
        return $this->belongsTo(ConsultationCategory::class, 'consultation_category_id');
    }

    public function activity()
    {
        return $this->morphOne(Activity::class, 'subject');
    }
}
