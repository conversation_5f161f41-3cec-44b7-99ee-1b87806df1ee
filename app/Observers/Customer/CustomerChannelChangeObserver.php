<?php

namespace App\Observers\Customer;

use App\Actions\Notification\CustomerChannelChangeNotificationAction;
use App\Models\Activity\Activity;
use App\Models\Activity\Enum\ActivityStatus;
use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Enum\ChangeNameStatus;
use App\Models\Customer\Enum\PaymentStatus;
use Illuminate\Support\Facades\Log;

class CustomerChannelChangeObserver
{
    public function updated($data)
    {
        if ($data->wasChanged('payment_status') || $data->wasChanged('current_status')) {
            Log::info('[CustomerChannelChangeObserver] Status Changed', [
                'id' => $data->id,
                'old_payment_status' => $data->getOriginal('payment_status'),
                'new_payment_status' => $data->payment_status,
                'old_current_status' => $data->getOriginal('current_status'),
                'new_current_status' => $data->current_status
            ]);

            CustomerChannelChangeNotificationAction::run($data);

            try {
                $activity = Activity::where('subject_type', CustomerChannelChange::class)
                    ->where('subject_id', $data->id)
                    ->first();

                if ($activity) {
                    $activityStatus = $data->current_status;

                    if ($activityStatus === $activity->status) {
                        Log::info('[CustomerChannelChangeObserver] Activity status already synchronized, no need to update', [
                            'activityId' => $activity->id,
                            'activityStatus' => $activity->status,
                            'changeNameStatus' => $data->current_status,
                        ]);
                        return;
                    }

                    $activity->status = $activityStatus;
                    $activity->save();

                    $activity->timelines()->create([
                        'status'        => $activityStatus,
                        'description'   => ChangeNameStatus::getNotificationMessage($data->current_status)
                    ]);

                    Log::info('[CustomerChannelChangeObserver] updateStatus - Activity updated', [
                        'activityId' => $activity->id,
                        'activityStatus' => $activity->status,
                        'description'   => ChangeNameStatus::getNotificationMessage($data->current_status)
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('[CustomerChannelChangeObserver] Error updating activity', [
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
