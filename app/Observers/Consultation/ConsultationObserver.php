<?php

namespace App\Observers\Consultation;

use App\Actions\Notification\ConstulationNotificationAction;
use App\Events\Consultation\ConsultationUpdatedEvent;
use App\Models\Activity\Activity;
use App\Models\Activity\Enum\ActivityStatus;
use App\Models\Consultation\Consultation;
use App\Models\Consultation\Enum\ConsultationStatus;
use Illuminate\Support\Facades\Log;

class ConsultationObserver
{
    public function created($data)
    {
        Log::info('[ConsultationObserver] Created', ['id' => $data->id, 'status' => $data->status]);

        try {
            $activity = new Activity();
            $activity->subject()->associate($data);
            $activity->code = $data->code;
            $activity->title = 'Permohonan <PERSON> Konsult<PERSON>';
            $activity->category = 'Layanan Konsultasi';
            $activity->description = 'Layanan Konsultasi';
            $activity->status = ActivityStatus::WAITING->value;
            $activity->customer_id = auth('api')->user()->id;
            $activity->save();

            $activity->timelines()->create([
                'status'        => ActivityStatus::WAITING->value,
                'description'   => 'Permohonan layanan konsultasi berhasil diajukan'
            ]);

            ConstulationNotificationAction::run($data);

            broadcast(new ConsultationUpdatedEvent())->toOthers();

            Log::info('[ConsultationObserver] Activity created', [
                'activityId' => $activity->id,
                'activityStatus' => $activity->status,
            ]);
        } catch (\Exception $e) {
            Log::error('[ConsultationObserver] Error creating activity', [
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function deleted($data)
    {
        Log::info('[ConsultationObserver] Deleted', ['id' => $data->id]);
        broadcast(new ConsultationUpdatedEvent())->toOthers();
    }

    public function updated($data)
    {
        if ($data->wasChanged('status')) {
            Log::info('[ConsultationObserver] Status Changed', [
                'id' => $data->id,
                'old_status' => $data->getOriginal('status'),
                'new_status' => $data->status
            ]);

            try {
                $activity = Activity::where('subject_type', Consultation::class)
                    ->where('subject_id', $data->id)
                    ->first();
                if ($activity) {
                    $activityStatus = $data->status;

                    if ($activityStatus === $activity->status) {
                        Log::info('[ConsultationObserver] Activity status already synchronized, no need to update', [
                            'activityId' => $activity->id,
                            'activityStatus' => $activityStatus,
                        ]);
                    } else {
                        $activity->status = $activityStatus;
                        $activity->save();

                        $activity->timelines()->create([
                            'status'        => $activityStatus,
                            'description'   => 'Permohonan layanan konsultasi ' . ConsultationStatus::getDescription($data->status)
                        ]);
                    }
                }

                ConstulationNotificationAction::run($data);
                broadcast(new ConsultationUpdatedEvent())->toOthers();
            } catch (\Exception $e) {
                Log::error('[ConsultationObserver] Error updating activity', [
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
