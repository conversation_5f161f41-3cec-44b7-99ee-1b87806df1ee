<?php

namespace App\Mail;

use App\Models\Informations\ServiceImprovement;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ServiceImprovementBulkMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    private $serviceImprovement;
    private $emailSubject;
    private $message;
    private $date;

    public function __construct(ServiceImprovement $serviceImprovement)
    {
        $this->serviceImprovement = $serviceImprovement;
        $this->emailSubject = 'Penyempurnaan Layanan';
        $this->date = Carbon::parse($serviceImprovement->date)->locale('id')->isoFormat('D MMMM Y');
        $this->message = 'Kami melakukan penyempurnaan layanan di wilayah kamu pada tanggal ' . $this->date;
    }

    public function build()
    {
        return $this->subject($this->emailSubject)
            ->view('emails.service-improvement.notification', [
                'serviceImprovement' => $this->serviceImprovement,
                'subject' => $this->emailSubject,
                'msg' => $this->message,
            ]);
    }
}
