<?php

namespace App\Libraries\SimbioApi\ChangeName;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetCustomerChangeNamePaymentStatusApi extends SimbioApi
{
    public function run($idTransaction): array
    {
        $url = $this->baseUrl . '/customerChangeName/getPaymentStatus/' . $idTransaction;

        Log::info('[GetCustomerChangeNamePaymentStatusApi] GET payment status change name to Simbio API', [
            'url' => $url,
            'id_transaction' => $idTransaction,
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->get($url);

            Log::info('[GetCustomerChangeNamePaymentStatusApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to GET payment status change name to Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetCustomerChangeNamePaymentStatusApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
