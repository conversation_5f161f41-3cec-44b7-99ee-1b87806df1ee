<?php

namespace App\Libraries\SimbioApi\ChangeName;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CustomerChangeNamePaymentApi extends SimbioApi
{
    public function run(Request $request): array
    {
        $url = $this->baseUrl . '/customerChangeNamePayment';

        Log::info('[CustomerChangeNamePaymentApi] POST change name payment to Simbio API', [
            'url'       => $url
        ]);

        try {
            $multipartData = [
                [
                    'name' => 'id_customer',
                    'contents' => $request->input('external_customer_id')
                ],
                [
                    'name' => 'id_transaction',
                    'contents' => $request->input('id_transaction')
                ]
            ];

            if ($request->hasFile('payment_photo')) {
                $file = $request->file('payment_photo');
                $multipartData[] = [
                    'name' => 'payment_photo',
                    'contents' => fopen($file->getPathname(), 'r'),
                    'filename' => $file->getClientOriginalName(),
                    'headers' => [
                        'Content-Type' => $file->getMimeType()
                    ]
                ];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->attach($multipartData)
                ->post($url);

            Log::info('[CustomerChangeNamePaymentApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to POST change name payment to Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[CustomerChangeNamePaymentApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
