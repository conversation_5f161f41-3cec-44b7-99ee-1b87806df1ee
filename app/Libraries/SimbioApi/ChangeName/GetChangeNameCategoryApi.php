<?php

namespace App\Libraries\SimbioApi\ChangeName;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetChangeNameCategoryApi extends SimbioApi
{
    public function run(): array
    {
        $cacheKey = 'simbio_change_name_category';

        if (Cache::has($cacheKey)) {
            Log::info('[GetChangeNameCategoryApi] Returning cached change name category data', [
                'cacheKey' => $cacheKey,
            ]);
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . '/getChangeNameCategory';

        Log::info('[GetChangeNameCategoryApi] Fetching change name category from Simbio API', [
            'url'       => $url
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->get($url);


            Log::info('[GetChangeNameCategoryApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    if (count($data['data']) > 0) {
                        Cache::put($cacheKey, $data['data'], now()->addMinutes(5));
                    }
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to retrieve change name category from Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetChangeNameCategoryApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
