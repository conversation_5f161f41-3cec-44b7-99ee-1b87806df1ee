<?php

namespace App\Libraries\SimbioApi\ChangeName;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetCustomerChangeNameTrackingApi extends SimbioApi
{
    public function run($externalCustomerId): array
    {
        $cacheKey = 'simbio_change_name_history_' . $externalCustomerId;

        if (Cache::has($cacheKey)) {
            Log::info('[GetCustomerChangeNameTrackingApi] Returning cached change name history data', [
                'cacheKey' => $cacheKey,
            ]);
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . '/customerChangeName/viewTracking/' . $externalCustomerId;

        Log::info('[GetCustomerChangeNameTrackingApi] GET tracking change name to Simbio API', [
            'url'       => $url
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->get($url);

            Log::info('[GetCustomerChangeNameTrackingApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to GET tracking change name to Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetCustomerChangeNameTrackingApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
