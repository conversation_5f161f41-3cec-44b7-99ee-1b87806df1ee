<?php

namespace App\Libraries\SimbioApi\ChangeName;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CustomerChangeNameApi extends SimbioApi
{
    public function run(Request $request): array
    {
        $url = $this->baseUrl . '/customerChangeName';

        Log::info('[CustomerChangeNameApi] POST change name to Simbio API', [
            'url'       => $url,
            'request'   => $request->all(),
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->withBody(json_encode($request->all()), 'application/json')
                ->post($url);

            Log::info('[CustomerChangeNameApi] Response from Simbio API', [
                'url' => $url,
                'request' => $request->all(),
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to POST change name to Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[CustomerChangeNameApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
