<?php

namespace App\Libraries\SimbioApi\Village;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetSubDistrictVillageApi extends SimbioApi
{
    public function run(): array
    {
        $cacheKey = 'simbio_subdistrict_village';
        if (Cache::has($cacheKey)) {
            Log::info('[GetSubDistrictVillageApi] Returning cached subdistrict-village data', [
                'cacheKey' => $cacheKey,
            ]);
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . '/getSubDistrictWithVillage';

        Log::info('[GetSubDistrictVillageApi] Fetching subdisctrict-village from Simbio API', [
            'url' => $url,
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
            ])->get($url);

            Log::info('[GetSubDistrictVillageApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    if (count($data['data']) > 0) {
                        Cache::put($cacheKey, $data['data'], now()->addMinutes(5));
                    }
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to retrieve subdisctrict-village from Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetSubDistrictVillageApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
