<?php

namespace App\Libraries\SimbioApi\LandDocStatus;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetDocumentStatusApi extends SimbioApi
{
    public function run(): array
    {
        $cacheKey = 'simbio_document_status';
        if (Cache::has($cacheKey)) {
            Log::info('[GetDocumentStatusApi] Returning cached document status data', [
                'cacheKey' => $cacheKey,
            ]);
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . '/getLandDocument';

        Log::info('[GetDocumentStatusApi] GET document status from Simbio API', [
            'url' => $url
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->get($url);

            Log::info('[GetDocumentStatusApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    if (count($data['data']) > 0) {
                        Cache::put($cacheKey, $data['data'], now()->addMinutes(5));
                    }
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to GET document status from Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetDocumentStatusApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
