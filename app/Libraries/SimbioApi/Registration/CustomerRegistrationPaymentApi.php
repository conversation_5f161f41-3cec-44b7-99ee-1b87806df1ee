<?php

namespace App\Libraries\SimbioApi\Registration;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CustomerRegistrationPaymentApi extends SimbioApi
{
    public function run(Request $request): array
    {
        $url = $this->baseUrl . '/customerRegistrationPayment';

        Log::info('[CustomerRegistrationPaymentApi] POST registration payment to Simbio API', [
            'url'       => $url
        ]);

        try {
            $multipartData = [
                [
                    'name' => 'id_customer',
                    'contents' => $request->input('external_customer_id')
                ]
            ];

            if ($request->hasFile('payment_photo')) {
                $file = $request->file('payment_photo');
                $multipartData[] = [
                    'name' => 'payment_photo',
                    'contents' => fopen($file->getPathname(), 'r'),
                    'filename' => $file->getClientOriginalName(),
                    'headers' => [
                        'Content-Type' => $file->getMimeType()
                    ]
                ];
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->attach($multipartData)
                ->post($url);

            Log::info('[CustomerRegistrationPaymentApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to POST registration payment to Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[CustomerRegistrationPaymentApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
