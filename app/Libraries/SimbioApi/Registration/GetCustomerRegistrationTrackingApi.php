<?php

namespace App\Libraries\SimbioApi\Registration;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetCustomerRegistrationTrackingApi extends SimbioApi
{
    public function run($externalCustomerId): array
    {
        $url = $this->baseUrl . '/customerRegistration/viewTracking/' . $externalCustomerId;

        Log::info('[GetCustomerRegistrationTrackingApi] GET tracking info registration to Simbio API', [
            'url' => $url,
            'external_customer_id' => $externalCustomerId,
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->get($url);

            Log::info('[GetCustomerRegistrationTrackingApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to GET tracking info registration to Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetCustomerRegistrationTrackingApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
