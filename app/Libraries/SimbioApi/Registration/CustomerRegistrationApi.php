<?php

namespace App\Libraries\SimbioApi\Registration;

use App\Libraries\SimbioApi\SimbioApi;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CustomerRegistrationApi extends SimbioApi
{
    private $storageFiles = [];

    public function run(Request $request): array
    {
        $url = $this->baseUrl . '/customerRegistration';

        Log::info('[CustomerRegistrationApi] POST registration to Simbio API', [
            'url' => $url,
            'request' => $request->all()
        ]);

        try {
            $multipartData = $this->prepareMultipartData($request);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept'        => 'application/json',
            ])->send('POST', $url, [
                'multipart' => $multipartData,
            ]);

            Log::info('[CustomerRegistrationApi] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to POST registration to Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[CustomerRegistrationApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }

    private function prepareMultipartData(Request $request): array
    {
        $multipartData = [
            ['name' => 'fullname', 'contents' => $request->input('fullname')],
            ['name' => 'address_ktp', 'contents' => $request->input('address_ktp')],
            ['name' => 'no_ktp', 'contents' => $request->input('no_ktp')],
            ['name' => 'profession', 'contents' => $request->input('profession')],
            ['name' => 'telephone', 'contents' => $request->input('telephone')],
            ['name' => 'name', 'contents' => $request->input('name')],
            ['name' => 'address', 'contents' => $request->input('address')],
            ['name' => 'id_sub_district', 'contents' => $request->input('id_sub_district')],
            ['name' => 'id_village', 'contents' => $request->input('id_village')],
            ['name' => 'land_status', 'contents' => $request->input('land_status')],
            ['name' => 'land_document', 'contents' => $request->input('land_document')],
            ['name' => 'occupant', 'contents' => $request->input('occupant')],
            ['name' => 'used_for', 'contents' => $request->input('used_for')],
            ['name' => 'electrical_power', 'contents' => $request->input('electrical_power')],
            ['name' => 'building_area', 'contents' => $request->input('building_area')],
            ['name' => 'long_registration', 'contents' => $request->input('long_registration')],
            ['name' => 'lat_registration', 'contents' => $request->input('lat_registration')],
        ];

        if ($request->filled('etc')) {
            $multipartData[] = ['name' => 'etc', 'contents' => $request->input('etc')];
        }

        $this->handleFileUpload($request, $multipartData, 'family_card_photo');
        $this->handleFileUpload($request, $multipartData, 'house_image_registration');

        foreach ($this->storageFiles as $fieldName => $filePath) {
            if (file_exists($filePath) && is_readable($filePath) && filesize($filePath) > 0) {
                $multipartData[] = [
                    'name'      => $fieldName,
                    'contents'  => file_get_contents($filePath),
                    'filename'  => basename($filePath),
                    'headers'   => ['Content-Type' => mime_content_type($filePath)]
                ];

                Log::info("[CustomerRegistrationApi] Storage file added to multipart", [
                    'field' => $fieldName,
                    'path' => $filePath,
                    'size' => filesize($filePath),
                    'mime' => mime_content_type($filePath)
                ]);
            }
        }

        return $multipartData;
    }

    public function setStorageFile(string $fieldName, string $filePath): void
    {
        $this->storageFiles[$fieldName] = $filePath;
    }

    private function handleFileUpload(Request $request, array &$multipartData, string $fieldName): void
    {
        if ($request->hasFile($fieldName)) {
            $file = $request->file($fieldName);

            if ($file instanceof UploadedFile) {
                $filePath = $file->getPathname();

                $multipartData[] = [
                    'name'      => $fieldName,
                    'contents'  => fopen($filePath, 'r'),
                    'filename'  => $file->getClientOriginalName(),
                    'headers'   => ['Content-Type' => $file->getMimeType()]
                ];
            } else {
                Log::error("[CustomerRegistrationApi.handleFileUpload] File is not UploadedFile instance", [
                    'field' => $fieldName,
                    'type' => gettype($file),
                    'class' => is_object($file) ? get_class($file) : 'not_object'
                ]);
            }
        } else {
            Log::warning("[DEBUG] No file found for field", [
                'field' => $fieldName,
                'all_files' => array_keys($request->allFiles())
            ]);
        }
    }
}
