<?php

namespace App\Libraries\SimbioApi;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetServiceZone extends SimbioApi
{
    public function run(): array
    {
        $cacheKey = 'simbio_service_zone';
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $url = $this->baseUrl . '/getServiceZone';

        Log::info('[GetServiceZone] Fetching service-zone from Simbio API', [
            'url' => $url,
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
            ])->get($url);

            Log::info('[GetServiceZone] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    if (count($data['data']) > 0) {
                        Cache::put($cacheKey, $data['data'], now()->addHour());
                    }
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to retrieve service-zone from Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetServiceZone] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
