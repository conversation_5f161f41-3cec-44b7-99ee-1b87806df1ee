<?php

namespace App\Libraries\SimbioApi;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SimbioApi
{
    protected $accessToken;
    protected $baseUrl;
    protected $username;
    protected $password;

    public function __construct()
    {
        $this->baseUrl = config('services.simbio.url');
        $this->username = config('services.simbio.username');
        $this->password = config('services.simbio.password');

        $this->getToken();
    }

    private function getToken()
    {
        if (Cache::has('simbio_access_token')) {
            $this->accessToken = Cache::get('simbio_access_token');
            return;
        }

        $url = $this->baseUrl . '/login';

        Log::info('[SimbioApi] Fetching access token from Simbio API', [
            'url' => $url,
        ]);

        $this->accessToken = null;;

        try {
            $response = Http::asForm()->post($url, [
                'email' => $this->username,
                'password' => $this->password,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $data = $data['data'] ?? null;
                $accessToken = $data['access_token'] ?? null;

                if ($accessToken) {
                    Cache::put('simbio_access_token', $accessToken, now()->addMinutes(115));
                    $this->accessToken = $accessToken;
                    Log::info('[SimbioApi] Access token retrieved successfully from Simbio API');
                } else {
                    Log::error('[SimbioApi] Access token not found in Simbio API response', [
                        'url' => $url,
                        'status' => $response->status(),
                        'meta' => $response->json()['meta'] ?? null,
                    ]);
                    throw new \Exception('Failed to retrieve access token from Simbio API.');
                }
            }
        } catch (\Exception $e) {
            Log::error('[SimbioApi] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return null;
    }
}
