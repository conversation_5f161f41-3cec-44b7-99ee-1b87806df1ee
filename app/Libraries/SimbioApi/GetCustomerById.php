<?php

namespace App\Libraries\SimbioApi;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetCustomerById extends SimbioApi
{
    public function run($customerId): array
    {
        $url = $this->baseUrl . '/customer/findById';

        Log::info('[GetCustomerById] Fetching customer-byid from Simbio API', [
            'url' => $url,
            'customerId' => $customerId,
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->withBody(json_encode(['id_customer' => $customerId]), 'application/json')
                ->post($url);

            Log::info('[GetCustomerById] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to retrieve customer-byid from Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetCustomerById] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
