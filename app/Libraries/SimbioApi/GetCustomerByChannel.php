<?php

namespace App\Libraries\SimbioApi;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GetCustomerByChannel extends SimbioApi
{
    public function run($channel): array
    {
        $url = $this->baseUrl . '/customer/findByChannel';

        Log::info('[GetCustomerByChannel] Fetching customer-channel from Simbio API', [
            'url' => $url,
            'channel' => $channel,
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json',
            ])
                ->withBody(json_encode(['number' => $channel]), 'application/json')
                ->post($url);

            Log::info('[GetCustomerByChannel] Response from Simbio API', [
                'url' => $url,
                'status' => $response->status(),
                'meta' => $response->json()['meta'] ?? null,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to retrieve customer-channel from Simbio API.');
            }
        } catch (\Exception $e) {
            Log::error('[GetCustomerByChannel] Error connecting to Simbio API', [
                'url' => $url,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw new \Exception('Error connecting to Simbio API: ' . $e->getMessage());
        }

        return [];
    }
}
