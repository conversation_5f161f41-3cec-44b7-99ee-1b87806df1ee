<?php

namespace App\Libraries\SimpegApi;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SimpegApi
{
    private $accessToken;
    private $baseUrl;
    private $username;
    private $password;

    public function __construct()
    {
        $this->baseUrl = config('services.simpeg.url');
        $this->username = config('services.simpeg.username');
        $this->password = config('services.simpeg.password');

        $this->getToken();
    }

    private function getToken()
    {
        if (Cache::has('simpeg_access_token')) {
            $this->accessToken = Cache::get('simpeg_access_token');
            return;
        }

        $url = $this->baseUrl . '/login';

        Log::info('[SimpegApi] Fetching access token from Simpeg API', [
            'url' => $url,
        ]);

        $this->accessToken = null;;

        try {
            $response = Http::asForm()->post($url, [
                'email' => $this->username,
                'password' => $this->password,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $accessToken = $data['token'] ?? null;

                if ($accessToken) {
                    Cache::put('simpeg_access_token', $accessToken, now()->addMinutes(115));
                    $this->accessToken = $accessToken;
                    Log::info('[SimpegApi] Access token retrieved successfully from Simpeg API');
                } else {
                    Log::error('[SimpegApi] Access token not found in Simpeg API response', [
                        'url' => $url,
                        'status' => $response->status(),
                        'meta' => $response->json()['meta'] ?? null,
                    ]);
                    throw new \Exception('Failed to retrieve access token from Simpeg API.');
                }
            }
        } catch (\Exception $e) {
            Log::error('[SimpegApi] Error connecting to Simpeg API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simpeg API: ' . $e->getMessage());
        }

        return null;
    }

    public function getFilteredEmployee(): array
    {
        if (Cache::has('simpeg_filtered_employee')) {
            return Cache::get('simpeg_filtered_employee');
        }

        $url = $this->baseUrl . '/v1/getFilteredEmployee';

        Log::info('[SimpegApi] Fetching filtered employee from Simpeg API', [
            'url' => $url,
        ]);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
            ])->get($url);

            Log::info('[SimpegApi] Response from Simpeg API', [
                'url' => $url,
                'isSuccessful' => $response->successful(),
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['data']) && is_array($data['data'])) {
                    if (count($data['data']) > 0) {
                        Cache::put('simpeg_filtered_employee', $data['data'], now()->addHour());
                    }
                    return $data['data'];
                }
            } else {
                throw new \Exception('Failed to retrieve filtered employee from Simpeg API.');
            }
        } catch (\Exception $e) {
            Log::error('[SimpegApi] Error connecting to Simpeg API', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            throw new \Exception('Error connecting to Simpeg API: ' . $e->getMessage());
        }

        return [];
    }
}
