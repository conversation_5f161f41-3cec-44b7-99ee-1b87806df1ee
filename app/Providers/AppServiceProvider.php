<?php

namespace App\Providers;

use App\Models\Consultation\Consultation;
use App\Models\Customer\CustomerChannelChange;
use App\Models\User\Enum\UserRole;
use App\Observers\Consultation\ConsultationObserver;
use App\Observers\Customer\CustomerChannelChangeObserver;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Laravel\Passport\Passport;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Gate::before(function ($user, $ability) {
            return $user->hasRole(UserRole::SUPERADMIN) ? true : null;
        });

        Passport::tokensExpireIn(now()->addDays(15));
        Passport::refreshTokensExpireIn(now()->addDays(30));
        Passport::personalAccessTokensExpireIn(now()->addMonths(6));

        Consultation::observe(ConsultationObserver::class);
        CustomerChannelChange::observe(CustomerChannelChangeObserver::class);
    }
}
