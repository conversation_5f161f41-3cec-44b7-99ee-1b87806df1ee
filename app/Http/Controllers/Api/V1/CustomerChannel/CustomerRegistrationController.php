<?php

namespace App\Http\Controllers\Api\V1\CustomerChannel;

use App\Actions\CustomerChannel\Registration\GetCustomerRegistrationPaymentAction;
use App\Actions\CustomerChannel\Registration\GetCustomerRegistrationTrackingAction;
use App\Actions\CustomerChannel\Registration\StoreCustomerRegistrationAction;
use App\Actions\CustomerChannel\Registration\StoreCustomerRegistrationPaymentAction;
use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\CustomerChannel\StoreCustomerRegistrationPaymentRequest;
use App\Http\Requests\Api\V1\CustomerChannel\StoreCustomerRegistrationRequest;
use App\Http\Resources\CustomerChannel\CustomerChannelRegDetailResource;
use App\Libraries\SimbioApi\Registration\GetCustomerRegistrationPaymentApi;
use App\Libraries\SimbioApi\Registration\GetCustomerRegistrationTrackingApi;
use App\Models\Customer\CustomerChannel;
use App\Models\Customer\Enum\PaymentStatus;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class CustomerRegistrationController extends Controller
{
    use ApiResponse;

    public function store(StoreCustomerRegistrationRequest $request): JsonResponse
    {
        $store = new StoreCustomerRegistrationAction();
        return $store->handle($request);
    }

    public function show(string $id)
    {
        $customer = auth('api')->user();
        $customerChannel = CustomerChannel::where('id', $id)
            ->whereHas('customers', function ($query) use ($customer) {
                $query->where('customer_id', $customer->id);
            })
            ->first();

        if (!$customerChannel) {
            return $this->errorResponse("Saluran tidak ditemukan", 404);
        }

        $payment = null;
        $tracking = null;
        try {
            $getPayment = new GetCustomerRegistrationPaymentApi();
            $paymentInfo = $getPayment->run($customerChannel->external_customer_id);
            unset($paymentInfo['id']);
            $paymentInfo['external_customer_id'] = $customerChannel->external_customer_id;

            foreach ($paymentInfo['registration_payments'] as $key => $payment) {
                $paymentInfo['registration_payments'][$key]['status_description'] = PaymentStatus::getDescription($payment['status']);
            }
            $payment = $paymentInfo;
        } catch (\Exception $e) {
            Log::error('[CustomerRegistrationController] Error calling GetCustomerRegistrationPaymentApi', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        try {
            $getTracking = new GetCustomerRegistrationTrackingApi();
            $tracking = $getTracking->run($customerChannel->external_customer_id);
            unset($tracking['id']);
        } catch (\Exception $e) {
            Log::error('[CustomerRegistrationController] Error calling GetCustomerRegistrationTrackingApi', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return $this->singleResponse(
            'Detail pendaftaran saluran',
            new CustomerChannelRegDetailResource([
                'customerChannel' => $customerChannel->load('activity'),
                'customer' => $customer,
                'payment' => $payment,
                'tracking' => $tracking
            ])
        );
    }

    public function storePayment(StoreCustomerRegistrationPaymentRequest $request)
    {
        $storePayment = new StoreCustomerRegistrationPaymentAction();
        return $storePayment->handle($request);
    }

    public function getPayment($id)
    {
        $payment = new GetCustomerRegistrationPaymentAction();
        return $payment->handle($id);
    }

    public function getTracking($id)
    {
        $tracking = new GetCustomerRegistrationTrackingAction();
        return $tracking->handle($id);
    }
}
