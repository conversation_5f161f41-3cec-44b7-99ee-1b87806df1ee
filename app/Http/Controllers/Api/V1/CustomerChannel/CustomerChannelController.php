<?php

namespace App\Http\Controllers\Api\V1\CustomerChannel;

use App\Actions\Customer\UpsertCustomerChannelAction;
use App\DTO\UpsertCustomerChannelDTO;
use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\CustomerChannel\StoreCustomerChannelRequest;
use App\Http\Resources\CustomerChannel\CustomerChannelDetailResource;
use App\Jobs\Customer\SyncCustomerChannelJob;
use App\Libraries\SimbioApi\ChangeName\GetChangeNameCategoryApi;
use App\Models\Consultation\Enum\ConsultationStatus;
use App\Models\Customer\CustomerChannel;
use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Enum\ChangeNameStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CustomerChannelController extends Controller
{
    use ApiResponse;

    public function index(Request $request)
    {
        $customer = auth('api')->user();
        $channels = CustomerChannel::withCount(['consultations as unfinished_consultations' => function ($query) {
            return $query->whereIn('status', [ConsultationStatus::WAITING->value, ConsultationStatus::PROGRESS->value]);
        }])
            ->whereHas('customers', function ($query) use ($customer) {
                $query->where('customer_id', $customer->id);
            })
            ->when($request->filled('for-change-name') && $request->get('for-change-name') === 'true', function ($query) {
                $query->whereDoesntHave('changeName', function ($query) {
                    $query->whereNotIn('current_status', [ChangeNameStatus::CANCELED->value, ChangeNameStatus::FINISHED->value]);
                })->where('status', 'Active');
            })
            ->whereNotNull('channel')
            ->orderBy('external_customer_id', 'asc')
            ->limit(5)
            ->get();

        return $this->resourcesResponse(
            'Data Customer channel',
            CustomerChannelDetailResource::collection($channels)
        );
    }

    public function store(StoreCustomerChannelRequest $request)
    {
        $channelId = $request->input('channel');
        $dto = new UpsertCustomerChannelDTO(
            externalCustomerId: null,
            channelId: $channelId,
            customerId: auth('api')->id()
        );

        $result = app(UpsertCustomerChannelAction::class)->handle($dto);

        if ($result->status === 'error' || ($result->status === 'success' && $result->isUpdate)) {
            return $this->errorResponse(
                $result->message,
                $result->status === 'success' && $result->isUpdate ? 400 : $result->code
            );
        }

        return $this->singleResponse(
            'Data Customer channel',
            new CustomerChannelDetailResource($result->data)
        );
    }

    public function destroy(string $externalCustomerId)
    {
        $customer = auth('api')->user();
        $channel = CustomerChannel::whereHas('customers', function ($query) use ($customer) {
            $query->where('customer_id', $customer->id);
        })
            ->where('external_customer_id', $externalCustomerId)
            ->first();

        if (!$channel) {
            return $this->errorResponse(
                'Saluran tidak ditemukan',
                404
            );
        }

        $isOngoingChangeNameExists = CustomerChannelChange::where('external_customer_id', $externalCustomerId)
            ->whereNotIn('current_status', [ChangeNameStatus::CANCELED->value, ChangeNameStatus::FINISHED->value])
            ->exists();

        if ($isOngoingChangeNameExists) {
            return $this->errorResponse(
                'Saluran tidak dapat dihapus, tunggu hingga proses balik nama selesai/dibatalkan',
                400
            );
        }

        $channel->customers()->detach($customer->id);

        return $this->singleResponse(
            'Saluran berhasil dihapus'
        );
    }

    public function sync(string $externalCustomerId)
    {
        $customerChannel = CustomerChannel::where('external_customer_id', $externalCustomerId)->first();
        if (!$customerChannel) {
            Log::error('[CustomerChannelController.sync] Customer channel not found', [
                'external_customer_id' => $externalCustomerId,
            ]);
            return $this->errorResponse(
                'Saluran tidak ditemukan',
                400
            );
        }
        SyncCustomerChannelJob::dispatch($externalCustomerId);

        return $this->singleResponse(
            'Sync job dispatched',
            ['external_customer_id' => $externalCustomerId]
        );
    }

    public function getChangeNameCategory()
    {
        try {
            $getChangeNameCategory = new GetChangeNameCategoryApi();
            $changeNameCategories = $getChangeNameCategory->run();

            return $this->resourcesResponse(
                'Change name categories',
                $changeNameCategories
            );
        } catch (\Exception $e) {
            Log::error('[CustomerChannelController] Error fetching change name category', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->errorResponse(
                'Gagal mengambil data kategori balik nama',
                500
            );
        }
    }
}
