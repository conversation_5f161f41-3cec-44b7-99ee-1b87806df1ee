<?php

namespace App\Http\Controllers\Api\V1\CustomerChannel;

use App\Actions\CustomerChannel\ChangeName\GetChangeNameDetailAction;
use App\Actions\CustomerChannel\ChangeName\GetChangeNamePaymentAction;
use App\Actions\CustomerChannel\ChangeName\GetChangeNamePaymentStatusAction;
use App\Actions\CustomerChannel\ChangeName\GetCustomerChangeNameTrackingAction;
use App\Actions\CustomerChannel\ChangeName\StoreChangeNameAction;
use App\Actions\CustomerChannel\ChangeName\StoreChangeNamePaymentAction;
use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\CustomerChannel\ChangeNamePaymentRequest;
use App\Http\Requests\Api\V1\CustomerChannel\ChangeNameRequest;
use App\Jobs\Customer\SyncChangeNameJob;
use App\Libraries\SimbioApi\ChangeName\GetChangeNameCategoryApi;
use App\Models\Customer\CustomerChannelChange;
use Illuminate\Support\Facades\Log;

class ChangeNameController extends Controller
{
    use ApiResponse;

    public function getCategories()
    {
        try {
            $getChangeNameCategory = new GetChangeNameCategoryApi();
            $changeNameCategories = $getChangeNameCategory->run();

            return $this->resourcesResponse(
                'Change name categories',
                $changeNameCategories
            );
        } catch (\Exception $e) {
            Log::error('[ChangeNameController] Error fetching change name category', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->errorResponse(
                'Failed to fetch change name categories: ' . $e->getMessage(),
                500
            );
        }
    }

    public function store(ChangeNameRequest $request)
    {
        $store = new StoreChangeNameAction();
        return $store->handle($request);
    }

    public function show($id)
    {
        $detail = new GetChangeNameDetailAction();
        return $detail->handle($id);
    }

    public function getPayment($id)
    {
        $payment = new GetChangeNamePaymentAction();
        return $payment->handle($id);
    }

    public function getPaymentStatus($id)
    {
        $payment = new GetChangeNamePaymentStatusAction();
        return $payment->handle($id);
    }

    public function storePayment(ChangeNamePaymentRequest $request)
    {
        $storeChangeNamePaymentAction = new StoreChangeNamePaymentAction();
        return $storeChangeNamePaymentAction->handle($request);
    }

    public function getTracking($id)
    {
        $trackings = new GetCustomerChangeNameTrackingAction();
        return $trackings->handle($id);
    }

    public function sync($externalCustomerId)
    {
        $customerChannelChange = CustomerChannelChange::where('external_customer_id', $externalCustomerId)->first();
        if (!$customerChannelChange) {
            Log::error('[ChangeNameController.sync] Change name not found', [
                'external_customer_id' => $externalCustomerId,
            ]);
            return $this->errorResponse(
                'Balik Nama tidak ditemukan',
                400
            );
        }
        SyncChangeNameJob::dispatch($externalCustomerId);

        return $this->singleResponse(
            'Sync job dispatched',
            ['external_customer_id' => $externalCustomerId]
        );
    }
}
