<?php

namespace App\Http\Controllers\Api\V1\Notification;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Notification\NotificationResource;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->filled('unread') && $request->get('unread') === 'true') {
            $resources = auth('api')->user()->unreadNotifications()->paginate(10);
        } else {
            $resources = auth('api')->user()->notifications()->paginate(10);
        }

        return $this->resourcesResponse(
            'List Notification',
            NotificationResource::collection($resources),
        );
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            $resource = auth('api')->user()->notifications()->findOrFail($id);

            $resource->markAsRead();

            return $this->singleResponse(
                'Update Notification',
                NotificationResource::make($resource),
            );
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Notification not found',
                404
            );
        }
    }

    /**
     * Update all unread.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateAll(Request $request)
    {
        $unreadNotifications = auth('api')->user()->unreadNotifications()->count();
        auth('api')->user()->unreadNotifications->markAsRead();

        return $this->singleResponse(
            'All unread notification has been read',
            [
                'count' => $unreadNotifications
            ],
        );
    }
}
