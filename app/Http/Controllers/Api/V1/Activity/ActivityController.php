<?php

namespace App\Http\Controllers\Api\V1\Activity;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Activity\GetActivityRequest;
use App\Http\Resources\Activity\ActivityResource;
use App\Models\Activity\Activity;
use App\Models\Activity\Enum\ActivityStatus;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ActivityController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(GetActivityRequest $request)
    {
        $isRange = $request->start_date && $request->end_date;
        $resources = Activity::where('customer_id', auth('api')->user()->id)
            ->when($request->search, function ($query) use ($request) {
                $query->where('code', 'like', "%{$request->search}%")
                    ->orWhere('title', 'like', "%{$request->search}%");
            })
            ->when($request->status, function ($query) use ($request) {
                $query->where('status', Str::upper($request->status));
            })
            ->when(!$isRange && $request->date === 'last_30_days', function ($query) {
                $query->whereDate('created_at', '>=', now()->subDays(30));
            })
            ->when(!$isRange && $request->date === 'last_90_days', function ($query) {
                $query->whereDate('created_at', '>=', now()->subDays(90));
            })
            ->when($request->start_date && $request->end_date, function ($query) use ($request) {
                $startDate = Carbon::parse($request->start_date)->startOfDay();
                $endDate = Carbon::parse($request->end_date)->endOfDay();
                $query->where('created_at', '>=', $startDate)
                    ->where('created_at', '<=', $endDate);
            })
            ->orderBy('created_at', $request->sort === 'oldest' ? 'asc' : 'desc')
            ->paginate($request->per_page);

        return $this->resourcesPaginationResponse("Data Aktifitas", ActivityResource::collection($resources));
    }

    public function getStatusList()
    {
        return $this->resourcesResponse(
            'List status aktifitas',
            ActivityStatus::getOptions()
        );
    }
}
