<?php

namespace App\Http\Controllers\Api\V1\Location;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Libraries\SimbioApi\Village\GetSubDistrictVillageApi;
use App\Models\Location\District;
use App\Models\Location\Province;
use App\Models\Location\Regency;
use Illuminate\Http\Request;

class LocationController extends Controller
{
    use ApiResponse;

    public function provinces(Request $request)
    {
        return $this->resourcesResponse(
            'Data Provinsi',
            Province::when($request->filled('search'), function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%');
            })->orderBy('name')->get()->toArray()
        );
    }

    public function regencies(Province $province, Request $request)
    {
        return $this->resourcesResponse(
            'Data Kabupaten/Kota',
            $province->regencies()->when($request->filled('search'), function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%');
            })->orderBy('name')->get()->toArray()
        );
    }

    public function districts(Regency $regency, Request $request)
    {
        return $this->resourcesResponse(
            'Data Kecamatan',
            $regency->districts()->when($request->filled('search'), function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%');
            })->orderBy('name')->get()->toArray()
        );
    }

    public function villages(District $district, Request $request)
    {
        return $this->resourcesResponse(
            'Data Desa',
            $district->villages()->when($request->filled('search'), function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%');
            })->orderBy('name')->get()->toArray()
        );
    }

    public function simbioSubDistricts()
    {
        try {
            $api = new GetSubDistrictVillageApi();
            $data = $api->run();

            $subDistricts = collect($data)->map(function ($item) {
                return [
                    'id'    => $item['id'],
                    'name'  => $item['name_sub_district']
                ];
            })->toArray();

            return $this->resourcesResponse('Data Kecamatan', $subDistricts);
        } catch (\Exception $e) {
            return $this->errorResponse('Gagal mengambil data kecamatan: ' . $e->getMessage(), 500);
        }
    }

    public function simbioVillages($subDistrict)
    {
        try {
            $api = new GetSubDistrictVillageApi();
            $data = $api->run();

            $villages = collect($data)
                ->where('id', $subDistrict)
                ->flatMap(function ($subDistrictData) {
                    return collect($subDistrictData['village'])->map(function ($village) {
                        return [
                            'id'    => $village['id'],
                            'name'  => $village['name_village']
                        ];
                    });
                })->toArray();

            return $this->resourcesResponse('Data Kelurahan', $villages);
        } catch (\Exception $e) {
            return $this->errorResponse('Gagal mengambil data kelurahan: ' . $e->getMessage(), 500);
        }
    }
}
