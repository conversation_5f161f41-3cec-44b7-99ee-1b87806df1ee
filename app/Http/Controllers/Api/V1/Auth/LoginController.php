<?php

namespace App\Http\Controllers\Api\V1\Auth;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Auth\ApiLoginRequest;
use App\Models\Customer\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;

class LoginController extends Controller
{
    use ApiResponse;

    public function login(ApiLoginRequest $request)
    {
        try {
            $user = Socialite::driver('google')->userFromToken($request->token);

            $customer = Customer::where('email', $user->getEmail())->first();

            if (!$customer) {
                $customer = new Customer();
                $customer->fill([
                    'name' => $user->getName(),
                    'email' => $user->getEmail(),
                    'avatar' => $user->getAvatar(),
                    'is_active' => 1
                ]);
                $customer->save();
            }

            $token = $customer->createToken('API Token')->accessToken;
            if ($customer->is_active) {
                return $this->singleResponse('Login sukses', [
                    'customer' => $customer,
                    'token' => $token
                ]);
            } else {
                return $this->errorResponse('Akun anda non-aktif');
            }
        } catch (\Exception $e) {
            Log::debug("Login API", [$e->getMessage()]);
            return $this->errorResponse('Login gagal, token tidak valid', 400);
        }
    }

    public function loginProvider(Request $request)
    {
        return Socialite::driver('google')->redirect();
    }

    public function callback(Request $request)
    {
        try {
            $user = Socialite::driver('google')->user();

            return json_encode($user);
        } catch (\Exception $e) {
            return json_encode([
                'success' => false,
                'message' => 'Login failed',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function logout(Request $request)
    {
        $request->user()->token()->revoke();
        return $this->singleResponse('Logout sukses', null, 200);
    }
}
