<?php

namespace App\Http\Controllers\Api\V1\Profile;

use App\Actions\Customer\UpdateVerificationAction;
use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Customer\CheckUpdateCustomerVerificationRequest;
use App\Http\Requests\Api\V1\Customer\ReUpdateCustomerVerificationRequest;
use App\Http\Requests\Api\V1\Customer\UpdateCustomerRequest;
use App\Http\Requests\Api\V1\Customer\UpdateCustomerVerificationRequest;
use App\Http\Requests\Api\V1\Customer\UpdatePlayerIdRequest;
use App\Http\Resources\Customer\CustomerResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CustomerController extends Controller
{
    use ApiResponse;

    public function show()
    {
        $customer = auth('api')->user();

        if (!$customer) {
            return $this->errorResponse('Data customer tidak ditemukan', 404);
        }

        $customer->load([
            'province',
            'regency',
            'district',
            'village',
        ]);

        return $this->singleResponse('Data customer', new CustomerResource($customer));
    }

    public function update(UpdateCustomerRequest $request)
    {
        try {
            DB::beginTransaction();

            $customer = auth('api')->user();
            $customer->fill($request->validated());
            if ($request->hasFile('avatar')) {
                $customer->avatar = $request->avatar->store('customer-avatar', 'public');
            }
            $customer->save();

            DB::commit();

            $customer->load([
                'province',
                'regency',
                'district',
                'village',
            ]);

            return $this->singleResponse(
                'Data customer berhasil diubah',
                new CustomerResource($customer)
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Gagal mengubah data customer: ' . $e->getMessage(), 500);
        }
    }

    public function checkVerificationForm(CheckUpdateCustomerVerificationRequest $request)
    {
        return $this->singleResponse('Data customer valid', []);
    }

    public function updateVerification(UpdateCustomerVerificationRequest $request)
    {
        return UpdateVerificationAction::run($request);
    }

    public function reUpdateVerification(ReUpdateCustomerVerificationRequest $request)
    {
        return UpdateVerificationAction::run($request);
    }

    public function updatePlayerId(UpdatePlayerIdRequest $request)
    {
        try {
            DB::beginTransaction();

            $customer   = auth('api')->user();
            $playerIds  = $customer->player_ids ?? [];

            if (!in_array($request->player_id, $playerIds) && !$request->is_delete) {
                $playerIds[] = $request->player_id;
            } else if (in_array($request->player_id, $playerIds) && $request->is_delete) {
                $playerIds = array_diff($playerIds, [$request->player_id]);
            }
            $customer->player_ids = collect($playerIds)->values();
            $customer->save();

            DB::commit();

            $customer->load([
                'province',
                'regency',
                'district',
                'village',
            ]);

            return $this->singleResponse(
                'Data customer berhasil diubah',
                new CustomerResource($customer)
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Gagal mengubah data customer: ' . $e->getMessage(), 500);
        }
    }

    public function getPoint()
    {
        $customer = auth('api')->user();

        if (!$customer) {
            return $this->errorResponse('Data customer tidak ditemukan', 404);
        }

        return $this->singleResponse('Poin customer', [
            'point' => $customer->point,
        ]);
    }
}
