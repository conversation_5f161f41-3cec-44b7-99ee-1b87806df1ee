<?php

namespace App\Http\Controllers\Api\V1\Consultation;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\Consultation\ConsultationCategory;
use App\Models\Consultation\Enum\ConsultationStatus;
use Illuminate\Http\Request;

class ConsultationCategoryController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $resources = ConsultationCategory::when($request->search, function ($query) use ($request) {
            $query->where('name', 'like', "%{$request->search}%");
        })
            ->where('is_active', true)
            ->orderBy('name', 'asc')
            ->paginate($request->per_page);

        return $this->resourcesPaginationResponse("Data Kategory Konsultasi", $resources);
    }
}
