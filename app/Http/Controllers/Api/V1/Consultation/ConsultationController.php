<?php

namespace App\Http\Controllers\Api\V1\Consultation;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Consultation\StoreConsultationRequest;
use App\Http\Requests\Api\V1\Consultation\CancelConsultationRequest;
use App\Models\Consultation\Consultation;
use App\Models\Consultation\Enum\ConsultationStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ConsultationController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $consultations = Consultation::where('customer_id', auth('api')->user()->id)
            ->when($request->search, function ($query) use ($request) {
                $query->where('code', 'like', "%{$request->search}%")
                    ->orWhere('title', 'like', "%{$request->search}%");
            })
            ->when($request->status, function ($query) use ($request) {
                $query->where('status', Str::upper($request->status));
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->per_page);

        return $this->resourcesPaginationResponse("Data Konsultasi", $consultations);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreConsultationRequest $request)
    {
        $existingConsultation = Consultation::select('id', 'status')
            ->when($request->filled('external_customer_id'), function ($query) use ($request) {
                $query->where('external_customer_id', $request->external_customer_id);
            })
            ->where('customer_id', auth('api')->user()->id)
            ->whereIn('status', [ConsultationStatus::WAITING->value, ConsultationStatus::PROGRESS->value])
            ->first();

        if ($existingConsultation) {
            return $this->errorResponse('Kamu masih memiliki permohonan layanan konsultasi yang belum selesai. Silakan tunggu hingga permohonan tersebut selesai atau dibatalkan sebelum membuat permohonan baru.', 400);
        }

        Cache::forget('waiting_consultation_count');

        DB::beginTransaction();
        try {
            $consultation = new Consultation();
            $consultation->fill($request->validated());
            $consultation->customer_id = auth('api')->user()->id;
            $consultation->code = "CONS-" . auth('api')->user()->id . Str::upper(Str::random(8));
            $consultation->status = ConsultationStatus::WAITING->value;
            $consultation->save();

            Log::info('[ConsultationController - API] Consultation created successfully', [
                'consultation_id' => $consultation->id,
                'customer_id' => auth('api')->user()->id
            ]);

            DB::commit();

            return $this->singleResponse('Permohonan layanan konsultasi berhasil diajukan', $consultation, 201);
        } catch (\Exception $e) {
            Log::error('[ConsultationController - API] Failed to create consultation', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            DB::rollBack();
            return $this->errorResponse('Gagal mengajukan permohonan layanan konsultasi', 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $consultation = Consultation::with(
            'activity.timelines',
            'customerChannel',
            'category'
        )
            ->where('id', $id)
            ->where('customer_id', auth('api')->user()->id)
            ->first();

        if (!$consultation) {
            return $this->errorResponse('Layanan konsultasi tidak ditemukan', 404);
        }

        return $this->singleResponse('Detail layanan konsultasi', $consultation);
    }

    /**
     * Update the specified resource in storage.
     */
    public function cancel(CancelConsultationRequest $request, string $id)
    {
        $consultation = Consultation::where('id', $id)
            ->where('customer_id', auth('api')->user()->id)
            ->first();

        if (!$consultation) {
            return $this->errorResponse('Layanan konsultasi tidak ditemukan', 404);
        }

        if ($consultation->status !== ConsultationStatus::WAITING->value) {
            return $this->errorResponse('Layanan konsultasi tidak dapat dibatalkan karena statusnya tidak sesuai', 400);
        }

        DB::beginTransaction();
        try {
            $consultation->status = ConsultationStatus::CANCELED_BY_CUSTOMER->value;
            $consultation->cancel_reason = $request->cancel_reason;
            $consultation->save();

            Log::info('[ConsultationController - API] Consultation cancelled successfully', [
                'consultation_id' => $consultation->id,
                'customer_id' => auth('api')->user()->id,
                'cancel_reason' => $request->cancel_reason,
            ]);

            DB::commit();

            $consultation->setRelation('activity', null);
            return $this->singleResponse('Permohonan layanan konsultasi berhasil dibatalkan', $consultation);
        } catch (\Exception $e) {
            Log::error('[ConsultationController - API] Failed to cancel consultation', [
                'error' => $e->getMessage(),
                'consultation_id' => $id,
                'customer_id' => auth('api')->user()->id,
            ]);
            DB::rollBack();
            return $this->errorResponse('Gagal membatalkan permohonan layanan konsultasi', 500);
        }
    }
}
