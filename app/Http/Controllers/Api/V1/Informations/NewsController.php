<?php

namespace App\Http\Controllers\Api\V1\Informations;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\Informations\News;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class NewsController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $resources = News::when($request->filled('search'), function ($query) use ($request) {
            $query->where('title', 'like', "%{$request->search}%")
                ->orWhere('content', 'like', "%{$request->search}%")
                ->orWhere('slug', 'like', "%{$request->search}%");
        })
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('is_active', Str::lower($request->status) === 'active');
            })
            ->when($request->filled('category'), function ($query) use ($request) {
                $query->where('category', Str::lower($request->category));
            })
            ->orderBy('date', 'desc')
            ->paginate($request->per_page);

        return $this->resourcesPaginationResponse("Data News", $resources);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $news = News::select([
            'news.*',
            'creator.name as creator_name',
            'updater.name as updater_name',
        ])
            ->join('users as creator', 'news.created_by', '=', 'creator.id')
            ->leftJoin('users as updater', 'news.updated_by', '=', 'updater.id')
            ->where('news.id', $id)
            ->first();

        if (!$news) {
            return $this->errorResponse("News not found", 404);
        }

        return $this->singleResponse("Detail News", $news);
    }
}
