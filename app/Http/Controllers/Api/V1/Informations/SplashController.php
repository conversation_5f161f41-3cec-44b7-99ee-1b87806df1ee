<?php

namespace App\Http\Controllers\Api\V1\Informations;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\Informations\Splash;
use Illuminate\Http\Request;

class SplashController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $resources = Splash::where('is_active', true)->first();

        return $this->singleResponse("Data Splash", $resources);
    }
}
