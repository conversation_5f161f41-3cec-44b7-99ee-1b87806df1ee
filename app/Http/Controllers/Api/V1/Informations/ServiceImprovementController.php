<?php

namespace App\Http\Controllers\Api\V1\Informations;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\Informations\ServiceImprovement;
use Illuminate\Http\Request;

class ServiceImprovementController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $resources = ServiceImprovement::when($request->search, function ($query) use ($request) {
            $query->where('title', 'like', "%{$request->search}%")
                ->orWhere('zone_name', 'like', "%{$request->search}%")
                ->orWhere('service_area_name', 'like', "%{$request->search}%");
        })
            ->where('is_active', true)
            ->orderBy('date', 'desc')
            ->paginate($request->per_page);

        return $this->resourcesPaginationResponse("Data Penyempurnaan Layanan", $resources);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $serviceImprovement = ServiceImprovement::select([
            'service_improvements.*',
            'creator.name as creator_name',
            'updater.name as updater_name',
        ])
            ->join('users as creator', 'service_improvements.created_by', '=', 'creator.id')
            ->leftJoin('users as updater', 'service_improvements.updated_by', '=', 'updater.id')
            ->where('service_improvements.is_active', true)
            ->where('service_improvements.id', $id)
            ->first();

        if (!$serviceImprovement) {
            return $this->errorResponse("Penyempurnaan Layanan tidak ditemukan", 404);
        }

        return $this->singleResponse("Detail Penyempurnaan Layanan", $serviceImprovement);
    }
}
