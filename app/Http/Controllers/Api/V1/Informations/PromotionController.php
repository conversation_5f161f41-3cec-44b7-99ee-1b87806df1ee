<?php

namespace App\Http\Controllers\Api\V1\Informations;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\Informations\Promotion;
use Illuminate\Http\Request;

class PromotionController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $resources = Promotion::when($request->search, function ($query) use ($request) {
            $query->where('title', 'like', "%{$request->search}%")
                ->orWhere('content', 'like', "%{$request->search}%")
                ->orWhere('slug', 'like', "%{$request->search}%");
        })
            ->where('is_active', true)
            ->orderBy('date', 'desc')
            ->paginate($request->per_page);

        return $this->resourcesPaginationResponse("Data Promotion", $resources);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $promotion = Promotion::select([
            'promotions.id',
            'promotions.title',
            'promotions.content',
            'promotions.slug',
            'promotions.is_active',
            'promotions.created_at',
            'promotions.updated_at',
            'promotions.date',
            'promotions.image',
            'creator.name as creator_name',
            'updater.name as updater_name',
        ])
            ->join('users as creator', 'promotions.created_by', '=', 'creator.id')
            ->leftJoin('users as updater', 'promotions.updated_by', '=', 'updater.id')
            ->where('promotions.is_active', true)
            ->where('promotions.id', $id)
            ->first();

        if (!$promotion) {
            return $this->errorResponse("Promotion tidak ditemukan", 404);
        }

        return $this->singleResponse("Detail Promotion", $promotion);
    }

    public function current()
    {
        $promotion = Promotion::select([
            'promotions.id',
            'promotions.title',
            'promotions.content',
            'promotions.slug',
            'promotions.is_active',
            'promotions.created_at',
            'promotions.updated_at',
            'promotions.date',
            'promotions.image',
            'creator.name as creator_name',
            'updater.name as updater_name',
        ])
            ->join('users as creator', 'promotions.created_by', '=', 'creator.id')
            ->leftJoin('users as updater', 'promotions.updated_by', '=', 'updater.id')
            ->where('promotions.is_active', true)
            ->whereDate('date', '>=', now())
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$promotion) {
            return $this->errorResponse("Promotion tidak ditemukan", 404);
        }

        return $this->singleResponse("Current Promotion", $promotion);
    }
}
