<?php

namespace App\Http\Controllers\Api\V1\Informations;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Models\Informations\Event;
use Illuminate\Http\Request;

class EventController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $resources = Event::when($request->search, function ($query) use ($request) {
            $query->where('title', 'like', "%{$request->search}%")
                ->orWhere('content', 'like', "%{$request->search}%")
                ->orWhere('slug', 'like', "%{$request->search}%");
        })
            ->where('is_active', true)
            ->orderBy('date', 'desc')
            ->paginate($request->per_page);

        return $this->resourcesPaginationResponse("Data Event", $resources);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $event = Event::select([
            'events.id',
            'events.title',
            'events.slug',
            'events.content',
            'events.is_active',
            'events.created_at',
            'events.updated_at',
            'events.date',
            'events.image',
            'creator.name as creator_name',
            'updater.name as updater_name',
        ])
            ->join('users as creator', 'events.created_by', '=', 'creator.id')
            ->leftJoin('users as updater', 'events.updated_by', '=', 'updater.id')
            ->where('events.is_active', true)
            ->where('events.id', $id)
            ->first();

        if (!$event) {
            return $this->errorResponse("Event tidak ditemukan", 404);
        }

        return $this->singleResponse("Detail Event", $event);
    }
}
