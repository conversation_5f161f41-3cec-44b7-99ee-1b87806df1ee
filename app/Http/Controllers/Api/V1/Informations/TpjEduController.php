<?php

namespace App\Http\Controllers\Api\V1\Informations;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Information\EduDetailResource;
use App\Http\Resources\Information\EduResource;
use App\Models\Informations\Edu;
use Illuminate\Http\Request;

class TpjEduController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $filters = [];

        $resources = Edu::when($request->search, function ($query) use ($request) {
            $query->where('title', 'like', "%{$request->search}%");
        })
            ->with('images')
            ->where('is_active', true)
            ->orderBy('date', 'desc')
            ->paginate($request->per_page);

        return $this->resourcesPaginationResponse("Data Edu", EduResource::collection($resources));
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $edu = Edu::select([
            'edus.id',
            'edus.title',
            'edus.is_active',
            'edus.created_at',
            'edus.updated_at',
            'edus.date',
            'creator.name as creator_name',
            'updater.name as updater_name',
        ])
            ->join('users as creator', 'edus.created_by', '=', 'creator.id')
            ->leftJoin('users as updater', 'edus.updated_by', '=', 'updater.id')
            ->with('images')
            ->where('edus.is_active', true)
            ->where('edus.id', $id)
            ->first();

        if (!$edu) {
            return $this->errorResponse("Edu tidak ditemukan", 404);
        }

        return $this->singleResponse("Detail Edu", EduDetailResource::make($edu));
    }
}
