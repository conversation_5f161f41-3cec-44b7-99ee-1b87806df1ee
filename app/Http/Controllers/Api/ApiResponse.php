<?php

namespace App\Http\Controllers\Api;

trait ApiResponse
{
    public function successCreatedResponse($message = 'Created', $data = null, $code = 201)
    {
        return response()->json([
            'meta' => [
                'code' => $code,
                'status' => 'success',
                'message' => $message,
            ],
            'data' => $data,
        ], $code);
    }

    public function singleResponse($message = 'Success', $data = null, $code = 200)
    {
        return response()->json([
            'meta' => [
                'code' => $code,
                'status' => 'success',
                'message' => $message,
            ],
            'data' => $data,
        ], $code);
    }

    public function resourcesResponse($message = 'Success', $data = null, $code = 200)
    {
        return response()->json([
            'meta' => [
                'code' => $code,
                'status' => 'success',
                'message' => $message,
            ],
            'data' => $data,
        ], $code);
    }

    public function resourcesPaginationResponse($message = 'Success', $resources, $code = 200)
    {
        return response()->json([
            'meta' => [
                'code' => $code,
                'status' => 'success',
                'message' => $message,
            ],
            'data' => [
                'pagination'    => [
                    'current_page'  => $resources->currentPage(),
                    'last_page'     => $resources->lastPage(),
                    'per_page'      => $resources->perPage(),
                    'total'         => $resources->total(),
                ],
                'resources'         => $resources->items(),
            ],
        ], $code);
    }

    public function errorResponse($message = 'Error', $code = 400)
    {
        return response()->json([
            'meta' => [
                'code' => $code,
                'status' => 'error',
                'message' => $message,
            ],
            'data' => null,
        ], $code);
    }
}
