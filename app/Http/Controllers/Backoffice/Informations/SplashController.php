<?php

namespace App\Http\Controllers\Backoffice\Informations;

use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Informations\StoreSplashRequest;
use App\Http\Requests\IndexRequest;
use App\Libraries\Helpers\RouteHelper;
use App\Models\Informations\Filters\StatusFilter;
use App\Models\Informations\Splash;
use App\Services\ImageCompressor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class SplashController extends Controller
{
    public function index(IndexRequest $request)
    {
        $filters = [
            new StatusFilter()
        ];

        $resources = Splash::filterResource($request, ['title'], $filters)
            ->orderBy($request->get('sort', 'created_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('Splash/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Data Splash',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('splashes.index'), 'title' => 'Informasi'],
                ['link' => route('splashes.index'), 'title' => 'Splash'],
            ],
        ]));
    }

    public function create()
    {
        return Inertia::render('Splash/Form', [
            'title'         => 'Tambah Splash',
            'breadcrumbs'    => [
                ['link' => route('splashes.index'), 'title' => 'Informasi'],
                ['link' => route('splashes.index'), 'title' => 'Splash'],
                ['link' => route('splashes.create'), 'title' => 'Tambah'],
            ]
        ]);
    }

    public function store(StoreSplashRequest $request)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();

            if ($request->is_active) {
                Splash::where('is_active', true)->update(['is_active' => false]);
            }

            $splash = new Splash();
            $splash->title = $request->title;
            $splash->is_active = $request->is_active;
            $splash->created_by = auth()->id();

            if ($request->hasFile('image')) {
                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'splash-images'
                );
                $splash->image = $result['path'];
            } else {
                $splash->image = RouteHelper::ImageUrlToDb($request->image);
            }

            $splash->save();

            DB::commit();

            return redirect()->route('splashes.index')->with('success', 'Data berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error store Splash', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('splashes.index')->with('error', 'Data gagal ditambahkan');
        }
    }

    public function show(Splash $splash)
    {
        return Inertia::render('Splash/Show', [
            'title'         => 'Detail Splash',
            'resource'      => $splash->load(['creator', 'updater']),
            'breadcrumbs'    => [
                ['link' => route('splashes.index'), 'title' => 'Informasi'],
                ['link' => route('splashes.index'), 'title' => 'Splash'],
                ['link' => route('splashes.show', $splash), 'title' => 'Detail'],
            ]
        ]);
    }

    public function edit(Splash $splash)
    {
        return Inertia::render('Splash/Form', [
            'title'         => 'Edit Splash',
            'resource'      => $splash,
            'breadcrumbs'    => [
                ['link' => route('splashes.index'), 'title' => 'Informasi'],
                ['link' => route('splashes.index'), 'title' => 'Splash'],
                ['link' => route('splashes.edit', $splash), 'title' => 'Edit'],
            ]
        ]);
    }

    public function update(StoreSplashRequest $request, Splash $splash)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();

            if ($request->is_active) {
                Splash::where('is_active', true)
                    ->where('id', '!=', $splash->id)
                    ->update(['is_active' => false]);
            }

            $splash->title = $request->title;
            $splash->is_active = $request->is_active;
            $splash->updated_by = auth()->id();

            if ($request->hasFile('image')) {
                if ($splash->getRawOriginal('image') && Storage::disk('public')->exists($splash->getRawOriginal('image'))) {
                    Storage::disk('public')->delete($splash->getRawOriginal('image'));

                    Log::info('[SplashController.update] Splash image deleted', [
                        'id' => $splash->id,
                        'image' => $splash->getRawOriginal('image'),
                    ]);
                }
                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'splash-images'
                );
                $splash->image = $result['path'];
            } else {
                $splash->image = RouteHelper::ImageUrlToDb($request->image);
            }

            $splash->save();

            DB::commit();

            return redirect()->route('splashes.index')->with('success', 'Data berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error update Splash', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('splashes.index')->with('error', 'Data gagal diubah');
        }
    }

    public function destroy(Splash $splash)
    {
        try {
            $splash->delete();

            if ($splash->getRawOriginal('image') && Storage::disk('public')->exists($splash->getRawOriginal('image'))) {
                Storage::disk('public')->delete($splash->getRawOriginal('image'));

                Log::info('[SplashController.destroy] Splash image deleted', [
                    'id' => $splash->id,
                    'image' => $splash->getRawOriginal('image'),
                ]);
            }

            return redirect()->route('splashes.index')->with('success', 'Data berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error delete Splash', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('splashes.index')->with('error', 'Data gagal dihapus');
        }
    }
}
