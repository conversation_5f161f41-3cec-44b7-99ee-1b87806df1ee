<?php

namespace App\Http\Controllers\Backoffice\Informations;

use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Informations\StorePromotionRequest;
use App\Http\Requests\IndexRequest;
use App\Libraries\Helpers\RouteHelper;
use App\Models\Informations\Filters\DateFilter;
use App\Models\Informations\Filters\StatusFilter;
use App\Models\Informations\Promotion;
use App\Services\ImageCompressor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class PromotionsController extends Controller
{
    public function index(IndexRequest $request)
    {
        $filters = [
            new StatusFilter(),
            new DateFilter(),
        ];

        $resources = Promotion::filterResource($request, ['title', 'content'], $filters)
            ->orderBy($request->get('sort', 'created_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('Promotion/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Data Promo',
            'resources'     => $resources,
            'breadcrumbs'   => [
                ['link' => route('promotions.index'), 'title' => 'Informasi'],
                ['link' => route('promotions.index'), 'title' => 'Promo'],
            ],
        ]));
    }

    public function create()
    {
        $permissions = config('permissions');

        return Inertia::render('Promotion/Form', [
            'title'         => 'Tambah Promo',
            'breadcrumbs'    => [
                ['link' => route('promotions.index'), 'title' => 'Informasi'],
                ['link' => route('promotions.index'), 'title' => 'Promo'],
                ['link' => route('promotions.create'), 'title' => 'Tambah'],
            ],
            'permissions'    => $permissions,
        ]);
    }

    public function store(StorePromotionRequest $request)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();

            $promo = new Promotion();
            $promo->fill($request->validated());
            $promo->slug = Str::slug($promo->title, '-');

            if ($request->hasFile('image')) {
                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'promotion-images'
                );
                $promo->image = $result['path'];
            } else {
                $promo->image = RouteHelper::ImageUrlToDb($request->image);
            }

            $promo->created_by = auth()->id();
            $promo->save();

            DB::commit();

            return redirect()->route('promotions.index')->with('success', 'Promo berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error store promotion', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('promotions.index')->with('error', 'Promo gagal ditambahkan');
        }
    }

    public function show(Promotion $promotion)
    {
        return Inertia::render('Promotion/Show', [
            'title'         => 'Detail Promo',
            'resource'      => $promotion->load('creator', 'updater'),
            'breadcrumbs'    => [
                ['link' => route('promotions.index'), 'title' => 'Informasi'],
                ['link' => route('promotions.index'), 'title' => 'Promo'],
                ['link' => route('promotions.show', $promotion), 'title' => 'Detail'],
            ]
        ]);
    }

    public function edit(Promotion $promotion)
    {
        return Inertia::render('Promotion/Form', [
            'title'         => 'Edit Promo',
            'resource'      => $promotion,
            'breadcrumbs'   => [
                ['link' => route('promotions.index'), 'title' => 'Informasi'],
                ['link' => route('promotions.index'), 'title' => 'Promo'],
                ['link' => route('promotions.edit', $promotion), 'title' => 'Edit'],
            ]
        ]);
    }

    public function update(StorePromotionRequest $request, Promotion $promotion)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();
            $promotion->fill($request->validated());
            $promotion->slug = Str::slug($promotion->title, '-');
            $promotion->updated_by = auth()->id();

            if ($request->hasFile('image')) {
                if ($promotion->getRawOriginal('image') && Storage::disk('public')->exists($promotion->getRawOriginal('image'))) {
                    Storage::disk('public')->delete($promotion->getRawOriginal('image'));

                    Log::info('[PromotionController.update] Promotion image deleted', [
                        'id' => $promotion->id,
                        'image' => $promotion->getRawOriginal('image'),
                    ]);
                }

                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'promotion-images'
                );
                $promotion->image = $result['path'];
            } else {
                $promotion->image = RouteHelper::ImageUrlToDb($request->image);
            }

            $promotion->save();

            DB::commit();

            return redirect()->route('promotions.index')->with('success', 'Promo berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error update promotion', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('promotions.index')->with('error', 'Promo gagal diubah');
        }
    }

    public function destroy(Promotion $promotion)
    {
        try {
            $promotion->delete();

            if ($promotion->getRawOriginal('image') && Storage::disk('public')->exists($promotion->getRawOriginal('image'))) {
                Storage::disk('public')->delete($promotion->getRawOriginal('image'));

                Log::info('[PromotionController.destroy] Promotion image deleted', [
                    'id' => $promotion->id,
                    'image' => $promotion->getRawOriginal('image'),
                ]);
            }

            return redirect()->route('promotions.index')->with('success', 'Promo berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error delete promotion', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('promotions.index')->with('error', 'Promo gagal dihapus');
        }
    }
}
