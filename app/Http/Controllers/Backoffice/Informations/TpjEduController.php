<?php

namespace App\Http\Controllers\Backoffice\Informations;

use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Informations\StoreEduRequest;
use App\Http\Requests\IndexRequest;
use App\Libraries\Helpers\RouteHelper;
use App\Models\Informations\Edu;
use App\Models\Informations\EduImage;
use App\Models\Informations\Filters\DateFilter;
use App\Models\Informations\Filters\StatusFilter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class TpjEduController extends Controller
{
    public function index(IndexRequest $request)
    {
        $filters = [
            new StatusFilter(),
            new DateFilter(),
        ];

        $resources = Edu::filterResource($request, ['title'], $filters)
            ->orderBy($request->get('sort', 'created_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('Edu/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Data TPJ Edu',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('tpj-edu.index'), 'title' => 'Informasi'],
                ['link' => route('tpj-edu.index'), 'title' => 'TPJ Edu'],
            ],
        ]));
    }

    public function create()
    {
        return Inertia::render('Edu/Form', [
            'title'         => 'Tambah TPJ Edu',
            'breadcrumbs'    => [
                ['link' => route('tpj-edu.index'), 'title' => 'Informasi'],
                ['link' => route('tpj-edu.index'), 'title' => 'TPJ Edu'],
                ['link' => route('tpj-edu.create'), 'title' => 'Tambah'],
            ]
        ]);
    }

    public function store(StoreEduRequest $request)
    {
        try {
            DB::beginTransaction();

            $edu = new Edu();
            $edu->title = $request->title;
            $edu->date = $request->date;
            $edu->is_active = $request->is_active;
            $edu->created_by = auth()->id();
            $edu->save();

            if (!is_array($request->images)) {
                $request->images = [];
            }

            foreach ($request->images as $image) {
                $eduImage = new EduImage();
                $eduImage->edu_id = $edu->id;
                $eduImage->image = $image->store('edu-images', 'public');
                $eduImage->save();
            }

            DB::commit();

            return redirect()->route('tpj-edu.index')->with('success', 'Data berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error store TPJ', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('tpj-edu.index')->with('error', 'Data gagal ditambahkan');
        }
    }

    public function show(Edu $edu)
    {
        return Inertia::render('Edu/Show', [
            'title'         => 'Detail TPJ Edu',
            'resource'      => $edu->load(['images', 'creator', 'updater']),
            'breadcrumbs'    => [
                ['link' => route('tpj-edu.index'), 'title' => 'Informasi'],
                ['link' => route('tpj-edu.index'), 'title' => 'TPJ Edu'],
                ['link' => route('tpj-edu.show', $edu), 'title' => 'Detail'],
            ]
        ]);
    }

    public function edit(Edu $edu)
    {
        return Inertia::render('Edu/Form', [
            'title'         => 'Edit TPJ Edu',
            'resource'      => $edu->load(['images']),
            'breadcrumbs'    => [
                ['link' => route('tpj-edu.index'), 'title' => 'Informasi'],
                ['link' => route('tpj-edu.index'), 'title' => 'TPJ Edu'],
                ['link' => route('tpj-edu.edit', $edu), 'title' => 'Edit'],
            ]
        ]);
    }

    public function update(StoreEduRequest $request, Edu $edu)
    {
        try {
            DB::beginTransaction();

            $edu->title = $request->title;
            $edu->date = $request->date;
            $edu->is_active = $request->is_active;
            $edu->updated_by = auth()->id();
            $edu->save();

            $edu->images()->delete();

            if (!is_array($request->images)) {
                $request->images = [];
            }
            foreach ($request->images as $image) {
                $eduImage = new EduImage();
                $eduImage->edu_id = $edu->id;

                if ($image instanceof \Illuminate\Http\UploadedFile) {
                    $eduImage->image = $image->store('edu-images', 'public');
                } else {
                    $eduImage->image = RouteHelper::ImageUrlToDb($image);
                }

                $eduImage->save();
            }

            DB::commit();

            return redirect()->route('tpj-edu.index')->with('success', 'Data berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error update TPJ', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('tpj-edu.index')->with('error', 'Data gagal diubah');
        }
    }

    public function destroy(Edu $edu)
    {
        try {
            $edu->delete();
            EduImage::where('edu_id', $edu->id)->delete();

            return redirect()->route('tpj-edu.index')->with('success', 'Data berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error delete TPJ', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('tpj-edu.index')->with('error', 'Data gagal dihapus');
        }
    }
}
