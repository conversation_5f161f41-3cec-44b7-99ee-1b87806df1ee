<?php

namespace App\Http\Controllers\Backoffice\Informations;

use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Informations\StoreEventRequest;
use App\Http\Requests\IndexRequest;
use App\Libraries\Helpers\RouteHelper;
use App\Models\Informations\Event;
use App\Models\Informations\Filters\DateFilter;
use App\Models\Informations\Filters\StatusFilter;
use App\Services\ImageCompressor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class EventsController extends Controller
{
    public function index(IndexRequest $request)
    {
        $filters = [
            new StatusFilter(),
            new DateFilter(),
        ];

        $resources = Event::filterResource($request, ['title', 'content'], $filters)
            ->orderBy($request->get('sort', 'created_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('Event/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Event',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('events.index'), 'title' => 'Informasi'],
                ['link' => route('events.index'), 'title' => 'Event'],
            ],
        ]));
    }

    public function create()
    {
        return Inertia::render('Event/Form', [
            'title'         => 'Tambah Event',
            'breadcrumbs'    => [
                ['link' => route('events.index'), 'title' => 'Informasi'],
                ['link' => route('events.index'), 'title' => 'Event'],
                ['link' => route('events.create'), 'title' => 'Tambah'],
            ],
        ]);
    }

    public function store(StoreEventRequest $request)
    {
        $compressor = new ImageCompressor();
        try {
            DB::beginTransaction();

            $event = new Event();
            $event->fill($request->validated());
            $event->slug = Str::slug($event->title, '-');

            if ($request->hasFile('image')) {
                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'events-image'
                );
                $event->image = $result['path'];
            } else {
                $event->image = RouteHelper::ImageUrlToDb($request->image);
            }

            $event->created_by = auth()->id();
            $event->save();

            DB::commit();

            return redirect()->route('events.index')->with('success', 'Event berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error store event', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('events.index')->with('error', 'Event gagal ditambahkan');
        }
    }

    public function show(Event $event)
    {
        return Inertia::render('Event/Show', [
            'title'         => 'Detail Event',
            'resource'      => $event->load('creator', 'updater'),
            'breadcrumbs'    => [
                ['link' => route('events.index'), 'title' => 'Informasi'],
                ['link' => route('events.index'), 'title' => 'Event'],
                ['link' => route('events.show', $event), 'title' => 'Detail'],
            ]
        ]);
    }

    public function edit(Event $event)
    {
        return Inertia::render('Event/Form', [
            'title'         => 'Edit Event',
            'resource'      => $event,
            'breadcrumbs'    => [
                ['link' => route('events.index'), 'title' => 'Informasi'],
                ['link' => route('events.index'), 'title' => 'Event'],
                ['link' => route('events.edit', $event), 'title' => 'Edit'],
            ]
        ]);
    }

    public function update(StoreEventRequest $request, Event $event)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();
            $event->fill($request->validated());
            $event->slug = Str::slug($event->title, '-');
            $event->updated_by = auth()->id();

            if ($request->hasFile('image')) {
                if ($event->getRawOriginal('image') && Storage::disk('public')->exists($event->getRawOriginal('image'))) {
                    Storage::disk('public')->delete($event->getRawOriginal('image'));

                    Log::info('[EventsController.update] Event image deleted', [
                        'id' => $event->id,
                        'image' => $event->getRawOriginal('image'),
                    ]);
                }

                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'events-image'
                );
                $event->image = $result['path'];
            } else {
                $event->image = RouteHelper::ImageUrlToDb($request->image);
            }

            $event->save();
            DB::commit();

            return redirect()->route('events.index')->with('success', 'Event berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error update event', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('events.index')->with('error', 'Event gagal diubah');
        }
    }

    public function destroy(Event $event)
    {
        try {
            $event->delete();

            if ($event->getRawOriginal('image') && Storage::disk('public')->exists($event->getRawOriginal('image'))) {
                Storage::disk('public')->delete($event->getRawOriginal('image'));

                Log::info('[EventsController.destroy] Event image deleted', [
                    'id' => $event->id,
                    'image' => $event->getRawOriginal('image'),
                ]);
            }

            return redirect()->route('events.index')->with('success', 'Event berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error delete event', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('events.index')->with('error', 'Event gagal dihapus');
        }
    }
}
