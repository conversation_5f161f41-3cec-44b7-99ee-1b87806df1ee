<?php

namespace App\Http\Controllers\Backoffice\Informations;

use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\News\StoreNewsRequest;
use App\Http\Requests\IndexRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use App\Models\Informations\News;
use App\Libraries\Helpers\RouteHelper;
use App\Models\Informations\Enum\NewsCategory;
use App\Models\Informations\Filters\DateFilter;
use App\Models\Informations\Filters\NewsFilter;
use App\Models\Informations\Filters\StatusFilter;
use App\Services\ImageCompressor;
use Illuminate\Support\Facades\Storage;

class NewsController extends Controller
{
    public function index(IndexRequest $request)
    {
        $filters = [
            new NewsFilter(),
            new StatusFilter(),
            new DateFilter(),
        ];

        $resources = News::filterResource($request, ['title', 'content'], $filters)
            ->orderBy($request->get('sort', 'created_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('News/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Data Berita',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('news.index'), 'title' => 'Informasi'],
                ['link' => route('news.index'), 'title' => 'Berita'],
            ],
        ]));
    }

    public function create()
    {

        $categories = NewsCategory::getValues();
        $categories = array_map(function ($category) {
            return [
                'id' => $category,
                'name' => NewsCategory::getDescription($category),
            ];
        }, $categories);
        return Inertia::render('News/Form', [
            'title'         => 'Tambah Berita',
            'categories'      => $categories,
            'breadcrumbs'    => [
                ['link' => route('news.index'), 'title' => 'Informasi'],
                ['link' => route('news.index'), 'title' => 'Berita'],
                ['link' => route('news.create'), 'title' => 'Tambah'],
            ],
        ]);
    }

    public function store(StoreNewsRequest $request)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();
            $data = new News();
            $data->title = $request->title;
            $data->content = $request->content;
            $data->is_active = $request->is_active;
            $data->category = $request->category;
            $data->slug = $request->slug;
            $data->date = $request->date;
            $data->created_by = auth()->user()->id;
            $data->updated_by = auth()->user()->id;

            if ($request->hasFile('image')) {
                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'news-image'
                );
                $data->image = $result['path'];
            } else {
                $data->image = RouteHelper::ImageUrlToDb($request->image);
            }

            if ($request->hasFile('image_banner')) {
                $result = $compressor->compressAndStore(
                    $request->file('image_banner'),
                    'news-image-banner'
                );
                $data->image_banner = $result['path'];
            } else {
                $data->image_banner = RouteHelper::ImageUrlToDb($request->image_banner);
            }

            $data->save();

            DB::commit();

            return redirect()->route('news.index')->with('success', 'Data Berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error store News', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('news.index')->with('error', 'Data gagal ditambahkan');
        }
    }

    public function show(News $news)
    {
        $categories = NewsCategory::getValues();
        $categories = array_map(function ($category) {
            return [
                'id' => $category,
                'name' => NewsCategory::getDescription($category),
            ];
        }, $categories);
        return Inertia::render('News/Show', [
            'title'         => 'Detail Berita',
            'resource'      => $news->load('creator', 'updater'),
            'categories'      => $categories,
            'breadcrumbs'    => [
                ['link' => route('news.index'), 'title' => 'Informasi'],
                ['link' => route('news.index'), 'title' => 'Berita'],
                ['link' => route('news.show', $news), 'title' => 'Detail'],
            ],
        ]);
    }

    public function edit(News $news)
    {
        $resource           = $news;
        $categories = NewsCategory::getValues();
        $categories = array_map(function ($category) {
            return [
                'id' => $category,
                'name' => NewsCategory::getDescription($category),
            ];
        }, $categories);
        return Inertia::render('News/Form', [
            'title'         => 'Edit Berita',
            'resource'      => $resource,
            'categories'      => $categories,
            'breadcrumbs'    => [
                ['link' => route('news.index'), 'title' => 'Informasi'],
                ['link' => route('news.index'), 'title' => 'Berita'],
                ['link' => route('news.edit', $news), 'title' => 'Edit'],
            ],
        ]);
    }

    public function update(StoreNewsRequest $request, News $news)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();
            $news->title = $request->title;
            $news->content = $request->content;
            $news->category = $request->category;
            $news->is_active = $request->is_active;
            $news->slug = $request->slug;
            $news->date = $request->date;
            $news->updated_by = auth()->user()->id;
            $news->image = $request->hasFile('image') ?
                $request->image->store('news-image', 'public') :
                RouteHelper::ImageUrlToDb($request->image);
            $news->image_banner = $request->hasFile('image_banner') ?
                $request->image_banner->store('news-image-banner', 'public') :
                RouteHelper::ImageUrlToDb($request->image_banner);

            if ($request->hasFile('image')) {
                if ($news->getRawOriginal('image') && Storage::disk('public')->exists($news->getRawOriginal('image'))) {
                    Storage::disk('public')->delete($news->getRawOriginal('image'));

                    Log::info('[NewsController.update] News image deleted', [
                        'id' => $news->id,
                        'image' => $news->getRawOriginal('image'),
                    ]);
                }

                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'news-image'
                );
                $news->image = $result['path'];
            } else {
                $news->image = RouteHelper::ImageUrlToDb($request->image);
            }

            if ($request->hasFile('image_banner')) {
                if ($news->getRawOriginal('image_banner') && Storage::disk('public')->exists($news->getRawOriginal('image_banner'))) {
                    Storage::disk('public')->delete($news->getRawOriginal('image_banner'));

                    Log::info('[NewsController.update] News image banner deleted', [
                        'id' => $news->id,
                        'image_banner' => $news->getRawOriginal('image_banner'),
                    ]);
                }

                $result = $compressor->compressAndStore(
                    $request->file('image_banner'),
                    'news-image-banner'
                );
                $news->image_banner = $result['path'];
            } else {
                $news->image_banner = RouteHelper::ImageUrlToDb($request->image_banner);
            }
            $news->save();

            DB::commit();

            return redirect()->route('news.index')->with('success', 'Data Berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error update News', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('news.index')->with('error', 'Data gagal diubah');
        }
    }

    public function destroy(News $news)
    {
        try {

            $news->delete();

            if ($news->getRawOriginal('image') && Storage::disk('public')->exists($news->getRawOriginal('image'))) {
                Storage::disk('public')->delete($news->getRawOriginal('image'));

                Log::info('[NewsController.destroy] News image deleted', [
                    'id' => $news->id,
                    'image' => $news->getRawOriginal('image'),
                ]);
            }

            if ($news->getRawOriginal('image_banner') && Storage::disk('public')->exists($news->getRawOriginal('image_banner'))) {
                Storage::disk('public')->delete($news->getRawOriginal('image_banner'));

                Log::info('[NewsController.destroy] News image banner deleted', [
                    'id' => $news->id,
                    'image_banner' => $news->getRawOriginal('image_banner'),
                ]);
            }

            return redirect()->route('news.index')->with('success', 'Data Berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error delete News', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('news.index')->with('error', 'Data gagal dihapus');
        }
    }
}
