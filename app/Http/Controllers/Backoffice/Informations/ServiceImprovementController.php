<?php

namespace App\Http\Controllers\Backoffice\Informations;

use App\Actions\Notification\ServiceImprovementNotificationAction;
use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Informations\StoreServiceImprovementRequest;
use App\Http\Requests\IndexRequest;
use App\Libraries\Helpers\RouteHelper;
use App\Libraries\SimbioApi\GetServiceZone;
use App\Models\Informations\Filters\DateFilter;
use App\Models\Informations\Filters\StatusFilter;
use App\Models\Informations\Filters\ZoneFilter;
use App\Models\Informations\ServiceImprovement;
use App\Services\ImageCompressor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ServiceImprovementController extends Controller
{
    use ApiResponse;

    public function index(IndexRequest $request)
    {
        $filters = [
            new StatusFilter(),
            new DateFilter(),
            new ZoneFilter(),
        ];

        $resources = ServiceImprovement::filterResource($request, ['title'], $filters)
            ->orderBy($request->get('sort', 'created_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('ServiceImprovement/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Penyempurnaan Layanan',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('service-improvements.index'), 'title' => 'Informasi'],
                ['link' => route('service-improvements.index'), 'title' => 'Penyempurnaan Layanan'],
            ],
        ]));
    }

    public function create()
    {
        $zones = $this->getListZones();

        return Inertia::render('ServiceImprovement/Form', [
            'title'         => 'Tambah Penyempurnaan Layanan',
            'breadcrumbs'    => [
                ['link' => route('service-improvements.index'), 'title' => 'Informasi'],
                ['link' => route('service-improvements.index'), 'title' => 'Penyempurnaan Layanan'],
                ['link' => route('service-improvements.create'), 'title' => 'Tambah'],
            ],
            'zones' => $zones,
        ]);
    }

    public function store(StoreServiceImprovementRequest $request)
    {
        $compressor = new ImageCompressor();

        try {
            DB::beginTransaction();

            $serviceImprovement = new ServiceImprovement();
            $serviceImprovement->fill($request->validated());
            $serviceImprovement->slug = Str::slug($serviceImprovement->title, '-');

            if ($request->hasFile('image')) {
                $result = $compressor->compressAndStore(
                    $request->file('image'),
                    'service-improvements-image'
                );
                $serviceImprovement->image = $result['path'];
            } else {
                $serviceImprovement->image = RouteHelper::ImageUrlToDb($request->image);
            }

            $serviceImprovement->created_by = auth()->id();
            $serviceImprovement->zone_id = $request->zone_id;

            $zone = $this->getZoneById($request->zone_id);
            $serviceImprovement->zone_name = $zone ? $zone['name'] : null;
            $serviceImprovement->service_area_name = $zone ? $zone['service_area_name'] : null;

            $serviceImprovement->save();

            DB::commit();

            ServiceImprovementNotificationAction::run($serviceImprovement);

            return redirect()->route('service-improvements.index')->with('success', 'Penyempurnaan Layanan berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error store Penyempurnaan Layanan', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('service-improvements.index')->with('error', 'Penyempurnaan Layanan gagal ditambahkan');
        }
    }

    public function show(ServiceImprovement $serviceImprovement)
    {
        return Inertia::render('ServiceImprovement/Show', [
            'title'         => 'Detail Penyempurnaan Layanan',
            'resource'      => $serviceImprovement->load('creator', 'updater'),
            'breadcrumbs'    => [
                ['link' => route('service-improvements.index'), 'title' => 'Informasi'],
                ['link' => route('service-improvements.index'), 'title' => 'Penyempurnaan Layanan'],
                ['link' => route('service-improvements.show', $serviceImprovement), 'title' => 'Detail'],
            ]
        ]);
    }

    public function edit(ServiceImprovement $serviceImprovement)
    {
        $zones = $this->getListZones();

        return Inertia::render('ServiceImprovement/Form', [
            'title'         => 'Edit Penyempurnaan Layanan',
            'resource'      => $serviceImprovement,
            'breadcrumbs'    => [
                ['link' => route('service-improvements.index'), 'title' => 'Informasi'],
                ['link' => route('service-improvements.index'), 'title' => 'Penyempurnaan Layanan'],
                ['link' => route('service-improvements.edit', $serviceImprovement), 'title' => 'Edit'],
            ],
            'zones' => $zones,
        ]);
    }

    public function update(StoreServiceImprovementRequest $request, ServiceImprovement $serviceImprovement)
    {
        
    }

    public function destroy(ServiceImprovement $serviceImprovement)
    {
        try {
            $serviceImprovement->delete();

            if ($serviceImprovement->getRawOriginal('image') && Storage::disk('public')->exists($serviceImprovement->getRawOriginal('image'))) {
                Storage::disk('public')->delete($serviceImprovement->getRawOriginal('image'));

                Log::info('[ServiceImprovementController.destroy] Service improvement image deleted', [
                    'id' => $serviceImprovement->id,
                    'image' => $serviceImprovement->getRawOriginal('image'),
                ]);
            }

            return redirect()->route('service-improvements.index')->with('success', 'Penyempurnaan Layanan berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error delete Penyempurnaan Layanan', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('service-improvements.index')->with('error', 'Penyempurnaan Layanan gagal dihapus');
        }
    }

    public function sendNotification(ServiceImprovement $serviceImprovement)
    {
        try {
            ServiceImprovementNotificationAction::run($serviceImprovement);
            return $this->singleResponse('Notifikasi berhasil dikirim');
        } catch (\Exception $e) {
            Log::error('Error send notification', [$e->getMessage(), $e->getTraceAsString()]);

            return $this->errorResponse('Notifikasi gagal dikirim', 500);
        }
    }

    private function getListZones()
    {
        $getServiceZone = new GetServiceZone();
        $serviceZone = $getServiceZone->run();

        return collect($serviceZone)->sortBy('name')->values()->map(function ($item) {
            $label = $item['name'];
            if (isset($item['service_area_name']) && $item['service_area_name']) {
                $label .= ' - ' . $item['service_area_name'];
            }
            return [
                'id'    => $item['id'],
                'name'  => $label
            ];
        })->toArray();
    }

    private function getZoneById($id)
    {
        $getServiceZone = new GetServiceZone();
        $serviceZone = $getServiceZone->run();

        return collect($serviceZone)->firstWhere('id', $id);
    }

    /**
     * access by axios
     */
    public function getZones()
    {
        try {
            $zones = $this->getListZones();
            return $this->resourcesResponse('Data Zona Wilayah', $zones);
        } catch (\Exception $e) {
            Log::error('Error get zones', [$e->getMessage(), $e->getTraceAsString()]);
            return $this->errorResponse('Gagal mendapatkan data zona wilayah', 500);
        }
    }
}
