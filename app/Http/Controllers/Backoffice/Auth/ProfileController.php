<?php

namespace App\Http\Controllers\Backoffice\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use App\Libraries\Helpers\RouteHelper;

class ProfileController extends Controller
{
    public function profile()
    {
        $profile = auth()->user();
        return Inertia::render('Profile/Show', [
            'title' => 'Setting Profile',
            'resource' => $profile,
            'breadcrumbs' => [
                ['link' => route('user.profile'), 'title' => 'Setting Profile'],
                ['link' => route('user.profile'), 'title' => 'Detail Profile'],
            ]
        ]);
    }

    public function editProfile()
    {
        $profile = auth()->user();
        return Inertia::render('Profile/Form', [
            'title' => 'Setting Profile',
            'resource' => $profile,
            'breadcrumbs' => [
                ['link' => route('user.profile'), 'title' => 'Detail Profile'],
                ['link' => route('profile.edit'), 'title' => 'Edit Profile'],
            ]
        ]);
    }
    public function editPassword()
    {
        $profile = auth()->user();
        return Inertia::render('Profile/Form-Password', [
            'title' => 'Setting Profile',
            'resource' => $profile,
            'breadcrumbs' => [
                ['link' => route('user.profile'), 'title' => 'Detail Profile'],
                ['link' => route('profile.password'), 'title' => 'Ubah Password'],
            ]
        ]);
    }

    public function updateProfile(ProfileRequest $request)
    {
        try {
            $profile = auth()->user();
            $profile->name = $request->name;
            $profile->profile_photo_path = $request->hasFile('profile_photo_path') ?
                $request->profile_photo_path->store('user-photo', 'public') :
                RouteHelper::ImageUrlToDb($request->profile_photo_path);
            $profile->updated_at = now();
            $profile->save();
            return redirect()->route('user.profile')->with('success', 'Data sukses disimpan')
                ->with('code', 201);
        } catch (\Throwable $th) {
            Log::error($th);
            return redirect()->route('user.profile')->with('error', 'Data gagal disimpan ' . $th->getMessage())
                ->with('code', 400);
        }
    }

    public function updatePassword(PasswordRequest $request)
    {
        try {
            // check if the old password is correct
            if (!auth()->user()->checkPassword($request->old_password)) {
                return redirect()->route('profile.password')->with('error', 'Password lama tidak sesuai')
                    ->with('code', 400);
            }
            // update the password
            auth()->user()->password = bcrypt($request->password);
            auth()->user()->updated_at = now();
            auth()->user()->save();
            auth()->guard('web')->logout();
            return redirect()->route('login')->with('success', 'Password sukses diubah')
                ->with('code', 201);
        } catch (\Throwable $th) {
            Log::error($th);
            return redirect()->route('profile.password')->with('error', 'Password gagal diubah')
                ->with('code', 400);
        }
    }
}
