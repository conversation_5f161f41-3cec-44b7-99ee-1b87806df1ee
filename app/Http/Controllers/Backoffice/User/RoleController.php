<?php

namespace App\Http\Controllers\Backoffice\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Role\StoreRoleRequest;
use App\Http\Requests\Backoffice\Role\UpdateRoleRequest;
use App\Http\Requests\IndexRequestByName;
use App\Models\User\Filters\StatusFilter;
use App\Models\User\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends Controller
{
    public function index(IndexRequestByName $request)
    {
        $filters = [
            new StatusFilter(),
        ];

        $resources = Role::withCount('permissions')
            ->withCount(['users' => function ($query) {
                $query->where('is_active', true);
            }])
            ->when($request->search, function ($query) use ($request) {
                $query->where('name', 'like', "%{$request->search}%");
            })
            ->when($request->has('filter-status'), function ($query) use ($request) {
                $query->where('is_active', $request->get('filter-status'));
            })
            ->orderBy($request->get('sort', 'name'), $request->get('order', 'asc'))
            ->paginate($request->per_page);
        return Inertia::render('Role/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Data Role & Permission',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('roles.index'), 'title' => 'Manajemen Staff'],
                ['link' => route('roles.index'), 'title' => 'Role & Permission'],
            ],
        ]));
    }

    public function create()
    {
        $permissions = config('permissions');

        return Inertia::render('Role/Form', [
            'title'         => 'Tambah Role & Permission',
            'breadcrumbs'    => [
                ['link' => route('roles.index'), 'title' => 'Manajemen Staff'],
                ['link' => route('roles.index'), 'title' => 'Role & Permission'],
                ['link' => route('roles.create'), 'title' => 'Tambah'],
            ],
            'permissions'    => $permissions,
        ]);
    }

    public function store(StoreRoleRequest $request)
    {
        try {
            DB::beginTransaction();

            $role = DB::table('roles')->insertGetId([
                'name'          => $request->name,
                'is_active'     => $request->is_active,
                'guard_name'    => 'web',
                'created_at'    => now(),
                'updated_at'    => now(),
                'created_by'    => auth()->id(),
            ]);

            $role = Role::findOrFail($role);

            foreach ($request->permissions as $permission) {
                Permission::firstOrCreate(['name'  => $permission]);
            }

            $role->syncPermissions($request->permissions);

            Log::info('[Role] create');

            DB::commit();

            return redirect()->route('roles.index')->with('success', 'Role berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error store role', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('roles.index')->with('error', 'Role gagal ditambahkan');
        }
    }

    public function show(Role $role)
    {
        $permissions        = config('permissions');
        $resource           = $role->load('permissions');
        $savedPermissions   = $resource->permissions->pluck('name')->toArray();
        $createdBy          = User::select('id', 'name')->find($resource->created_by);
        $updatedBy          = User::select('id', 'name')->find($resource->updated_by);

        return Inertia::render('Role/Show', [
            'title'         => 'Detail Role & Permission',
            'resource'      => $resource,
            'breadcrumbs'    => [
                ['link' => route('roles.index'), 'title' => 'Manajemen Staff'],
                ['link' => route('roles.index'), 'title' => 'Role & Permission'],
                ['link' => route('roles.show', $role), 'title' => 'Detail'],
            ],
            'permissions'    => $permissions,
            'savedPermissions' => $savedPermissions,
            'createdBy'     => $createdBy,
            'updatedBy'     => $updatedBy,
        ]);
    }

    public function edit(Role $role)
    {
        $permissions        = config('permissions');
        $resource           = $role->load('permissions');
        $savedPermissions   = $resource->permissions->pluck('name')->toArray();

        return Inertia::render('Role/Form', [
            'title'         => 'Edit Role & Permission',
            'resource'      => $role,
            'breadcrumbs'    => [
                ['link' => route('roles.index'), 'title' => 'Managemen Staff'],
                ['link' => route('roles.index'), 'title' => 'Role & Permission'],
                ['link' => route('roles.edit', $role), 'title' => 'Edit'],
            ],
            'permissions'    => $permissions,
            'savedPermissions' => $savedPermissions,
        ]);
    }

    public function update(UpdateRoleRequest $request, Role $role)
    {
        if ($role->users()->exists() && $request->is_active == false) {
            return redirect()->route('roles.index')->with('error', 'Role tidak bisa dinonaktifkan karena masih digunakan oleh pengguna');
        }
        try {
            DB::beginTransaction();

            DB::table('roles')
                ->where('id', $role->id)
                ->update([
                    'name'          => $request->name,
                    'is_active'     => $request->is_active,
                    'updated_at'    => now(),
                    'updated_by'    => auth()->id(),
                ]);

            foreach ($request->permissions as $permission) {
                Permission::firstOrCreate(['name'  => $permission]);
            }

            $role->syncPermissions($request->permissions);

            Log::info('[Role] update');

            DB::commit();

            return redirect()->route('roles.index')->with('success', 'Role berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error update role', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('roles.index')->with('error', 'Role gagal diubah');
        }
    }

    public function destroy(Role $role)
    {
        try {
            if ($role->users()->exists()) {
                return redirect()->route('roles.index')->with('error', 'Role tidak bisa dihapus karena masih digunakan');
            }

            $role->delete();
            Log::info('[Role] delete');

            return redirect()->route('roles.index')->with('success', 'Role berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error delete role', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('roles.index')->with('error', 'Role gagal dihapus');
        }
    }
}
