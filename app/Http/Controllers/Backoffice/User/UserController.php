<?php

namespace App\Http\Controllers\Backoffice\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\User\StoreUserRequest;
use App\Http\Requests\Backoffice\User\UpdateUserRequest;
use App\Http\Requests\IndexRequestByName;
use App\Libraries\SimpegApi\SimpegApi;
use App\Models\User\Filters\RoleFilter;
use App\Models\User\Filters\StatusFilter;
use App\Models\User\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(IndexRequestByName $request)
    {
        $filters = [
            new RoleFilter(),
            new StatusFilter(),
        ];

        $queryRoles = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->join('users', 'model_has_roles.model_id', '=', 'users.id')
            ->select('users.id', DB::raw('GROUP_CONCAT(roles.name) as roles'))
            ->where('model_type', 'App\Models\User\User')
            ->groupBy('users.id');

        $resources = User::select('users.*', 'roles.roles')
            ->when($request->has('search') && $request->filled('search'), function ($query) use ($request) {
                $query->where('users.name', 'like', '%' . $request->search . '%')
                    ->orWhere('users.email', 'like', '%' . $request->search . '%');
            })
            ->when($request->has('filter-role') && $request->filled('filter-role'), function ($query) use ($request) {
                $query->whereIn('users.id', function ($query) use ($request) {
                    $query->select('model_id')
                        ->from('model_has_roles')
                        ->where('role_id', $request->get('filter-role'))
                        ->where('model_type', 'App\Models\User\User');
                });
            })
            ->when($request->has('filter-status') && $request->filled('filter-status'), function ($query) use ($request) {
                $query->where('users.is_active', $request->get('filter-status'));
            })
            ->leftJoinSub($queryRoles, 'roles', function ($join) {
                $join->on('users.id', '=', 'roles.id');
            })
            ->orderBy($request->get('sort', 'users.name'), $request->get('order', 'asc'))
            ->paginate($request->per_page);

        return Inertia::render('User/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Manajemen Staff',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('users.index'), 'title' => 'Manajemen Staff'],
                ['link' => route('users.index'), 'title' => 'Staff'],
            ],
        ]));
    }

    public function create()
    {
        $employees = $this->getEmployeeList();

        return Inertia::render('User/Form', [
            'title'         => 'Tambah Staff',
            'breadcrumbs'   => [
                ['link' => route('users.index'), 'title' => 'Manajemen Staff'],
                ['link' => route('users.index'), 'title' => 'Staff'],
                ['link' => route('users.create'), 'title' => 'Tambah'],
            ],
            'roles'         => Role::where('is_active', true)->get()->map(function ($item) {
                return [
                    'value' => $item->id,
                    'label' => $item->name,
                ];
            }),
            'employees'     => $employees,
        ]);
    }

    public function store(StoreUserRequest $request)
    {
        try {
            DB::beginTransaction();

            $employee = $this->getEmployeeByName($request->name);
            if (!$employee || !isset($employee['user']['email'])) {
                return redirect()->back()->with('error', 'Pegawai tidak ditemukan. Silakan pilih pegawai yang valid.');
            }
            $passwordText = $request->password ?? 'Password123#';

            $user = new User();
            $user->fill($request->validated());
            $user->email = $employee['user']['email'] ?? null;
            $user->password = bcrypt($passwordText);
            $user->created_at = now();
            $user->updated_at = now();
            $user->save();

            $role = Role::find($request->role_id);
            $user->assignRole($role);

            DB::commit();
            return redirect()->route('users.index')->with('success', 'Data Berhasil Disimpan!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('error store user', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(User $user)
    {
        return Inertia::render('User/Show', [
            'title'         => 'Detail Staff',
            'breadcrumbs'    => [
                ['link' => route('users.index'), 'title' => 'Manajemen Staff'],
                ['link' => route('users.index'), 'title' => 'Staff'],
                ['link' => route('users.show', $user->id), 'title' => 'Detail'],
            ],
            'resource'      => $user->load('roles'),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $employees = $this->getEmployeeList();
        $selectedEmployee = $this->getEmployeeByName($user->name);
        if ($selectedEmployee) {
            array_unshift($employees, [
                'value' => $selectedEmployee['full_name'],
                'label' => $selectedEmployee['full_name'],
                'email' => $selectedEmployee['user']['email'] ?? null,
            ]);
        }

        return Inertia::render('User/Form', [
            'title'         => 'Edit Staff',
            'breadcrumbs'    => [
                ['link' => route('users.index'), 'title' => 'Manajemen Staff'],
                ['link' => route('users.index'), 'title' => 'Staff'],
                ['link' => route('users.edit', $user->id), 'title' => 'Edit'],
            ],
            'resource'      => $user->load('roles'),
            'roles'         => Role::where('is_active', true)->get()->map(function ($item) {
                return [
                    'value' => $item->id,
                    'label' => $item->name,
                ];
            }),
            'employees'     => $employees,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        try {
            DB::beginTransaction();

            if ($request->has('password') && !empty($request->password)) {
                $user->password = bcrypt($request->password);
            }

            $user->update($request->validated());
            $user->updated_at = now();
            $user->save();

            $user->syncRoles([]);

            $role = Role::find($request->role_id);
            $user->assignRole($role);

            DB::commit();

            return redirect()->route('users.index')->with('success', 'Data Berhasil Diperbarui!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('error update user', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        $user->delete();

        return redirect()->route('users.index')->with('success', 'Data Berhasil Dihapus!');
    }

    private function getExisingUserList()
    {
        return User::select('name')->get()->map(function ($user) {
            return [
                'value' => $user->name,
            ];
        })->toArray();
    }

    private function getEmployeeList()
    {
        $simpegApi = new SimpegApi();
        $employees = $simpegApi->getFilteredEmployee();

        $employeeList = collect($employees)->map(function ($employee) {
            return [
                'value' => $employee['full_name'],
                'label' => $employee['full_name'],
                'email' => $employee['user']['email'] ?? null,
            ];
        });

        $existingUsers = $this->getExisingUserList();
        $existingUserNames = collect($existingUsers)->pluck('value')->toArray();
        return $employeeList->filter(function ($employee) use ($existingUserNames) {
            return !in_array($employee['value'], $existingUserNames);
        })->values()->toArray();
    }

    private function getEmployeeByName($name)
    {
        $simpegApi = new SimpegApi();
        $employees = $simpegApi->getFilteredEmployee();

        return collect($employees)->firstWhere('full_name', $name) ?? null;
    }
}
