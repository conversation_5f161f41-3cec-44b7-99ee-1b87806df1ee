<?php

namespace App\Http\Controllers\Backoffice\Customer;

use App\Http\Controllers\Controller;
use App\Http\Requests\IndexRequestByName;
use App\Models\Customer\CustomerChannel;
use Inertia\Inertia;

class CustomerChannelController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(IndexRequestByName $request)
    {
        $filters = [];

        $resources = CustomerChannel::filterResource($request, [
            'name',
            'fullname',
            'channel',
            'external_customer_id',
            'id_transaction',
            'no_ktp'
        ], $filters)
            ->orderBy($request->get('sort', 'name'), $request->get('order', 'asc'))
            ->paginate($request->per_page);

        return Inertia::render('CustomerChannel/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Customer',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('customer-channels.index'), 'title' => 'Manajemen User'],
                ['link' => route('customer-channels.index'), 'title' => 'Customer'],
            ],
        ]));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(CustomerChannel $customerChannel)
    {
        return Inertia::render('CustomerChannel/Show', [
            'title'         => 'Detail Customer',
            'breadcrumbs'    => [
                ['link' => route('customer-channels.index'), 'title' => 'Manajemen User'],
                ['link' => route('customer-channels.index'), 'title' => 'Customer'],
                ['link' => route('customer-channels.show', $customerChannel->id), 'title' => 'Detail'],
            ],
            'resource'      => $customerChannel,
        ]);
    }
}
