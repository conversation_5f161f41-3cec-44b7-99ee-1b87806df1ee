<?php

namespace App\Http\Controllers\Backoffice\Customer;

use App\Actions\CustomerChannel\ChangeName\UpdateChangeNameAction;
use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\IndexRequestByUpdated;
use App\Libraries\SimbioApi\ChangeName\GetCustomerChangeNameTrackingApi;
use App\Models\Customer\CustomerChannelChange;
use App\Models\Customer\Filters\ChangeNameStatusFilter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class ChangeNameController extends Controller
{
    use ApiResponse;

    public function index(IndexRequestByUpdated $request)
    {
        $filters = [
            new ChangeNameStatusFilter(),
        ];

        $resources = CustomerChannelChange::filterResource($request, ['id_transaction', 'old_name', 'new_name', 'reason'], $filters)
            ->orderBy($request->get('sort', 'updated_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('ChangeName/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Balik Nama',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('change-names.index'), 'title' => 'Layanan'],
                ['link' => route('change-names.index'), 'title' => 'Balik Nama'],
            ],
        ]));
    }

    public function show(Request $request, CustomerChannelChange $changeName)
    {
        return Inertia::render('ChangeName/Show', [
            'title'         => 'Detail Balik Nama',
            'resource'      => $changeName->load('activity'),
            'breadcrumbs'    => [
                ['link' => route('change-names.index'), 'title' => 'Layanan'],
                ['link' => route('change-names.index'), 'title' => 'Balik Nama'],
                ['link' => route('change-names.show', $changeName), 'title' => 'Detail'],
            ],
        ]);
    }

    /**
     * access by axios
     */
    public function getTracking($externalCustomerId)
    {
        try {
            $getCustomerChangeNameTracking = new GetCustomerChangeNameTrackingApi();
            $trackings = $getCustomerChangeNameTracking->run($externalCustomerId);

            return $this->singleResponse(
                'Change name tracking',
                $trackings
            );
        } catch (\Exception $e) {
            Log::error('[ChangeNameController] Error fetching change name tracking', [
                'error' => $e->getMessage(),
            ]);
            return $this->errorResponse(
                'Failed to fetch change name tracking: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * access by axios
     */
    public function sync($id)
    {
        $changeName = CustomerChannelChange::where('id', $id)->first();
        if (!$changeName) {
            Log::error('[ChangeNameController.sync] Change name not found', [
                'id' => $id,
            ]);
            return $this->errorResponse(
                'Balik Nama tidak ditemukan',
                400
            );
        }

        $sync = new UpdateChangeNameAction();
        $result = $sync->handle($changeName->id_transaction);

        Log::info('[ChangeNameController.sync] Sync Change Name Job', [
            $result
        ]);

        if ($result['status'] === 'error') {
            return $this->errorResponse(
                $result['message'],
                $result['code']
            );
        }

        return $this->singleResponse(
            'Data balik nama berhasil disinkronkan',
            $changeName
        );
    }
}
