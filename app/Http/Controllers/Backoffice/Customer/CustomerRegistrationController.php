<?php

namespace App\Http\Controllers\Backoffice\Customer;

use App\Actions\Customer\UpsertCustomerChannelAction;
use App\Actions\CustomerChannel\Registration\GetCustomerRegistrationPaymentAction;
use App\Actions\CustomerChannel\Registration\GetCustomerRegistrationTrackingAction;
use App\DTO\UpsertCustomerChannelDTO;
use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\IndexRequestByUpdated;
use App\Models\Activity\Activity;
use App\Models\Customer\CustomerChannel;
use App\Models\Customer\Filters\LandStatusFilter;
use App\Models\Customer\Filters\RegistrationStatusFilter;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class CustomerRegistrationController extends Controller
{
    use ApiResponse;

    public function index(IndexRequestByUpdated $request)
    {
        $filters = [
            new RegistrationStatusFilter(),
            new LandStatusFilter(),
        ];

        $resources = CustomerChannel::select([
            'customer_channels.id',
            'customer_channels.id_transaction',
            'customer_channels.fullname',
            'customer_channels.name',
            'customer_channels.address_ktp',
            'customer_channels.land_status',
            'customer_channels.land_document',
            'customer_channels.used_for',
            'customer_channels.status_registration',
            'customer_channels.no_spl',
            'customer_channels.created_at',
            'customer_channels.updated_at',
            'activities.id as activity_id'
        ])
            ->join('activities', function ($join) {
                $join->on('activities.subject_id', '=', 'customer_channels.id')
                    ->where('activities.subject_type', '=', CustomerChannel::class);
            })
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('customer_channels.name', 'like', "%{$request->search}%")
                        ->orWhere('customer_channels.channel', 'like', "%{$request->search}%")
                        ->orWhere('customer_channels.id_transaction', 'like', "%{$request->search}%")
                        ->orWhere('customer_channels.address_ktp', 'like', "%{$request->search}%")
                        ->orWhere('customer_channels.address', 'like', "%{$request->search}%");
                });
            })
            ->when($request->has('filter-status_registration') && $request->filled('filter-status_registration'), function ($query) use ($request) {
                $query->where('customer_channels.status_registration', $request->get('filter-status_registration'));
            })
            ->when($request->has('filter-land_status') && $request->filled('filter-land_status'), function ($query) use ($request) {
                $query->where('customer_channels.land_status', $request->get('filter-land_status'));
            })
            ->orderBy($request->get('sort', 'customer_channels.created_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('CustomerRegistration/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Pasang Baru',
            'resources'     => $resources,
            'breadcrumbs'   => [
                ['link' => route('customer-registrations.index'), 'title' => 'Layanan'],
                ['link' => route('customer-registrations.index'), 'title' => 'Pasang Baru'],
            ],
        ]));
    }

    public function show(Activity $activity)
    {
        if ($activity->subject_type !== CustomerChannel::class) {
            abort(404);
        }

        $activity->load('subject', 'customer');

        return Inertia::render('CustomerRegistration/Show', [
            'title'         => 'Detail Pasang Baru',
            'resource'      => $activity,
            'breadcrumbs'   => [
                ['link' => route('customer-registrations.index'), 'title' => 'Layanan'],
                ['link' => route('customer-registrations.index'), 'title' => 'Pasang Baru'],
                ['link' => route('customer-registrations.show', $activity), 'title' => 'Detail'],
            ],
        ]);
    }

    /**
     * access by axios
     */
    public function getPayment($id)
    {
        $payment = new GetCustomerRegistrationPaymentAction();
        return $payment->handle($id);
    }

    /**
     * access by axios
     */
    public function getTracking($id)
    {
        $tracking = new GetCustomerRegistrationTrackingAction();
        return $tracking->handle($id);
    }

    /**
     * access by axios
     */
    public function sync($id)
    {
        $customerChannel = CustomerChannel::where('id', $id)->first();

        if (!$customerChannel) {
            Log::error('[CustomerChannelController.sync] Customer channel not found', [
                'id' => $id,
            ]);
            return $this->errorResponse(
                'Saluran tidak ditemukan',
                400
            );
        }

        $dto = new UpsertCustomerChannelDTO(
            externalCustomerId: $customerChannel->external_customer_id,
            channelId: null,
            customerId: 0
        );

        $result = app(UpsertCustomerChannelAction::class)->handle($dto);

        if ($result->status === 'error') {
            Log::error('[CustomerRegistrationController] Sync Customer Channel failed', [
                'message'   => $result->message,
                'code'      => $result->code,
            ]);
            return $this->errorResponse(
                $result->message,
                $result->code
            );
        }

        return $this->singleResponse(
            'Data saluran berhasil disinkronkan',
            $customerChannel
        );
    }
}
