<?php

namespace App\Http\Controllers\Backoffice\Customer;

use App\Actions\Notification\VerificationStatusNotificationAction;
use App\Events\Customer\CustomerVerificationEvent;
use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Customer\UpdateCustomerVerificationStatusRequest;
use App\Http\Requests\IndexRequestByUpdated;
use App\Models\Customer\Customer;
use App\Models\Customer\Enum\PointType;
use App\Models\Customer\Enum\VerificationStatus;
use App\Models\Customer\Filters\VerificationStatusFilter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(IndexRequestByUpdated $request)
    {
        $filters = [
            new VerificationStatusFilter(),
        ];

        $resources = Customer::select('id', 'email', 'name', 'no_ktp', 'phone', 'verification_status')
            ->filterResource($request, ['name', 'email', 'profession', 'phone', 'no_ktp'], $filters)
            ->withCount('channels')
            ->orderBy($request->get('sort', 'updated_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('Customer/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'User',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('customers.index'), 'title' => 'Manajemen User'],
                ['link' => route('customers.index'), 'title' => 'User'],
            ],
        ]));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Customer $customer)
    {
        return Inertia::render('Customer/Show', [
            'title'         => 'Detail User',
            'breadcrumbs'    => [
                ['link' => route('customers.index'), 'title' => 'Manajemen User'],
                ['link' => route('customers.index'), 'title' => 'User'],
                ['link' => route('customers.show', $customer->id), 'title' => 'Detail'],
            ],
            'resource'      => $customer->load([
                'province',
                'regency',
                'district',
                'village',
                'verificator',
                'channels',
            ]),
        ]);
    }

    public function updateVerificationStatus(UpdateCustomerVerificationStatusRequest $request, Customer $customer)
    {
        if ($customer->verification_status === VerificationStatus::VERIFIED->value) {
            return redirect()->route('customers.show', $customer->id)->with('error', 'Status verifikasi sudah diverifikasi');
        }

        DB::beginTransaction();

        try {
            if ($request->verification_status === VerificationStatus::VERIFIED->value) {
                $customer->point += 100;
                $customer->pointHistories()->create([
                    'point'         => 100,
                    'balance'       => $customer->point,
                    'description'   => 'Poin diberikan karena verifikasi akun',
                    'type'          => PointType::DEBIT->value,
                ]);
            }

            $customer->verification_status = $request->verification_status;
            $customer->verified_at = now();
            $customer->verified_by = auth('web')->user()->id;
            $customer->save();

            DB::commit();

            VerificationStatusNotificationAction::run($customer);

            Log::info('[CustomerController] Customer verification status updated', [
                'customer_id'           => $customer->id,
                'verification_status'   => $customer->verification_status,
                'updated_by'            => auth('web')->user()->id,
            ]);

            return redirect()->route('customers.show', $customer->id)->with('success', 'Status verifikasi berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('[CustomerController] Failed to update customer verification status', [
                'customer_id'   => $customer->id,
                'error'         => $e->getMessage(),
                'request'       => $request->all(),
                'trace'         => $e->getTraceAsString(),
            ]);
            return redirect()->route('customers.show', $customer->id)->with('error', 'Gagal mengubah status verifikasi: ' . $e->getMessage());
        } finally {
            CustomerVerificationEvent::dispatch();
        }
    }
}
