<?php

namespace App\Http\Controllers\Backoffice\Master;

use App\Http\Controllers\Api\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Master\StoreConsultationCategoryRequest;
use App\Http\Requests\IndexRequestByName;
use App\Models\Consultation\ConsultationCategory;
use App\Models\Informations\Filters\StatusFilter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class ConsultationCategoryController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     */
    public function index(IndexRequestByName $request)
    {
        $filters = [
            new StatusFilter()
        ];

        $resources = ConsultationCategory::filterResource($request, ['name'], $filters)
            ->orderBy($request->get('sort', 'name'), $request->get('order', 'asc'))
            ->paginate($request->per_page);

        return Inertia::render('ConsultationCategory/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Kategori Konsultasi',
            'resources'     => $resources,
            'breadcrumbs'    => [
                ['link' => route('consultation-categories.index'), 'title' => 'Master Layanan'],
                ['link' => route('consultation-categories.index'), 'title' => 'Kategori Konsultasi'],
            ],
        ]));
    }

    public function create()
    {
        return Inertia::render('ConsultationCategory/Form', [
            'title'         => 'Tambah Kategori Konsultasi',
            'breadcrumbs'    => [
                ['link' => route('consultation-categories.index'), 'title' => 'Master Layanan'],
                ['link' => route('consultation-categories.index'), 'title' => 'Kategori Konsultasi'],
                ['link' => route('consultation-categories.create'), 'title' => 'Tambah'],
            ],
        ]);
    }

    public function store(StoreConsultationCategoryRequest $request)
    {
        try {
            DB::beginTransaction();

            $consultationCategory = new ConsultationCategory();
            $consultationCategory->fill($request->validated());
            $consultationCategory->save();

            DB::commit();

            return redirect()->route('consultation-categories.index')->with('success', 'Kategori Konsultasi berhasil ditambahkan');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error store Kategori Konsultasi', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('consultation-categories.index')->with('error', 'Kategori Konsultasi gagal ditambahkan');
        }
    }

    public function show(ConsultationCategory $consultationCategory)
    {
        return Inertia::render('ConsultationCategory/Show', [
            'title'         => 'Detail Kategori Konsultasi',
            'resource'      => $consultationCategory,
            'breadcrumbs'    => [
                ['link' => route('consultation-categories.index'), 'title' => 'Master Layanan'],
                ['link' => route('consultation-categories.index'), 'title' => 'Kategori Konsultasi'],
                ['link' => route('consultation-categories.show', $consultationCategory), 'title' => 'Detail'],
            ]
        ]);
    }

    public function edit(ConsultationCategory $consultationCategory)
    {
        return Inertia::render('ConsultationCategory/Form', [
            'title'         => 'Edit ConsultationCategory',
            'resource'      => $consultationCategory,
            'breadcrumbs'    => [
                ['link' => route('consultation-categories.index'), 'title' => 'Master Layanan'],
                ['link' => route('consultation-categories.index'), 'title' => 'Kategori Konsultasi'],
                ['link' => route('consultation-categories.edit', $consultationCategory), 'title' => 'Edit'],
            ]
        ]);
    }

    public function update(StoreConsultationCategoryRequest $request, ConsultationCategory $consultationCategory)
    {
        try {
            DB::beginTransaction();
            $consultationCategory->fill($request->validated());
            $consultationCategory->save();
            DB::commit();

            return redirect()->route('consultation-categories.index')->with('success', 'Kategori Konsultasi berhasil diubah');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error update Kategori Konsultasi', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('consultation-categories.index')->with('error', 'Kategori Konsultasi gagal diubah');
        }
    }

    public function destroy(ConsultationCategory $consultationCategory)
    {
        try {
            $consultationCategory->delete();

            return redirect()->route('consultation-categories.index')->with('success', 'Kategori Konsultasi berhasil dihapus');
        } catch (\Exception $e) {
            Log::error('Error delete Kategori Konsultasi', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('consultation-categories.index')->with('error', 'Kategori Konsultasi gagal dihapus');
        }
    }

    /**
     * Access by axios
     */
    public function getConsultationCategories(Request $request)
    {
        $resources = ConsultationCategory::filterResource($request, ['name'])
            ->select(['id', 'name'])
            ->orderBy('name', 'asc')
            ->get();

        return $this->resourcesResponse('Kategori Konsultasi berhasil diambil', $resources);
    }
}
