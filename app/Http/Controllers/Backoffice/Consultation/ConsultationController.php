<?php

namespace App\Http\Controllers\Backoffice\Consultation;

use App\Http\Controllers\Controller;
use App\Http\Requests\Backoffice\Consultation\UpdateConsultationStatusRequest;
use App\Http\Requests\IndexRequest;
use App\Models\Consultation\Consultation;
use App\Models\Consultation\Enum\ConsultationMedia;
use App\Models\Consultation\Enum\ConsultationStatus;
use App\Models\Consultation\Filters\ConsultationCategoryFilter;
use App\Models\Consultation\Filters\ConsultationStatusFilter;
use App\Models\Consultation\Filters\CreatedFilter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class ConsultationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(IndexRequest $request)
    {
        $filters = [
            new ConsultationCategoryFilter(),
            new ConsultationStatusFilter(),
            new CreatedFilter(),
        ];

        $resources = Consultation::filterResource($request, [], $filters)
            ->select([
                'consultations.*',
                'consultation_categories.name as consultation_category_name',
                'customers.name as customer_name',
                'customers.email as customer_email',
                'customer_channels.fullname as channel_fullname',
                'customer_channels.channel as channel',
                'activities.code as activity_code',
            ])
            ->when($request->has('search'), function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('consultations.title', 'like', '%' . $request->search . '%')
                        ->orWhere('consultations.description', 'like', '%' . $request->search . '%')
                        ->orWhere('activities.code', 'like', '%' . $request->search . '%')
                        ->orWhere('customers.name', 'like', '%' . $request->search . '%')
                        ->orWhere('customer_channels.fullname', 'like', '%' . $request->search . '%')
                        ->orWhere('customer_channels.channel', 'like', '%' . $request->search . '%');
                });
            })
            ->join('activities', function ($join) {
                $join->on('activities.subject_id', '=', 'consultations.id')
                    ->where('activities.subject_type', '=', Consultation::class);
            })
            ->join('consultation_categories', 'consultation_categories.id', '=', 'consultations.consultation_category_id')
            ->join('customers', 'customers.id', '=', 'consultations.customer_id')
            ->leftJoin('customer_channels', function ($join) {
                $join->on('customer_channels.external_customer_id', '=', 'consultations.external_customer_id')
                    ->whereNull('customer_channels.deleted_at');
            })
            ->orderBy($request->get('sort', 'created_at'), $request->get('order', 'desc'))
            ->paginate($request->per_page);

        return Inertia::render('Consultation/Index', array_merge($this->simpleResourceAdditionalData($request, $resources, $filters), [
            'title'         => 'Konsultasi',
            'resources'     => $resources,
            'breadcrumbs'   => [
                ['link' => route('consultations.index'), 'title' => 'Layanan'],
                ['link' => route('consultations.index'), 'title' => 'Konsultasi'],
            ],
        ]));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Consultation $consultation)
    {
        return Inertia::render('Consultation/Show', [
            'title'         => 'Detail Konsultasi',
            'breadcrumbs'   => [
                ['link' => route('consultations.index'), 'title' => 'Layanan'],
                ['link' => route('consultations.index'), 'title' => 'Konsultasi'],
                ['link' => route('consultations.show', $consultation->id), 'title' => 'Detail'],
            ],
            'resource'      => $consultation->load([
                'customer',
                'customerChannel',
                'category',
                'activity.timelines',
            ]),
        ]);
    }

    public function updateStatus(UpdateConsultationStatusRequest $request, Consultation $consultation)
    {
        DB::beginTransaction();
        if ($consultation->status === $request->status) {
            return redirect()->route('consultations.show', $consultation->id)
                ->with('info', 'Status Konsultasi sudah sesuai dengan yang dipilih');
        }

        try {
            $consultation->status = $request->status;
            if ($request->status === ConsultationStatus::CANCELED_BY_ADMIN->value) {
                $consultation->cancel_reason = $request->cancel_reason;
            } elseif ($request->status === ConsultationStatus::PROGRESS->value) {
                $consultation->media = ConsultationMedia::ONLINE->value;
                $consultation->note = $request->note;
                $consultation->approved_consultation_time = $request->approved_consultation_time;
            }
            $consultation->save();

            Log::info('[ConsultationController] Consultation status updated', [
                'consultation_id' => $consultation->id,
                'status' => $consultation->status,
                'updated_by' => auth()->user()->id,
            ]);

            DB::commit();

            if ($request->is_detail) {
                return redirect()->route('consultations.show', $consultation->id)
                    ->with('success', 'Status Konsultasi berhasil diperbarui');
            }

            return redirect()->route('consultations.index')
                ->with('success', 'Status Konsultasi berhasil diperbarui');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('[ConsultationController] Error updating consultation status', [$e->getMessage(), $e->getTraceAsString()]);

            return redirect()->route('consultations.show', $consultation->id)
                ->with('error', 'Gagal memperbarui status Konsultasi');
        }
    }
}
