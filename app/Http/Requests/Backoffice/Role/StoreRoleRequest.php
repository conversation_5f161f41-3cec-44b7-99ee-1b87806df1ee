<?php

namespace App\Http\Requests\Backoffice\Role;

use Illuminate\Foundation\Http\FormRequest;

class StoreRoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'          => 'required|string|max:255|unique:roles,name',
            'is_active' => 'required|boolean',
            'permissions'   => 'required|array',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'Nama role harus diisi.',
            'name.unique'   => 'Role dengan nama ini sudah ada.',
            'name.max'      => 'Nama role tidak boleh lebih dari 255 karakter.',
            'is_active.required' => 'Status aktif harus dipilih.',
            'permissions.required' => 'Setidaknya satu permission harus dipilih.',
        ];
    }
}
