<?php

namespace App\Http\Requests\Backoffice\Master;

use Illuminate\Foundation\Http\FormRequest;

class StoreConsultationCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'         => 'required',
            'is_active'     => 'required|boolean',
        ];
    }

    public function messages()
    {
        return [
            'name.required'        => 'Nama kategori konsultasi harus diisi.',
            'is_active.required'   => 'Status aktif harus dipilih.',
        ];
    }
}
