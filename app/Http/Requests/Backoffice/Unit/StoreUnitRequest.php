<?php

namespace App\Http\Requests\Backoffice\Unit;

use Illuminate\Foundation\Http\FormRequest;

class StoreUnitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title'         => 'required|string|max:255',
            'address'       => 'required|string',
            'longitude'     => 'nullable|string|max:255',
            'latitude'      => 'nullable|string|max:255',
            'radius'        => 'nullable|numeric|min:0',
            'is_active'     => 'required|boolean',
        ];
    }

    public function messages()
    {
        return [
            'title.required' => 'Harap masukkan Nama Unit Anda, kolom ini tidak boleh kosong!',
            'title.string' => 'Nama Unit harus berupa string',
            'title.max' => 'Nama Unit maksimal 255 karakter',
            'address.required' => 'Harap masukkan <PERSON>, kolom ini tidak boleh kosong!',
            'address.string' => 'Lokasi harus berupa string',
            'longitude.string' => 'Longitude harus berupa string',
            'longitude.max' => 'Longitude maksimal 255 karakter',
            'latitude.string' => 'Latitude harus berupa string',
            'latitude.max' => 'Latitude maksimal 255 karakter',
            'radius.numeric' => 'Radius harus berupa angka',
            'radius.min' => 'Radius minimal 0',
            'is_active.required' => 'Status aktif wajib diisi',
            'is_active.boolean' => 'Status aktif harus berupa boolean',
        ];
    }
}
