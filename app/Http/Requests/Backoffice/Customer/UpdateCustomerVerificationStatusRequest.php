<?php

namespace App\Http\Requests\Backoffice\Customer;

use App\Models\Customer\Enum\VerificationStatus;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCustomerVerificationStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('web')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'verification_status' => 'required|in:' . implode(',', array_map(fn($case) => $case->value, VerificationStatus::cases())),
        ];
    }
}
