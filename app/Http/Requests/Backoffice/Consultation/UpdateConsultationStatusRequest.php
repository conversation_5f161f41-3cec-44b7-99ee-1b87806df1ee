<?php

namespace App\Http\Requests\Backoffice\Consultation;

use App\Models\Consultation\Consultation;
use App\Models\Consultation\Enum\ConsultationMedia;
use App\Models\Consultation\Enum\ConsultationStatus;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class UpdateConsultationStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('web')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id'        => 'required|exists:consultations,id',
            'status'    => [
                'required',
                'in:' . implode(',', array_map(fn($case) => $case->value, ConsultationStatus::cases()))
            ],
            // 'media'     => [
            //     'nullable',
            //     'required_if:status,' . ConsultationStatus::PROGRESS->value,
            //     'in:' . implode(',', array_map(fn($case) => $case->value, ConsultationMedia::cases()))
            // ],
            'approved_consultation_time' => [
                'nullable',
                'required_if:status,' . ConsultationStatus::PROGRESS->value,
                'date_format:Y-m-d H:i',
                function ($attribute, $value, $fail) {
                    if (!$value || $this->status !== ConsultationStatus::PROGRESS->value) {
                        return;
                    }

                    $consultation = Consultation::find($this->id);
                    if (!$consultation) {
                        return;
                    }

                    $consultationTime = Carbon::parse($consultation->consultation_time);
                    $approvedTime = Carbon::createFromFormat('Y-m-d H:i', $value);

                    if ($consultationTime->isPast()) {
                        if ($approvedTime->isPast()) {
                            $fail('Harus setelah waktu sekarang.');
                        }
                    } else {
                        if ($approvedTime->lessThan($consultationTime)) {
                            $fail('Harus setelah waktu konsultasi yang diajukan.');
                        }
                    }
                }
            ],
            'note'      => [
                'nullable',
                'required_if:status,' . ConsultationStatus::PROGRESS->value,
                'string',
                'max:10000',
                'url',
            ],
            'cancel_reason' => [
                'nullable',
                'required_if:status,' . ConsultationStatus::CANCELED_BY_ADMIN->value,
                'string',
                'max:255'
            ],
        ];
    }

    public function messages()
    {
        return [
            'status.required' => 'Status konsultasi harus diisi.',
            'status.in' => 'Status konsultasi tidak valid.',
            'media.required_if' => 'Media harus diisi jika status adalah Sedang Diproses.',
            'media.in' => 'Media tidak valid. Pilih antara Online atau Offline.',
            'approved_consultation_time.required_if' => 'Waktu konsultasi harus diisi jika status adalah Sedang Diproses.',
            'approved_consultation_time.date_format' => 'Waktu konsultasi harus berupa tanggal dan waktu.',
            'note.required_if' => 'Link harus diisi jika status adalah Sedang Diproses.',
            'note.string' => 'Link harus berupa teks.',
            'note.max' => 'Link tidak boleh lebih dari 10000 karakter.',
            'note.url' => 'Link harus berupa URL yang valid.',
            'cancel_reason.required_if' => 'Alasan pembatalan harus diisi jika status adalah Dibatalkan oleh Admin.',
            'cancel_reason.string' => 'Alasan pembatalan harus berupa teks.',
            'cancel_reason.max' => 'Alasan pembatalan tidak boleh lebih dari 255 karakter.',
        ];
    }
}
