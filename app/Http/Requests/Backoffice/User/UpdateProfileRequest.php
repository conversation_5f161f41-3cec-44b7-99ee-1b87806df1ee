<?php

namespace App\Http\Requests\Backoffice\User;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\UploadedFile;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required',
            'profile_photo_path' => [
                'nullable',
                function ($attribute, $value, $fail) {
                    if (!is_string($value) && !($value instanceof UploadedFile)) {
                        $fail('The ' . $attribute . ' must either be a string or file.');
                    }
                    if ($value instanceof UploadedFile) {
                        $allowedMimes = ['image/jpeg', 'image/png'];
                        if (!in_array($value->getMimeType(), $allowedMimes)) {
                            $fail('<PERSON>ya type JPG, PNG yang diperbolehkan');
                            return;
                        }
                        $maxSize = 1024; // in KB
                        if ($value->getSize() / 1024 > $maxSize) {
                            $fail('File tidak boleh lebih dari 1 MB');
                        }
                    }
                }
            ]
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Harap lengkapi data Nama',
        ];
    }
}
