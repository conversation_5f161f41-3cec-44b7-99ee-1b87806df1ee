<?php

namespace App\Http\Requests\Backoffice\User;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'old_password' => 'required',
            'password' => 'required|min:8',
            'password_confirmation' => 'required|min:8|same:password',
        ];
    }

    public function messages()
    {
        return [
            'old_password.required' => 'Harap lengkapi data Password Lama',
            'password.required' => 'Harap lengkapi data Password Baru',
            'password.confirmed' => 'Konfirmasi Password Baru tidak cocok',
            'password.min' => 'Password Baru minimal :min karakter',
            'password_confirmation.required' => 'Harap lengkapi data Konfirmasi Password Baru',
            'password_confirmation.min' => 'Konfirmasi Password Baru minimal :min karakter',
            'password_confirmation.same' => 'Konfirmasi Password Baru tidak cocok',
        ];
    }
}
