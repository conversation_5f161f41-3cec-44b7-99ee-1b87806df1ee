<?php

namespace App\Http\Requests\Backoffice\User;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'role_id'   => 'required|exists:roles,id',
            'is_active' => 'required|boolean',
            'password_confirmation' => [
                'nullable',
                'min:8',
                'regex:/[a-z]/',
                'regex:/[A-Z]/',
                'regex:/[0-9]/',
                'regex:/[@$!%*#?&]/',
                'required_with:password',
            ],
            'password'              => [
                'nullable',
                'confirmed',
                'required_with:password_confirmation',
            ],
        ];
    }

    public function messages()
    {
        return [
            'role_id.required' => 'Harap masukkan Role Anda, kolom ini tidak boleh kosong!',
            'role_id.exists' => 'Role tidak valid',
            'is_active.required' => 'Status aktif wajib diisi',
            'is_active.boolean' => 'Status aktif harus berupa boolean',
            'password_confirmation.min' => 'Password minimal 8 karakter',
            'password_confirmation.regex' => 'Password harus mengandung huruf kecil, huruf besar, angka, dan karakter khusus',
            'password_confirmation.required_with' => 'Password diperlukan jika konfirmasi password diisi',
            'password.confirmed' => 'Password tidak cocok',
            'password.required_with' => 'Konfirmasi Password diperlukan jika password diisi',
            'password.min' => 'Password minimal 8 karakter',
            'password.regex' => 'Password harus mengandung huruf kecil, huruf besar, angka, dan karakter khusus',
        ];
    }
}
