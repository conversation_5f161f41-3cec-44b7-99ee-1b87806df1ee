<?php

namespace App\Http\Requests\Backoffice\Informations;

use App\Rules\FileUploadRule;
use App\Rules\FileUploadSizeRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreSplashRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title'         => ['required'],
            'is_active'     => ['required', 'boolean'],
            'image'         => ['required', new FileUploadRule(['image']), new FileUploadSizeRule('file', 2048000, 1000, 'Gambar')],
        ];
    }

    public function messages()
    {
        return [
            'title.required'        => 'Judul harus diisi.',
            'is_active.required'    => 'Status aktif harus dipilih.',
            'image.required'        => 'Gambar harus diunggah.',
            'image.image'           => 'File yang diunggah harus berupa gambar.',
            'image.max'             => 'Ukuran gambar tidak boleh lebih dari 2MB.',
            'image.mimes'           => 'File yang diunggah harus berupa gambar dengan format: jpeg, png, jpg',
        ];
    }
}
