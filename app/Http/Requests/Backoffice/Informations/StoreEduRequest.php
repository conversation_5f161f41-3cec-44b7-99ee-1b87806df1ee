<?php

namespace App\Http\Requests\Backoffice\Informations;

use App\Rules\FileUploadRule;
use App\Rules\FileUploadSizeRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreEduRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title'         => ['required'],
            'date'          => 'required|date|after_or_equal:today',
            'is_active'     => 'required|boolean',
            'images'        => ['nullable', 'array', 'max:10'],
            'images.*'      => ['required', new FileUploadRule(['image']), new FileUploadSizeRule('file', 2048000, 1000, 'Gambar')],
        ];
    }

    public function messages()
    {
        return [
            'title.required'        => 'Judul edukasi harus diisi.',
            'date.required'         => 'Tanggal edukasi harus diisi.',
            'date.date'             => 'Tanggal harus berupa tanggal yang valid.',
            'date.after_or_equal'   => 'Tanggal edukasi tidak boleh sebelum hari ini.',
            'is_active.required'    => 'Status aktif harus dipilih.',
            'image.mimes'           => 'File yang diunggah harus berupa gambar dengan format: jpeg, png, jpg',
            'images.array'          => 'Gambar harus berupa array.',
            'images.max'            => 'Maksimal 10 gambar yang dapat diunggah.',
            'images.*.image'        => 'Setiap gambar harus berupa file gambar.',
            'images.*.mimes'        => 'Setiap gambar harus berupa file dengan format: jpeg, png, jpg',
            'images.*.max'          => 'Setiap gambar tidak boleh lebih dari 2MB.',
        ];
    }
}
