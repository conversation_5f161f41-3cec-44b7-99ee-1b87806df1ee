<?php

namespace App\Http\Requests\Backoffice\News;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Rules\FileUploadRule;
use App\Models\Informations\Enum\NewsCategory;
use App\Rules\FileUploadSizeRule;

class StoreNewsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title'         => ['required', Rule::unique('news', 'title')->whereNull('deleted_at')->ignore($this->id), 'max:255'],
            'content'       => ['required', 'max:10000'],
            'category'      => 'required|string|' . Rule::in(NewsCategory::getValues()),
            'date'          => 'required|date|before_or_equal:today',
            'is_active'     => 'required|boolean',
            'image'         => ['required', new FileUploadRule(['image']), new FileUploadSizeRule('file', 2048000, 1000, 'Gambar')],
            'image_banner'  => ['nullable',new FileUploadRule(['image']), new FileUploadSizeRule('file', 5048000, 1000, 'Gambar Banner')],
        ];
    }

    public function messages()
    {
        return [
            'title.required'    => 'Judul berita harus diisi.',
            'title.unique'      => 'Judul berita sudah ada.',
            'content.required'  => 'Konten berita harus diisi.',
            'is_active.required' => 'Status aktif harus dipilih.',
            'image.required'     => 'Gambar berita harus diunggah.',
            'image.image'       => 'File yang diunggah harus berupa gambar.',
            'image.max'         => 'Ukuran gambar tidak boleh lebih dari 2MB.',
            'image.mimes'       => 'File yang diunggah harus berupa gambar dengan format: jpeg, png, jpg',
            'image_banner.image'=> 'File yang diunggah harus berupa gambar.',
            'image_banner.max'  => 'Ukuran gambar banner tidak boleh lebih dari 2MB.',
            'image_banner.mimes'=> 'File yang diunggah harus berupa gambar dengan format: jpeg, png, jpg',
            'category.required' => 'Kategori berita harus dipilih.',
            'category.in'       => 'Kategori berita tidak valid.',
            'date.required'     => 'Tanggal berita harus diisi.',
            'date.date'         => 'Tanggal harus berupa tanggal yang valid.',
            'date.before_or_equal' => 'Tanggal berita tidak boleh lebih dari hari ini.',
        ];
    }
}
