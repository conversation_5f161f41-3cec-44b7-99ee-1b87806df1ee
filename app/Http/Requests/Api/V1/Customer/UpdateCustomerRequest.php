<?php

namespace App\Http\Requests\Api\V1\Customer;

use App\Models\Location\District;
use App\Models\Location\Province;
use App\Models\Location\Regency;
use App\Models\Location\Village;
use App\Rules\FileUploadRule;
use App\Rules\FileUploadSizeRule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCustomerRequest extends FormRequest
{
    private ?Province $province;
    private ?Regency $regency;
    private ?District $district;
    private ?Village $village;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'          => ['required'],
            'phone'         => ['required', 'min:11', 'max:15', 'regex:/^(\+62|62|0)[0-9]{8,15}$/'],
            'gender'        => ['required', 'in:MALE,FEMALE'],
            'profession'    => ['required', 'max:100'],
            'address'       => ['nullable', 'max:190'],
            'avatar'        => ['nullable', new FileUploadRule('image'), new FileUploadSizeRule('file', 2048000, 10000, 'Avatar')],
            'birth_date'    => ['nullable', 'date_format:Y-m-d'],
            'province_id'   => 'required|exists:provinces,id',
            'regency_id'    => 'required|exists:regencies,id',
            'district_id'   => 'required|exists:districts,id',
            'village_id'    => 'required|exists:villages,id',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Nama lengkap harus diisi.',
            'phone.required' => 'Nomor telepon harus diisi.',
            'phone.min' => 'Nomor telepon minimal 11 karakter.',
            'phone.max' => 'Nomor telepon maksimal 15 karakter.',
            'phone.regex' => 'Format nomor telepon tidak valid. Gunakan format 62 atau 0 di depan nomor telepon.',
            'gender.required' => 'Jenis kelamin harus dipilih.',
            'gender.in' => 'Jenis kelamin tidak valid.',
            'profession.required' => 'Profesi harus diisi.',
            'profession.max' => 'Profesi tidak boleh lebih dari 100 karakter.',
            'address.max' => 'Alamat tidak boleh lebih dari 190 karakter.',
            'birth_date.date_format' => 'Format tanggal lahir tidak valid. Gunakan format YYYY-MM-DD.',
            'province_id.required' => 'Provinsi harus dipilih.',
            'province_id.exists' => 'Provinsi yang dipilih tidak valid.',
            'regency_id.required' => 'Kabupaten/Kota harus dipilih.',
            'regency_id.exists' => 'Kabupaten/Kota yang dipilih tidak valid.',
            'district_id.required' => 'Kecamatan harus dipilih.',
            'district_id.exists' => 'Kecamatan yang dipilih tidak valid.',
            'village_id.required' => 'Desa/Kelurahan harus dipilih.',
            'village_id.exists' => 'Desa/Kelurahan yang dipilih tidak valid.'
        ];
    }

    public function withValidator(\Illuminate\Validation\Validator $validator)
    {
        $validator->after(function ($validator) {
            if ($validator->errors()->isNotEmpty()) {
                return;
            }

            $validated = $validator->validated();

            $this->province = Province::find($validated['province_id']);

            $this->regency = Regency::find($validated['regency_id']);
            if ($this->province && $this->regency) {
                if ($this->regency->province_unique_id !== $this->province->unique_id) {
                    $validator->errors()->add('regency_id', 'Kabupaten/Kota yang dipilih tidak sesuai dengan Provinsi yang dipilih.');
                    return;
                }
            }

            $this->district = District::find($validated['district_id']);
            if ($this->regency && $this->district) {
                if ($this->district->regency_unique_id !== $this->regency->unique_id) {
                    $validator->errors()->add('district_id', 'Kecamatan yang dipilih tidak sesuai dengan Kabupaten/Kota yang dipilih.');
                    return;
                }
            }

            if (isset($validated['village_id'])) {
                $this->village = Village::find($validated['village_id']);
                if ($this->district && $this->village) {
                    if ($this->village->district_unique_id !== $this->district->unique_id) {
                        $validator->errors()->add('village_id', 'Desa/Kelurahan yang dipilih tidak sesuai dengan Kecamatan yang dipilih.');
                    }
                }
            }
        });
    }
}
