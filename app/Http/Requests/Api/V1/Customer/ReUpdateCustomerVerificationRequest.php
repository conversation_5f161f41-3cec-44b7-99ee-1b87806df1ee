<?php

namespace App\Http\Requests\Api\V1\Customer;

use App\Rules\FileUploadRule;
use App\Rules\FileUploadSizeRule;
use Illuminate\Foundation\Http\FormRequest;

class ReUpdateCustomerVerificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $customer = auth('api')->user();
        return [
            'no_ktp'        => ['required', 'max:20', 'regex:/^[0-9]{16}$/', 'unique:customers,no_ktp,' . $customer->id . ',id,deleted_at,NULL'],
            'id_card_photo' => ['required', new FileUploadRule('image'), new FileUploadSizeRule('file', 2048000, 10000, 'KTP')],
        ];
    }

    public function messages()
    {
        return [
            'no_ktp.required' => 'Nomor KTP harus diisi.',
            'no_ktp.max' => 'Nomor KTP tidak boleh lebih dari 20 karakter.',
            'no_ktp.regex' => 'Format Nomor KTP tidak valid. Gunakan format 16 digit angka.',
            'no_ktp.unique' => 'Nomor KTP sudah terdaftar. Silakan gunakan nomor KTP lain.',
            'id_card_photo.required' => 'Foto KTP harus diunggah.',
            'id_card_photo.image' => 'Foto KTP harus berupa gambar.',
            'id_card_photo.file' => 'Foto KTP harus berupa file.',
            'id_card_photo.size' => 'Ukuran file Foto KTP tidak boleh lebih dari 10 MB.',
            'id_card_photo.max' => 'Ukuran file Foto KTP tidak boleh lebih dari 10 MB.',
        ];
    }
}
