<?php

namespace App\Http\Requests\Api\V1\Consultation;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class StoreConsultationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('api')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'external_customer_id' => [
                'nullable',
                'max:1000000',
                'min:1',
                function ($attribute, $value, $fail) {
                    $exists = DB::table('customer_channels')
                        ->join('customer_customer_channel', 'customer_channels.id', '=', 'customer_customer_channel.customer_channel_id')
                        ->where('customer_channels.external_customer_id', $value)
                        ->where('customer_customer_channel.customer_id', auth('api')->user()->id)
                        ->whereNull('customer_channels.deleted_at')
                        ->exists();

                    if (!$exists) {
                        $fail('The selected external customer id is invalid.');
                    }
                },
            ],
            'consultation_category_id' => [
                'required',
                'exists:consultation_categories,id,is_active,1'
            ],
            'title' => [
                'required',
                'string',
                'max:255'
            ],
            'description' => [
                'nullable',
                'string',
                'max:10000'
            ],
            'consultation_time' => [
                'required',
                'date_format:Y-m-d H:i',
                'after_or_equal:now'
            ],
        ];
    }

    public function messages()
    {
        return [
            'external_customer_id.exists' => 'Customer ID tidak valid',
            'consultation_category_id.exists' => 'Kategori konsultasi tidak valid',
            'title.required' => 'Judul konsultasi harus diisi',
            'title.string' => 'Judul konsultasi harus berupa string',
            'title.max' => 'Judul konsultasi tidak boleh lebih dari 255 karakter',
            'description.string' => 'Deskripsi harus berupa string',
            'description.max' => 'Deskripsi tidak boleh lebih dari 10000 karakter',
            'consultation_time.required' => 'Waktu konsultasi harus diisi',
            'consultation_time.date_format' => 'Waktu konsultasi harus berupa tanggal dan waktu',
            'consultation_time.after_or_equal' => 'Waktu konsultasi harus setelah waktu sekarang',
        ];
    }
}
