<?php

namespace App\Http\Requests\Api\V1\CustomerChannel;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class ChangeNamePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('api')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'external_customer_id' => [
                'nullable',
                'max:1000000',
                'min:1',
                function ($attribute, $value, $fail) {
                    $exists = DB::table('customer_channels')
                        ->join('customer_customer_channel', 'customer_channels.id', '=', 'customer_customer_channel.customer_channel_id')
                        ->where('customer_channels.external_customer_id', $value)
                        ->where('customer_customer_channel.customer_id', auth('api')->user()->id)
                        ->whereNull('customer_channels.deleted_at')
                        ->exists();

                    if (!$exists) {
                        $fail('External ID Pelanggan tidak valid');
                    }
                },
            ],
            'id_transaction' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    $exists = DB::table('customer_channel_changes')
                        ->where('id_transaction', $value)
                        ->where('customer_id', auth('api')->user()->id)
                        ->where('external_customer_id', $this->external_customer_id)
                        ->whereNull('deleted_at')
                        ->exists();

                    if (!$exists) {
                        $fail('ID transaksi tidak valid');
                    }
                },
            ],
            'payment_photo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:10240',
        ];
    }

    public function messages()
    {
        return [
            'external_customer_id.exists' => 'Customer ID tidak valid',
            'id_transaction.required' => 'ID transaksi harus diisi',
            'id_transaction.string' => 'ID transaksi harus berupa string',
            'id_transaction.max' => 'ID transaksi maksimal 255 karakter',
            'id_transaction.exists' => 'ID transaksi tidak valid',
            'payment_photo.required' => 'Foto bukti pembayaran harus diunggah',
            'payment_photo.image' => 'Foto bukti pembayaran harus berupa gambar',
            'payment_photo.mimes' => 'Foto bukti pembayaran harus berformat jpeg, png, jpg, gif, atau svg',
            'payment_photo.max' => 'Foto bukti pembayaran maksimal 10MB',
        ];
    }
}
