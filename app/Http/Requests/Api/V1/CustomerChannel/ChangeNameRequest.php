<?php

namespace App\Http\Requests\Api\V1\CustomerChannel;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class ChangeNameRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('api')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'external_customer_id'  => [
                'required',
                'max:1000000',
                'min:1',
                function ($attribute, $value, $fail) {
                    $exists = DB::table('customer_channels')
                        ->join('customer_customer_channel', 'customer_channels.id', '=', 'customer_customer_channel.customer_channel_id')
                        ->where('customer_channels.external_customer_id', $value)
                        ->where('customer_customer_channel.customer_id', auth('api')->user()->id)
                        ->where('customer_channels.status', 'Active')
                        ->whereNull('customer_channels.deleted_at')
                        ->exists();

                    if (!$exists) {
                        $fail('The selected external customer id is invalid.');
                    }
                },
            ],
            'change_name_category_id' => 'required|integer|max:1000000',
            'new_name'              => 'required|string|max:255',
            'description'           => 'nullable|string|max:500',
        ];
    }
}
