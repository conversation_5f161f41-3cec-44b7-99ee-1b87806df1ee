<?php

namespace App\Http\Requests\Api\V1\CustomerChannel;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerChannelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('api')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // 'external_customer_id' => 'required|max:1000000|min:1',
            'channel' => 'required|string|min:1|max:100'
        ];
    }
}
