<?php

namespace App\Http\Requests\Api\V1\CustomerChannel;

use App\Libraries\SimbioApi\Village\GetSubDistrictVillageApi;
use App\Rules\FileUploadRule;
use App\Rules\FileUploadSizeRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerRegistrationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth('api')->check();
    }

    public function rules(): array
    {
        return [
            'fullname'          => ['required', 'string', 'max:255'],
            'address_ktp'       => ['required', 'string', 'max:500'],
            'no_ktp'            => ['required', 'digits:16', 'regex:/^[0-9]{10,16}$/'],
            'profession'        => ['required', 'string', 'max:100'],
            'telephone'         => ['required', 'min:11', 'max:15', 'regex:/^(\+62|62|0)[0-9]{8,15}$/'],
            'name'              => ['required', 'string', 'max:255'],
            'address'           => ['required', 'string', 'max:500'],
            'id_sub_district' => [
                'required',
                function ($attribute, $value, $fail) {
                    try {
                        $api = new GetSubDistrictVillageApi();
                        $data = $api->run();

                        $subDistrictExists = collect($data)->contains('id', $value);

                        if (!$subDistrictExists) {
                            $fail('Kecamatan tidak valid.');
                        }
                    } catch (\Exception $e) {
                        $fail('Gagal memvalidasi kecamatan.');
                    }
                }
            ],
            'id_village' => [
                'required',
                function ($attribute, $value, $fail) {
                    try {
                        $api = new GetSubDistrictVillageApi();
                        $data = $api->run();

                        $villageExists = false;
                        $subDistrictId = $this->input('id_sub_district');

                        foreach ($data as $subDistrict) {
                            if ($subDistrict['id'] == $subDistrictId) {
                                $villageExists = collect($subDistrict['village'])->contains('id', $value);
                                break;
                            }
                        }

                        if (!$villageExists) {
                            $fail('Kelurahan/Desa tidak valid atau tidak sesuai dengan kecamatan yang dipilih.');
                        }
                    } catch (\Exception $e) {
                        $fail('Gagal memvalidasi kelurahan.');
                    }
                }
            ],
            'land_status'       => ['required', 'string', 'max:100'],
            'land_document'     => ['required', 'string', 'max:100'],
            'occupant'          => ['required', 'integer', 'min:1'],
            'used_for'          => ['required', 'string', 'max:100'],
            'etc'               => ['nullable', 'string', 'max:500'],
            'electrical_power'  => ['required', 'integer', 'min:1'],
            'building_area'     => ['required', 'numeric', 'min:1'],
            'long_registration' => ['required', 'numeric'],
            'lat_registration'  => ['required', 'numeric'],
            'family_card_photo' => ['required', 'image', 'mimes:jpeg,png,jpg,gif,svg', new FileUploadRule('image'), new FileUploadSizeRule('file', 2048000, 10000, 'Kartu Keluarga')],
            'house_image_registration' => ['required', 'image', 'mimes:jpeg,png,jpg,gif,svg', new FileUploadRule('image'), new FileUploadSizeRule('file', 2048000, 10000, 'Foto Rumah')],
        ];
    }

    public function messages(): array
    {
        return [
            'fullname.required' => 'Nama lengkap harus diisi.',
            'fullname.string' => 'Nama lengkap harus berupa teks.',
            'fullname.max' => 'Nama lengkap maksimal 255 karakter.',

            'address_ktp.required' => 'Alamat KTP harus diisi.',
            'address_ktp.string' => 'Alamat KTP harus berupa teks.',
            'address_ktp.max' => 'Alamat KTP maksimal 500 karakter.',

            'no_ktp.required' => 'Nomor KTP harus diisi.',
            'no_ktp.digits' => 'Nomor KTP harus 16 digit.',
            'no_ktp.regex' => 'Format Nomor KTP tidak valid. Gunakan format 10-16 digit angka.',

            'profession.required' => 'Pekerjaan harus diisi.',
            'profession.string' => 'Pekerjaan harus berupa teks.',
            'profession.max' => 'Pekerjaan maksimal 100 karakter.',

            'telephone.required' => 'Nomor telepon harus diisi.',
            'telephone.min' => 'Nomor telepon minimal 11 karakter.',
            'telephone.max' => 'Nomor telepon maksimal 15 karakter.',
            'telephone.regex' => 'Format nomor telepon tidak valid.',

            'name.required' => 'Nama harus diisi.',
            'name.string' => 'Nama harus berupa teks.',
            'name.max' => 'Nama maksimal 255 karakter.',

            'address.required' => 'Alamat harus diisi.',
            'address.string' => 'Alamat harus berupa teks.',
            'address.max' => 'Alamat maksimal 500 karakter.',

            'id_sub_district.required' => 'Kecamatan harus dipilih.',

            'id_village.required' => 'Kelurahan/Desa harus dipilih.',

            'land_status.required' => 'Status tanah harus diisi.',
            'land_status.string' => 'Status tanah harus berupa teks.',
            'land_status.max' => 'Status tanah maksimal 100 karakter.',

            'land_document.required' => 'Dokumen tanah harus diisi.',
            'land_document.string' => 'Dokumen tanah harus berupa teks.',
            'land_document.max' => 'Dokumen tanah maksimal 100 karakter.',

            'occupant.required' => 'Jumlah penghuni harus diisi.',
            'occupant.integer' => 'Jumlah penghuni harus berupa angka.',
            'occupant.min' => 'Jumlah penghuni minimal 1.',

            'used_for.required' => 'Kegunaan harus diisi.',
            'used_for.string' => 'Kegunaan harus berupa teks.',
            'used_for.max' => 'Kegunaan maksimal 100 karakter.',

            'etc.string' => 'Keterangan tambahan harus berupa teks.',
            'etc.max' => 'Keterangan tambahan maksimal 500 karakter.',

            'electrical_power.required' => 'Daya listrik harus diisi.',
            'electrical_power.integer' => 'Daya listrik harus berupa angka.',
            'electrical_power.min' => 'Daya listrik minimal 1.',

            'building_area.required' => 'Luas bangunan harus diisi.',
            'building_area.numeric' => 'Luas bangunan harus berupa angka.',
            'building_area.min' => 'Luas bangunan minimal 1.',

            'long_registration.required' => 'Longitude harus diisi.',
            'long_registration.numeric' => 'Longitude harus berupa angka.',

            'lat_registration.required' => 'Latitude harus diisi.',
            'lat_registration.numeric' => 'Latitude harus berupa angka.',

            'family_card_photo.required' => 'Foto Kartu Keluarga harus diupload.',
            'family_card_photo.image' => 'Foto Kartu Keluarga harus berupa gambar.',
            'family_card_photo.mimes' => 'Foto Kartu Keluarga harus berformat jpeg, png, jpg, gif, atau svg.',

            'house_image_registration.required' => 'Foto rumah harus diupload.',
            'house_image_registration.image' => 'Foto rumah harus berupa gambar.',
            'house_image_registration.mimes' => 'Foto rumah harus berformat jpeg, png, jpg, gif, atau svg.',
        ];
    }
}
