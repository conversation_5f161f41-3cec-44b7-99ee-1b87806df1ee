<?php

namespace App\Http\Middleware;

use App\Models\Consultation\Consultation;
use App\Models\Consultation\Enum\ConsultationStatus;
use App\Models\Customer\Customer;
use App\Models\Customer\Enum\VerificationStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        $auth = Auth::guard('web');

        $consultationCount = $this->getWaitingConsultationCount();

        $waitingVerificationCount = $this->getWaitingVerificationCount();

        return array_merge(parent::share($request), [
            'auth' => function () use ($auth) {
                return [
                    'user' => $auth->user() ? [
                        'id'            => $auth->user()->id,
                        'name'          => $auth->user()->name,
                        'email'         => $auth->user()->email,
                        'permissions'   => $auth->user()->getPermissionsViaRoles()->pluck('name'),
                        'roles'         => $auth->user()->getRoleNames(),
                        'image'         => $auth->user()->profile_photo_path,
                        'guard'         => 'web'
                    ] : null,
                ];
            },
            'flash' => function () {
                return [
                    'success'   => Session::get('success'),
                    'error'     => Session::get('error'),
                ];
            },
            'countSummary' => [
                'waitingConsultationCount' => $consultationCount,
                'waitingVerificationCount' => $waitingVerificationCount,
            ],
        ]);
    }

    private function getWaitingConsultationCount()
    {
        return Cache::remember('waiting_consultation_count', now()->addMinutes(15), function () {
            Log::info('[HandleInertiaRequests] Cache consultationCount Missed');
            return Consultation::where('status', ConsultationStatus::WAITING->value)->count();
        });
    }

    private function getWaitingVerificationCount()
    {
        return Cache::remember('waiting_verification_count', now()->addMinutes(15), function () {
            Log::info('[HandleInertiaRequests] Cache waitingVerificationCount Missed');
            return Customer::where('verification_status', VerificationStatus::WAITING->value)->count();
        });
    }
}
