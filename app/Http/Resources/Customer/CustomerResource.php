<?php

namespace App\Http\Resources\Customer;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'            => $this->id,
            'name'          => $this->name,
            'email'         => $this->email,
            'password'      => $this->password,
            'phone'         => $this->phone,
            'gender'        => $this->gender,
            'birth_date'    => $this->birth_date,
            'age'           => $this->age,
            'gender_description'    => $this->gender_description,
            'is_active'             => $this->is_active,
            'is_active_description' => $this->is_active_description,
            'address'               => $this->address,
            'avatar'                => $this->avatar,
            'profession'            => $this->profession,
            'no_ktp'                => $this->no_ktp,
            'id_card_photo'         => $this->id_card_photo,
            'verification_status'   => $this->verification_status,
            'verification_status_description'   => $this->verification_status_description,
            'verified_at'           => $this->verified_at,
            'verified_by'           => $this->verified_by,
            'last_login'            => $this->last_login,
            'last_login_ip'         => $this->last_login_ip,
            'last_login_useragent'  => $this->last_login_useragent,
            'last_login_url'        => $this->last_login_url,
            'settings'              => $this->settings,
            'player_ids'            => $this->player_ids,
            'province_id'           => $this->province_id,
            'regency_id'            => $this->regency_id,
            'district_id'           => $this->district_id,
            'village_id'            => $this->village_id,
            'point'                 => $this->point,
            'is_profile_complete'   => $this->is_profile_complete,
            'province'              => $this->whenLoaded('province', function () {
                return [
                    'id'   => $this->province->id,
                    'name' => $this->province->name,
                ];
            }),
            'regency'               => $this->whenLoaded('regency', function () {
                return [
                    'id'   => $this->regency->id,
                    'name' => $this->regency->name,
                ];
            }),
            'district'              => $this->whenLoaded('district', function () {
                return [
                    'id'   => $this->district->id,
                    'name' => $this->district->name,
                ];
            }),
            'village'               => $this->whenLoaded('village', function () {
                return [
                    'id'   => $this->village->id,
                    'name' => $this->village->name,
                ];
            }),
        ];
    }
}
