<?php

namespace App\Http\Resources\CustomerChannel;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerChannelDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                        => $this->id ?? '',
            'external_customer_id'      => $this->external_customer_id ?? '',
            'channel'                   => $this->channel ?? '',
            'name'                      => $this->name ?? '',
            'address'                   => $this->address ?? '',
            'sequence_number'           => $this->sequence_number ?? '',
            'telephone'                 => $this->telephone ?? '',
            'telephone_2'               => $this->telephone_2 ?? '',
            'fullname'                  => $this->fullname ?? '',
            'address_ktp'               => $this->address_ktp ?? '',
            'no_ktp'                    => $this->no_ktp ?? '',
            'profession'                => $this->profession ?? '',
            'land_status'               => $this->land_status ?? '',
            'land_document'             => $this->land_document ?? '',
            'occupant'                  => $this->occupant ?? '',
            'used_for'                  => $this->used_for ?? '',
            'etc'                       => $this->etc ?? '',
            'electrical_power'          => $this->electrical_power ?? '',
            'building_area'             => $this->building_area ?? '',
            'id_baseline'               => $this->id_baseline ?? '',
            'no_spl'                    => $this->no_spl ?? '',
            'id_card_photo'             => $this->id_card_photo ?? '',
            'family_card_photo'         => $this->family_card_photo ?? '',
            'house_image_registration'  => $this->house_image_registration ?? '',
            'long_registration'         => $this->long_registration ?? '',
            'lat_registration'          => $this->lat_registration ?? '',
            'house_image'               => $this->house_image ?? '',
            'long'                      => $this->long ?? '',
            'lat'                       => $this->lat ?? '',
            'meter_number'              => $this->meter_number ?? '',
            'protected_plastic'         => $this->protected_plastic ?? '',
            'meter_seal'                => $this->meter_seal ?? '',
            'meter_then'                => $this->meter_then ?? '',
            'meter_then_past'           => $this->meter_then_past ?? '',
            'use_then'                  => $this->use_then ?? '',
            'use_then_past'             => $this->use_then_past ?? '',
            'cost'                      => $this->cost ?? '',
            'fare_cost'                 => $this->fare_cost ?? '',
            'load_cost'                 => $this->load_cost ?? '',
            'meter_image'               => $this->meter_image ?? '',
            'status'                    => $this->status ?? '',
            'status_registration'       => $this->status_registration ?? '',
            'decline_reason'            => $this->decline_reason ?? '',
            'registration_source'       => $this->registration_source ?? '',
            'disconnect_date'           => $this->disconnect_date ?? '',
            'register_date'             => $this->register_date ?? '',
            'install_date'              => $this->install_date ?? '',
            'pay_date'                  => $this->pay_date ?? '',
            'admit_date'                => $this->admit_date ?? '',
            'temporary_disconnect_date' => $this->temporary_disconnect_date ?? '',
            'new_installation_cost'     => $this->new_installation_cost ?? '',
            'tertiary_cost'             => $this->tertiary_cost ?? '',
            'new_installation_category' => $this->new_installation_category ?? '',
            'collective_name'           => $this->collective_name ?? '',
            'is_hankam'                 => $this->is_hankam ?? '',
            'id_region'                 => $this->id_region ?? '',
            'unit_code'                 => $this->unit_code ?? '',
            'id_group'                  => $this->id_group ?? '',
            'id_meter_condition'        => $this->id_meter_condition ?? '',
            'id_brand'                  => $this->id_brand ?? '',
            'id_size'                   => $this->id_size ?? '',
            'id_transaction'            => $this->id_transaction ?? '',
            'id_reduction'              => $this->id_reduction ?? '',
            'id_sub_district'           => $this->id_sub_district ?? '',
            'name_sub_district'         => $this->name_sub_district ?? '',
            'id_village'                => $this->id_village ?? '',
            'name_village'              => $this->name_village ?? '',
            'created_by'                => $this->created_by ?? '',
            'updated_by'                => $this->updated_by ?? '',
            'deleted_by'                => $this->deleted_by ?? '',
            'pts'                       => $this->pts ?? '',
            'region_code'               => $this->region_code ?? '',
            'region_name'               => $this->region_name ?? '',
            'id_service_zone'           => $this->id_service_zone ?? '',
            'zone_name'                 => $this->zone_name ?? '',
            'zone_service_area_name'    => $this->zone_service_area_name ?? '',
            'unit_name'                 => $this->unit_name ?? '',
            'group_name'                => $this->group_name ?? '',
            'group_category'            => $this->group_category ?? '',
            'group_description'         => $this->group_description ?? '',
            'group_customer_type'       => $this->group_customer_type ?? '',
            'water_meter_brand'         => $this->water_meter_brand ?? '',
            'water_meter_size'          => $this->water_meter_size ?? '',
            'status_description'        => $this->status_description ?? '',
            'status_registration_description' => $this->status_registration_description ?? '',
            'unfinished_consultations'  => $this->unfinished_consultations ?? 0,
            'updated_at'                => $this->updated_at ?? '',
            'created_at'                => $this->created_at ?? '',
            'deleted_at'                => $this->deleted_at ?? '',
        ];
    }
}
