<?php

namespace App\Http\Resources\CustomerChannel;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerChannelRegDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $customerChannel = $this->resource['customerChannel'];
        $customer = $this->resource['customer'];
        $payment = $this->resource['payment'];
        $tracking = $this->resource['tracking'];

        return [
            'activity' => $this->getActivityData($customerChannel),
            'customer_channel' => $this->getCustomerChannelData($customerChannel),
            'customer' => $this->getCustomerData($customer),
            'payment' => $payment,
            'tracking' => $tracking,
        ];
    }

    private function getActivityData($customerChannel): ?array
    {
        if (!$customerChannel->activity) {
            return null;
        }
        $activity = $customerChannel->activity->first();

        return [
            'id' => $activity->id ?? '',
            'code' => $activity->code ?? '',
            'title' => $activity->title ?? '',
            'category' => $activity->category ?? '',
            'description' => $activity->description ?? '',
            'status' => $activity->status ?? '',
            'status_description' => $activity->status_description ?? '',
            'created_at' => $activity->created_at ?? '',
        ];
    }

    private function getCustomerChannelData($customerChannel): array
    {
        return [
            'id' => $customerChannel->id,
            'external_customer_id' => $customerChannel->external_customer_id ?? '',
            'id_transaction' => $customerChannel->id_transaction ?? '',
            'name' => $customerChannel->name ?? '',
            'fullname' => $customerChannel->fullname ?? '',
            'address' => $customerChannel->address ?? '',
            'address_ktp' => $customerChannel->address_ktp ?? '',
            'name_sub_district' => $customerChannel->name_sub_district ?? '',
            'name_village' => $customerChannel->name_village ?? '',
            'land_status' => $customerChannel->land_status ?? '',
            'land_document' => $customerChannel->land_document ?? '',
            'occupant' => $customerChannel->occupant ?? '',
            'used_for' => $customerChannel->used_for ?? '',
            'etc' => $customerChannel->etc ?? '',
            'electrical_power' => $customerChannel->electrical_power ?? '',
            'building_area' => $customerChannel->building_area ?? '',
            'long_registration' => $customerChannel->long_registration ?? '',
            'lat_registration' => $customerChannel->lat_registration ?? '',
            'decline_reason' => $customerChannel->decline_reason ?? '',
            'status' => $customerChannel->status ?? '',
            'status_registration' => $customerChannel->status_registration ?? '',
            'status_registration_description' => $customerChannel->status_registration_description ?? '',
            'created_at' => $customerChannel->created_at ?? '',
            'updated_at' => $customerChannel->updated_at ?? '',
        ];
    }

    private function getCustomerData($customer): array
    {
        return [
            'id' => $customer->id ?? '',
            'name' => $customer->name ?? '',
            'email' => $customer->email ?? '',
            'phone' => $customer->phone ?? '',
        ];
    }
}
