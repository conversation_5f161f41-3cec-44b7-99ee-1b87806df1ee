<?php

namespace App\Http\Resources\Information;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EduDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'            => $this->id,
            'title'         => $this->title,
            'date'          => $this->date,
            'is_active'     => $this->is_active,
            'is_active_description' => $this->is_active_description,
            'created_at'    => $this->created_at,
            'updated_at'    => $this->updated_at,
            'image'         => $this->whenLoaded('images', function () {
                return $this->images->first()->image ?? null;
            }),
            'creator_name'  => $this->creator_name ?? null,
            'updater_name'  => $this->updater_name ?? null,
            'images'        => $this->whenLoaded('images', function () {
                return $this->images->map(function ($image) {
                    return [
                        'id'        => $image->id,
                        'edu_id'    => $image->id,
                        'image'     => $image->image,
                    ];
                });
            }),
        ];
    }
}
