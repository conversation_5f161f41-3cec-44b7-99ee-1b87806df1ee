<?php

namespace App\Http\Resources\Notification;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request)
    {
        return [
            'id'                => $this->id,
            'data'              => [
                'id'            => $this->data['data']['id'] ?? null,
                'type'          => $this->data['type'] ?? null,
                'title'         => $this->data['title'] ?? null,
                'message'       => $this->data['message'] ?? null,
                'status'        => $this->data['data']['status'] ?? null,
                'image_url'     => $this->data['data']['image_url'] ?? null,
            ],
            'read_at'           => $this->read_at,
            'created_at'        => $this->created_at,
        ];
    }
}
