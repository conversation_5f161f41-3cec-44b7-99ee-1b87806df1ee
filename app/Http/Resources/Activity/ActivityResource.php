<?php

namespace App\Http\Resources\Activity;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ActivityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'            => $this->id,
            'subject_type'  => $this->subject_type_name,
            'subject_id'    => $this->subject_id,
            'customer_id'   => $this->customer_id,
            'code'          => $this->code,
            'title'         => $this->title,
            'category'      => $this->category,
            'description'   => $this->description,
            'status'        => $this->status,
            'status_description' => $this->status_description,
            'created_at'    => $this->created_at,
            'updated_at'    => $this->updated_at,
        ];
    }
}
