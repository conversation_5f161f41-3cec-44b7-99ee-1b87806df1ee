<?php

namespace App\Events\Consultation;

use App\Models\Consultation\Consultation;
use App\Models\Consultation\Enum\ConsultationStatus;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ConsultationUpdatedEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $waitingCount = 0;

    /**
     * Create a new event instance.
     */
    public function __construct()
    {
        $this->waitingCount = Consultation::where('status', ConsultationStatus::WAITING->value)->count();

        Cache::put('waiting_consultation_count', $this->waitingCount, now()->addMinutes(15));

        Log::info('[ConsultationUpdatedEvent] Broadcasting', [
            'waitingCount' => $this->waitingCount,
            'timestamp' => now(),
            'channel' => 'admin-dashboard',
            'event' => 'waiting.consultation.count'
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('admin-dashboard'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'waiting.consultation.count';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'count' => $this->waitingCount,
            'timestamp' => now()->toISOString(),
        ];
    }

    public function broadcastWhen(): bool
    {
        return true;
    }
}
