<?php

namespace App\Events\Customer;

use App\Models\Customer\Customer;
use App\Models\Customer\Enum\VerificationStatus;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CustomerVerificationEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $waitingVerificationCount = 0;

    /**
     * Create a new event instance.
     */
    public function __construct()
    {
        $this->waitingVerificationCount = Customer::where('verification_status', VerificationStatus::WAITING->value)->count();

        Cache::put('waiting_verification_count', $this->waitingVerificationCount, now()->addMinutes(15));

        Log::info('[CustomerVerificationEvent] Broadcasting', [
            'waitingVerificationCount' => $this->waitingVerificationCount,
            'timestamp' => now(),
            'channel' => 'admin-dashboard',
            'event' => 'waiting.verification.count'
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('admin-dashboard'),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'waiting.verification.count';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'count' => $this->waitingVerificationCount,
            'timestamp' => now()->toISOString(),
        ];
    }

    public function broadcastWhen(): bool
    {
        return true;
    }
}
