<?php

namespace App\Jobs\Customer;

use App\Actions\CustomerChannel\ChangeName\UpdateChangeNameAction;
use App\Models\Customer\CustomerChannelChange;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class SyncChangeNameJob implements ShouldQueue
{
    use Queueable;

    protected $tries = 3;
    protected $externalCustomerId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $externalCustomerId)
    {
        $this->externalCustomerId = $externalCustomerId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('[SyncChangeNameJob] Sync Change Name Job started', [
            'external_customer_id' => $this->externalCustomerId,
        ]);

        $changeName = CustomerChannelChange::where('external_customer_id', $this->externalCustomerId)->first();

        if ($changeName) {
            $sync = new UpdateChangeNameAction();
            $result = $sync->handle($changeName->id_transaction);

            Log::error('[SyncChangeNameJob] Sync Change Name Job', [
                $result
            ]);

            if ($result['status'] === 'error') {
                return;
            }

            Log::info('[SyncChangeNameJob] Sync Change Name Job completed successfully');
        } else {
            Log::info('[SyncChangeNameJob] No change name found, nothing to sync.');
        }
    }
}
