<?php

namespace App\Jobs\Customer;

use App\Actions\Customer\UpsertCustomerChannelAction;
use App\DTO\UpsertCustomerChannelDTO;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class SyncCustomerChannel<PERSON><PERSON> implements ShouldQueue
{
    use Queueable;

    protected $tries = 3;
    protected $externalCustomerId;

    /**
     * Create a new job instance.
     */
    public function __construct(string $externalCustomerId)
    {
        $this->externalCustomerId = $externalCustomerId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('[SyncCustomerChannelJob] Sync Customer Channel Job started', [
            'external_customer_id' => $this->externalCustomerId,
        ]);

        $dto = new UpsertCustomerChannelDTO(
            externalCustomerId: $this->externalCustomerId,
            channelId: null,
            customerId: 0
        );

        $result = app(UpsertCustomerChannelAction::class)->handle($dto);

        if ($result->status === 'error') {
            Log::error('[SyncCustomerChannelJob] Sync Customer Channel Job failed', [
                'message'   => $result->message,
                'code'      => $result->code,
            ]);
            return;
        }

        Log::info('[SyncCustomerChannelJob] Sync Customer Channel Job completed successfully');
    }
}
