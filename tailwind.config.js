import defaultTheme from "tailwindcss/defaultTheme";
import forms from "@tailwindcss/forms";
import typography from "@tailwindcss/typography";

const { addDynamicIconSelectors } = require("@iconify/tailwind");

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./vendor/laravel/jetstream/**/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./resources/js/**/*.vue",
        "./resources/js/**/*.js",
        "./node_modules/flowbite/**/*.js",
    ],

    theme: {
        extend: {
            fontFamily: {
                tahoma: ["Tahoma", "sans-serif"],
            },
            fontSize: {
                sm: ["0.875rem", { lineHeight: "1.25rem" }],
                base: ["0.9375rem", { lineHeight: "1.5rem" }],
                lg: ["1.125rem", { lineHeight: "1.75rem" }],
                xl: ["1.25rem", { lineHeight: "1.75rem" }],
            },
            colors: {
                primary: {
                    100: "#DBECF4",
                    300: "#2A93D5",
                    500: "#1E73BE",
                    700: "#1D3D52",
                    900: "#1E1B15",
                },
                accent: {
                    100: "#CCD6C0",
                    300: "#63E529",
                    500: "#4DC518",
                    700: "#445D19",
                    900: "#26340E",
                },
                notification: {
                    1: "#F5365C",
                    2: "#2DCE89",
                    3: "#DCEEFF",
                    4: "#FFF0F3",
                },
            },
        },
    },

    plugins: [
        forms,
        typography,
        require("flowbite/plugin"),
        addDynamicIconSelectors({
            // Prefix for selectors, must be different for each addDynamicIconSelectors()
            prefix: "icon",
            // Removes redundant rules
            overrideOnly: false,
            // Icon height, 0 to disable size
            scale: 1.5,
            // Custom icon sets
            iconSets: {},
            // Callback to customise icons (such as change stroke-width, color, etc...).
            // First param is content, second is icon name, third is icon set prefix.
            // Function should return modified content.
            customise: (content, name, prefix) => content,
        }),
    ],
};
