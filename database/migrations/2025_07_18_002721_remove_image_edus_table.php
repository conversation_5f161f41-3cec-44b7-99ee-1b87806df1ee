<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('edus', function (Blueprint $table) {
            if (Schema::hasColumn('edus', 'image')) {
                $table->dropColumn('image');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('edus', function (Blueprint $table) {
            if (!Schema::hasColumn('edus', 'image')) {
                $table->string('image')->nullable()->after('date');
            }
        });
    }
};
