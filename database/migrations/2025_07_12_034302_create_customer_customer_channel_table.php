<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_customer_channel', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');
            $table->foreignId('customer_channel_id')->constrained('customer_channels')->onDelete('cascade');
            $table->timestamps();
        });

        Schema::table('customer_channels', function (Blueprint $table) {
            $table->dropColumn('customer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_customer_channel');

        Schema::table('customer_channels', function (Blueprint $table) {
            $table->unsignedBigInteger('customer_id')->index()->after('id');
        });
    }
};
