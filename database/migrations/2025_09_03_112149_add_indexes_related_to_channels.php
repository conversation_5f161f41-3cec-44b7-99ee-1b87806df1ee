<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_channels', function (Blueprint $table) {
            $table->index('channel');
            $table->index('id_transaction');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_channels', function (Blueprint $table) {
            $table->dropIndex('customer_channels_channel_index');
            $table->dropIndex('customer_channels_id_transaction_index');
        });
    }
};
