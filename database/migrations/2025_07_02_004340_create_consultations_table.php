<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('consultations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id')->index();
            $table->unsignedBigInteger('external_customer_id')->nullable()->index();
            $table->unsignedBigInteger('consultation_category_id')->index();
            $table->string('code');
            $table->string('title');
            $table->string('description')->nullable();
            $table->dateTime('consultation_time');
            $table->enum('status', ['WAITING', 'PROGRESS', 'CANCELED_BY_CUSTOMER', 'CANCELED_BY_ADMIN', 'DONE'])->default('WAITING');
            $table->string('cancel_reason')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('consultations');
    }
};
