<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('customer_channels');
        Schema::create('customer_channels', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id')->index();
            $table->unsignedBigInteger('external_customer_id')->nullable()->index();
            $table->string('channel')->nullable();
            $table->string('name')->nullable();
            $table->string('address')->nullable();
            $table->integer('sequence_number')->nullable();
            $table->string('telephone')->nullable();
            $table->string('telephone_2')->nullable();
            $table->string('fullname')->nullable();
            $table->string('address_ktp')->nullable();
            $table->string('no_ktp')->nullable();
            $table->string('profession')->nullable();
            $table->string('land_status')->nullable();
            $table->string('land_document')->nullable();
            $table->string('occupant')->nullable();
            $table->string('used_for')->nullable();
            $table->text('etc')->nullable();
            $table->string('electrical_power')->nullable();
            $table->string('building_area')->nullable();
            $table->unsignedBigInteger('id_baseline')->nullable();
            $table->string('no_spl')->nullable();
            $table->string('id_card_photo')->nullable();
            $table->string('family_card_photo')->nullable();
            $table->string('house_image_registration')->nullable();
            $table->decimal('long_registration', 11, 8)->nullable();
            $table->decimal('lat_registration', 10, 8)->nullable();
            $table->string('house_image')->nullable();
            $table->decimal('long', 11, 8)->nullable();
            $table->decimal('lat', 10, 8)->nullable();
            $table->string('meter_number')->nullable();
            $table->string('protected_plastic')->nullable();
            $table->string('meter_seal')->nullable();
            $table->string('meter_then')->nullable();
            $table->string('meter_then_past')->nullable();
            $table->string('use_then')->nullable();
            $table->string('use_then_past')->nullable();
            $table->string('cost')->nullable();
            $table->string('fare_cost')->nullable();
            $table->string('load_cost')->nullable();
            $table->string('meter_image')->nullable();
            $table->string('status')->nullable();
            $table->string('status_registration')->nullable();
            $table->text('decline_reason')->nullable();
            $table->string('registration_source')->nullable();
            $table->date('disconnect_date')->nullable();
            $table->date('register_date')->nullable();
            $table->date('install_date')->nullable();
            $table->date('pay_date')->nullable();
            $table->date('admit_date')->nullable();
            $table->date('temporary_disconnect_date')->nullable();
            $table->string('new_installation_cost')->nullable();
            $table->string('tertiary_cost')->nullable();
            $table->string('new_installation_category')->nullable();
            $table->string('collective_name')->nullable();
            $table->boolean('is_hankam')->nullable();

            $table->unsignedBigInteger('id_region')->nullable();
            $table->unsignedBigInteger('unit_code')->nullable();
            $table->unsignedBigInteger('id_group')->nullable();
            $table->unsignedBigInteger('id_meter_condition')->nullable();
            $table->unsignedBigInteger('id_brand')->nullable();
            $table->unsignedBigInteger('id_size')->nullable();
            $table->unsignedBigInteger('id_transaction')->nullable();
            $table->unsignedBigInteger('id_reduction')->nullable();
            $table->unsignedBigInteger('id_sub_district')->nullable();
            $table->unsignedBigInteger('id_village')->nullable();
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->unsignedBigInteger('deleted_by')->nullable();
            $table->string('pts')->nullable();

            $table->string('region_code')->nullable();
            $table->string('region_name')->nullable();
            $table->unsignedBigInteger('id_service_zone')->nullable();
            $table->string('zone_name')->nullable();
            $table->string('zone_service_area_name')->nullable();
            $table->string('unit_name')->nullable();
            $table->string('group_name')->nullable();
            $table->string('group_category')->nullable();
            $table->string('group_description')->nullable();
            $table->string('group_customer_type')->nullable();
            $table->string('water_meter_brand')->nullable();
            $table->string('water_meter_size')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_channels');
        Schema::create('customer_channels', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id')->index();
            $table->string('external_channel_id')->nullable();
            $table->string('channel')->nullable();
            $table->string('name')->nullable();
            $table->string('fullname')->nullable();
            $table->string('telephone')->nullable();
            $table->string('telephone_2')->nullable();
            $table->string('address')->nullable();
            $table->string('address_ktp')->nullable();
            $table->string('no_ktp')->nullable();
            $table->string('profession')->nullable();
            $table->string('land_status')->nullable();
            $table->string('occupant')->nullable();
            $table->string('used_for')->nullable();
            $table->string('etc')->nullable();
            $table->string('electrical_power')->nullable();
            $table->string('building_area')->nullable();
            $table->string('id_baseline')->nullable();
            $table->string('no_spl')->nullable();
            $table->string('house_image')->nullable();
            $table->string('status')->nullable();
            $table->unsignedBigInteger('sequence_number')->nullable();
            $table->string('meter_number')->nullable();
            $table->unsignedBigInteger('region_id')->nullable();
            $table->string('region_code')->nullable();
            $table->string('region_name')->nullable();
            $table->unsignedBigInteger('zone_id')->nullable();
            $table->string('zone_name')->nullable();
            $table->string('zone_service_area_name')->nullable();
            $table->unsignedBigInteger('group_id')->nullable();
            $table->string('group_name')->nullable();
            $table->string('group_category')->nullable();
            $table->string('group_description')->nullable();
            $table->string('group_customer_type')->nullable();
            $table->unsignedBigInteger('unit_id')->nullable();
            $table->string('unit_name')->nullable();
            $table->unsignedBigInteger('water_meter_id')->nullable();
            $table->string('water_meter_brand')->nullable();
            $table->string('water_meter_size')->nullable();
            $table->timestamps();
        });
    }
};
