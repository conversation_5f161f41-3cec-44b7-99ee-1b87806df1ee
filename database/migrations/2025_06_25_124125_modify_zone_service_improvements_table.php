<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_improvements', function (Blueprint $table) {
            $table->renameColumn('sub_district_id', 'zone_id');
            $table->renameColumn('sub_district_name', 'zone_name');
            $table->string('service_area_name')->nullable()->after('zone_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_improvements', function (Blueprint $table) {
            $table->renameColumn('zone_id', 'sub_district_id');
            $table->renameColumn('zone_name', 'sub_district_name');
            $table->dropColumn('service_area_name');
        });
    }
};
