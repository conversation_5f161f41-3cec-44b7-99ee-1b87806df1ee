<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('profession')->nullable()->after('birth_date');
            $table->string('no_ktp')->nullable()->after('profession');
            $table->string('id_card_photo')->nullable()->after('no_ktp');
            $table->string('verification_status')->default('UNVERIFIED')->after('id_card_photo');
            $table->timestamp('verified_at')->nullable()->after('verification_status');
            $table->unsignedBigInteger('verified_by')->nullable()->after('verified_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn([
                'profession',
                'no_ktp',
                'id_card_photo',
                'verification_status',
                'verified_at',
                'verified_by',
            ]);
        });
    }
};
