<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_channel_changes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')
                ->constrained('customers');
            $table->unsignedBigInteger('external_customer_id')->nullable();
            $table->string('id_history_name')->nullable();
            $table->string('id_transaction')->unique();
            $table->string('old_name')->nullable();
            $table->string('new_name');
            $table->text('reason')->nullable();
            $table->text('description')->nullable();
            $table->decimal('cost', 15, 2)->default(0);
            $table->string('payment_photo')->nullable();
            $table->string('payment_status')->default('NEED_REVIEW');
            $table->string('current_status')->default('PAYMENT');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_channel_changes');
    }
};
