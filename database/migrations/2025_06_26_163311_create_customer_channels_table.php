<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_channels', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id')->index();
            $table->string('external_channel_id')->nullable();
            $table->string('channel')->nullable();
            $table->string('name')->nullable();
            $table->string('fullname')->nullable();
            $table->string('telephone')->nullable();
            $table->string('telephone_2')->nullable();
            $table->string('address')->nullable();
            $table->string('address_ktp')->nullable();
            $table->string('no_ktp')->nullable();
            $table->string('profession')->nullable();
            $table->string('land_status')->nullable();
            $table->string('occupant')->nullable();
            $table->string('used_for')->nullable();
            $table->string('etc')->nullable();
            $table->string('electrical_power')->nullable();
            $table->string('building_area')->nullable();
            $table->string('id_baseline')->nullable();
            $table->string('no_spl')->nullable();
            $table->string('house_image')->nullable();
            $table->string('status')->nullable();
            $table->unsignedBigInteger('sequence_number')->nullable();
            $table->string('meter_number')->nullable();
            $table->unsignedBigInteger('region_id')->nullable();
            $table->string('region_code')->nullable();
            $table->string('region_name')->nullable();
            $table->unsignedBigInteger('zone_id')->nullable();
            $table->string('zone_name')->nullable();
            $table->string('zone_service_area_name')->nullable();
            $table->unsignedBigInteger('group_id')->nullable();
            $table->string('group_name')->nullable();
            $table->string('group_category')->nullable();
            $table->string('group_description')->nullable();
            $table->string('group_customer_type')->nullable();
            $table->unsignedBigInteger('unit_id')->nullable();
            $table->string('unit_name')->nullable();
            $table->unsignedBigInteger('water_meter_id')->nullable();
            $table->string('water_meter_brand')->nullable();
            $table->string('water_meter_size')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_channels');
    }
};
