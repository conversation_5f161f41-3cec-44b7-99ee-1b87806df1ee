<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->timestamp('last_login')->nullable()->after('is_active');
            $table->string('last_login_ip', 20)->nullable()->after('last_login');
            $table->string('last_login_useragent')->nullable()->after('last_login_ip');
            $table->text('last_login_url')->nullable()->after('last_login_useragent');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['last_login', 'last_login_ip', 'last_login_useragent', 'last_login_url']);
        });
    }
};
