<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('password')->nullable();
            $table->string('phone')->nullable();
            $table->enum('gender', ['MALE', 'FEMALE'])->default('MALE');
            $table->tinyInteger('is_active')->default(1);
            $table->text('address')->nullable();
            $table->string('avatar')->nullable();
            $table->timestamp('last_login')->nullable();
            $table->string('last_login_ip', 20)->nullable();
            $table->string('last_login_useragent')->nullable();
            $table->text('last_login_url')->nullable();
            $table->string('token')->nullable();
            $table->rememberToken();
            $table->json('settings')->nullable();
            $table->json('player_ids')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
