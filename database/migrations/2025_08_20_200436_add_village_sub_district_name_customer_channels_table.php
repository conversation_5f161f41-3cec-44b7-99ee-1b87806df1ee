<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_channels', function (Blueprint $table) {
            $table->string('name_village')->nullable()->after('id_village');
            $table->string('name_sub_district')->nullable()->after('id_sub_district');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_channels', function (Blueprint $table) {
            $table->dropColumn(['name_village', 'name_sub_district']);
        });
    }
};
