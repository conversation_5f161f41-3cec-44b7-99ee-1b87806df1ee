<?php

namespace Database\Seeders;

use App\Models\User\Enum\UserRole;
use App\Models\User\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::firstOrCreate([
            'name'  => 'Administrator',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $user->assignRole(UserRole::SUPERADMIN);
    }
}
