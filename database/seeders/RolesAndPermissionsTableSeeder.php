<?php

namespace Database\Seeders;

use App\Models\User\Enum\UserRole;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsTableSeeder extends Seeder
{
    protected $superadmin;

    protected $superadminResources = [
        'users',
        'roles',
    ];

    protected $actions = [
        'view',
        'create',
        'edit',
        'delete',
    ];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();

        $this->createRoles();
        $this->createPermissions();

        Schema::enableForeignKeyConstraints();
    }

    private function createRoles()
    {
        $roleId = DB::table('roles')->insertGetId([
            'name' => UserRole::SUPERADMIN,
            'guard_name' => 'web',
            'is_active' => true,
            'created_by' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        $this->superadmin = Role::find($roleId);
    }

    private function createPermissions()
    {
        collect($this->superadminResources)->each(function ($resource) {
            collect($this->actions)->each(function ($action) use ($resource) {
                $permission = Permission::firstOrCreate(['name' => "{$action} {$resource}"]);

                $this->superadmin->givePermissionTo($permission);
            });
        });
    }
}
