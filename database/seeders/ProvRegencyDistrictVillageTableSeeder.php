<?php

namespace Database\Seeders;

use App\Models\Location\District;
use App\Models\Location\Province;
use App\Models\Location\Regency;
use App\Models\Location\Village;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ProvRegencyDistrictVillageTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Schema::disableForeignKeyConstraints();
        Village::truncate();
        District::truncate();
        Regency::truncate();
        Province::truncate();

        DB::unprepared(file_get_contents(__DIR__ . '/sql/loc-provinces.sql'));
        DB::unprepared(file_get_contents(__DIR__ . '/sql/loc-regencies.sql'));
        DB::unprepared(file_get_contents(__DIR__ . '/sql/loc-districts.sql'));
        DB::unprepared(file_get_contents(__DIR__ . '/sql/loc-villages-1.sql'));
        DB::unprepared(file_get_contents(__DIR__ . '/sql/loc-villages-2.sql'));
        DB::unprepared(file_get_contents(__DIR__ . '/sql/loc-villages-3.sql'));
        DB::unprepared(file_get_contents(__DIR__ . '/sql/loc-villages-4.sql'));
        DB::unprepared(file_get_contents(__DIR__ . '/sql/loc-villages-5.sql'));

        Schema::enableForeignKeyConstraints();
    }
}
