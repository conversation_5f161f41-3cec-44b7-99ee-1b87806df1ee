image: aarie33/laravue-php83-alphine-node22-zip-scp:latest

stages:
  - build-deploy

dev:
  stage: build-deploy
  only:
    - develop
  before_script:
    - "which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )"
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" >> ~/.ssh/config'
  script:
    - cp .env.example .env
    - export APP_ENV=local
    - |
      (
        composer install -o -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist --ignore-platform-reqs
      ) & (
        npm ci
        npm run build
      )
    - rm -r node_modules
    - mkdir -p var && chmod -R 777 var
    - mkdir -p /tmp/build_artifact
    - tar -czf /tmp/build_artifact/$CI_COMMIT_SHA.tar.gz .
    - scp -P 9167 -p /tmp/build_artifact/$CI_COMMIT_SHA.tar.gz $SSH_USER@$SSH_HOST:/www/wwwroot/$PROJECT_FOLDER/
    - ssh -p 9167 $SSH_USER@$SSH_HOST "cd  /www/wwwroot/$PROJECT_FOLDER/ && find . -mindepth 1 \( -path "./storage" -o -path "./vendor" \) -prune -o ! -name "$CI_COMMIT_SHA.tar.gz" -exec rm -rf {} + && tar -xzf $CI_COMMIT_SHA.tar.gz && rm $CI_COMMIT_SHA.tar.gz && sh post-pull.dev.sh && ln -sfn $CI_COMMIT_SHA current && exit"
