# Sistem Informasi Manajemen Pelanggan

### Perumdam Tirta Pandalungan Kabupaten Jember

## Tech Stack

-   Laravel 11
-   Vue 3
-   InertiaJS

## Requirement

-   PHP v8.3
-   Node v22

## Installation

1. Clone this repository
2. `cp .env.example .env`
3. `composer install`
4. `php artisan migrate`
5. `php artisan db:seed`
6. `php artisan passport:client --personal`
7. `npm install`
8. `npm run dev` or `npm run build`
9. `php artisan serve`

## Cron Job Scheduler

Untuk menjalankan [schedule command](https://laravel.com/docs/11.x/scheduling) yang ada di aplikasi ini, setup cron per menit seperti contoh berikut:

```bash
$ crontab -e
# then add this line

* * * * * /www/server/php/83/bin/php /www/wwwroot/simpel.dafidea.xyz/artisan schedule:run >> /dev/null 2>&1
```

## Queue Job

#### Configure supervisor

-   make sure `supervisor` already installed. `sudo apt-get install supervisor`
-   create a conf file inside `/etc/supervisor/conf.d` directory
-   file name : `tpj-simpel.conf`
-   here is the file content, `don't forget to` customize the project directory

```
[program:tpj-simpel]
process_name=%(program_name)s_%(process_num)02d
command=/www/server/php/83/bin/php /www/wwwroot/simpel-tpj.dafidea.xyz/artisan queue:work --sleep=3 --tries=3 --max-time=3600 --max-jobs=10
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www
numprocs=2
redirect_stderr=true
stdout_logfile=/www/wwwroot/simpel-tpj.dafidea.xyz/storage/logs/worker.log
stopwaitsecs=3600
```

#### Run the command

```
sudo supervisorctl reread

sudo supervisorctl update

sudo supervisorctl start tpj-simpel

sudo supervisorctl status
```

More documentation for [supervisor](http://supervisord.org/configuration.html)

## API Documentation

[![Run in Postman](https://run.pstmn.io/button.svg)](https://orange-space-513118.postman.co/workspace/Simpel-Tirta-Pandalungan-Jember~0d697e84-ac61-4444-9a8f-e5c0740b112a/collection/1863545-4699f919-76e6-411b-ba99-e29cf64566d9?action=share&creator=1863545&active-environment=1863545-9bc99611-7502-4ce5-ab75-6e6a47bd8f6d)
