import "./bootstrap";
import "../css/app.css";
import "flowbite";
import "remixicon/fonts/remixicon.css";

import { createApp, h } from "vue";
import { Link, createInertiaApp } from "@inertiajs/vue3";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { ZiggyVue } from "../../vendor/tightenco/ziggy";
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import moment from "moment";
import "moment/dist/locale/id";

import AuthMixin from "@/Mixins/AuthMixin.js";

const appName =
    import.meta.env.VITE_APP_NAME || "Sistem Informasi Manajemen Pelanggan";

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) =>
        resolvePageComponent(
            `./Pages/${name}.vue`,
            import.meta.glob("./Pages/**/*.vue")
        ),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .component("Link", Link)
            .component("VueDatePicker", VueDatePicker)
            .mixin(AuthMixin);

        app.config.globalProperties.moment = moment;

        return app.mount(el);
    },
    progress: {
        color: "#4B5563",
    },
});
