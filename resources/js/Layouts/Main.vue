<template>

  <Head />
  <Sidebar :waitingConsultationCount="waitingConsultationCount" :waitingVerificationCount="waitingVerificationCount"
    @toggleCollapse="toggleCollapse" />
  <div class="pt-2 md:pt-14" :class="{ 'md:ml-20': collapsed, 'md:ml-64': !collapsed }">
    <Topbar />
    <Breadcrumb />

    <div class="px-2 pb-10">
      <slot></slot>
    </div>
  </div>
  <Alert />
</template>

<script>
import { initFlowbite } from "flowbite";
import Head from "@/Components/Global/Meta/Head.vue";
import Topbar from "./Topbar.vue";
import Sidebar from "./Sidebar.vue";
import Breadcrumb from "./Breadcrumb.vue";
import Alert from "@/Components/Global/Notification/Alert.vue"

export default {
  components: {
    Head,
    Topbar,
    Sidebar,
    Breadcrumb,
    Alert
  },
  data() {
    return {
      collapsed: false,
      waitingConsultationCount: this.$page.props.countSummary.waitingConsultationCount,
      waitingVerificationCount: this.$page.props.countSummary.waitingVerificationCount,
      isConnected: false,
      echoChannel: null,
    };
  },
  watch: {
    '$page.props.countSummary.waitingConsultationCount': {
      handler() {
        this.waitingConsultationCount = this.$page.props.countSummary.waitingConsultationCount;
      },
    },
    '$page.props.countSummary.waitingVerificationCount': {
      handler() {
        this.waitingVerificationCount = this.$page.props.countSummary.waitingVerificationCount;
      },
    },
  },
  methods: {
    toggleCollapse(attr) {
      this.collapsed = attr;
    },
    setupSummaryCountListener() {
      try {
        this.echoChannel = window.Echo.channel('admin-dashboard');

        this.echoChannel.listen('.waiting.consultation.count', (data) => {
          this.waitingConsultationCount = data.count;
        });

        this.echoChannel.listen('.waiting.verification.count', (data) => {
          this.waitingVerificationCount = data.count;
        });

        this.echoChannel.subscribed(() => {
          this.isConnected = true;
          console.log('✅ Connected to admin-dashboard channel');
        });

        this.echoChannel.error((error) => {
          this.isConnected = false;
          console.error('❌ Channel error:', error);
        });

      } catch (error) {
        console.error('Failed to setup Echo listener:', error);
      }
    },

    cleanupEchoListener() {
      if (this.echoChannel) {
        window.Echo.leaveChannel('admin-dashboard');
        this.echoChannel = null;
        this.isConnected = false;
        console.log('🧹 Cleaned up Echo listener');
      }
    }
  },

  mounted() {
    initFlowbite();
    this.setupSummaryCountListener();
  },

  unmounted() {
    this.cleanupEchoListener();
  }
};
</script>
