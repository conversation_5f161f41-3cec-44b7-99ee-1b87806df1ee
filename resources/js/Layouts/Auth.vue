<template>
  <div>
    <Head />
    <div class="grid md:grid-cols-2 flex flex-col h-screen">
      <div class="flex justify-center place-items-center w-full bg-navy-100">
        <img src="/images/logo.png" class="p-5 w-3/4" alt="TPJ Icon" />
      </div>
      <slot />
    </div>

    <Alert :message="$page.props.flash" />
  </div>
</template>

<script>
import Head from "@/Components/Global/Meta/Head.vue";
import Alert from "@/Components/Global/Notification/Alert.vue";

export default {
  components: {
    Head,
    Alert,
  },
};
</script>
