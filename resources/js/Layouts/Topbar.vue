<template>
  <nav class="fixed bg-gray-100 left-0 top-0 z-10 w-full">
    <div class="px-3 py-3 md:ps-64">
      <div class="flex items-center justify-between ms-0 md:ms-5">
        <div class="flex items-center">
          <button data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar"
            type="button"
            class="inline-flex items-center p-2 me-1 text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600">
            <span class="sr-only">Open sidebar</span>
            <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg">
              <path clip-rule="evenodd" fill-rule="evenodd"
                d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z">
              </path>
            </svg>
          </button>
          <div class="font-bold text-base text-primary-500">{{ $page.props.title }}</div>
        </div>
        <div>
          <button type="button" class="flex items-center me-4 hover:bg-green-100 p-1 rounded" aria-expanded="false"
            data-dropdown-toggle="dropdown-user">
            <img class="w-9 h-9 rounded-full"
              :src="$page.props.auth.user.image ? $page.props.auth.user.image : '/images/logo.png'" alt="user photo" />
            <div class="ms-2 flex flex-col items-start">
              <div class="text-sm font-semibold text-primary-500">
                {{ $page.props.auth.user.name }}
                <i class="ri-arrow-down-s-line text-gray-400"></i>
              </div>
              <div class="text-gray-500 text-xs">
                {{ $page.props.auth.user?.roles[0] ?? '-' }}
              </div>
            </div>
          </button>
          <div
            class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600"
            id="dropdown-user">
            <div class="px-4 py-3" role="none">
              <p class="text-sm text-gray-900 dark:text-white" role="none">
                {{ $page.props.auth.user.name }}
              </p>
              <p class="text-sm font-medium text-gray-900 truncate dark:text-gray-300" role="none">
                {{ $page.props.auth.user.email }}
              </p>
            </div>
            <ul class="py-1" role="none">
              <li>
                <Link :href="route('user.profile')"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-200 dark:text-gray-300 dark:hover:bg-indigo-600 dark:hover:text-white"
                  role="menuitem">Profil</Link>
              </li>
              <li>
                <a href="#"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-200 dark:text-gray-300 dark:hover:bg-indigo-600 dark:hover:text-white"
                  role="menuitem" @click="confirmLogout = true">Logout</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <ConfirmationModal :show="confirmLogout != null" @close="confirmLogout = null">
    <template #title> Logout </template>

    <template #content>
      Apakah Anda yakin akan keluar?
    </template>

    <template #footer>
      <SecondaryButton @click="confirmLogout = null"> Batal </SecondaryButton>

      <DangerButton class="ms-3" :class="{ 'opacity-25': loading }" :disabled="loading" @click="logout">
        Logout
      </DangerButton>
    </template>
  </ConfirmationModal>
</template>

<script>
import ConfirmationModal from "@/Components/ConfirmationModal.vue";
import DangerButton from "@/Components/DangerButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";

export default {
  components: {
    ConfirmationModal,
    DangerButton,
    SecondaryButton,
  },
  data() {
    return {
      confirmLogout: null,
      loading: false,
    };
  },
  methods: {
    logout() {
      this.loading = true;

      this.$inertia.post(route("logout"), {
        onSuccess: () => {
          this.loading = false;
          this.confirmLogout = null;
          this.$inertia.replace(route("login"));
        },
        onError: () => {
          this.loading = false;
        },
      });
    },
  },
};
</script>
