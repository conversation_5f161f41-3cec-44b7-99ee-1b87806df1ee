<template>
  <div class="flex justify-start pt-4 pb-2 px-5" v-if="$page.props.breadcrumbs">
    <nav class="flex" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        <li class="inline-flex items-center" v-for="(item, index) in $page.props.breadcrumbs" :key="index">
          <Link :href="item.link"
            class="inline-flex items-center text-xs font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
          <i class="ri-arrow-right-s-line me-1" v-if="index > 0"></i>
          {{ item.title }}
          </Link>
        </li>
      </ol>
    </nav>
  </div>
</template>

<script>
export default {};
</script>
