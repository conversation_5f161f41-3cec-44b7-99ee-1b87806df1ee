<template>
  <div class="flex items-center block md:hidden">
    <button @click="toggleSidebar" aria-controls="logo-sidebar" type="button"
      class="inline-flex items-center p-2 mt-2 ms-3 text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600">
      <span class="sr-only">Open sidebar</span>
      <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
        xmlns="http://www.w3.org/2000/svg">
        <path clip-rule="evenodd" fill-rule="evenodd"
          d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z">
        </path>
      </svg>
    </button>
    <a href="/" class="flex items-center m-4">
      <img src="/images/logo.png" class="h-8 w-8 me-3" alt="TPJ" />
      <span v-if="!collapsed"
        class="text-primary-500 text-base self-center break-word font-medium dark:text-white">Perumdam Tirta
        Pandalungan</span>
    </a>
  </div>

  <!-- Sidebar -->
  <aside id="logo-sidebar"
    :class="['fixed top-0 left-0 z-40 h-screen transition-transform -translate-x-full md:translate-x-0 bg-white dark:bg-gray-800 dark:border-gray-700 text-sm duration-300', collapsed ? 'w-20' : 'w-64']"
    aria-label="Sidebar">
    <div class="flex m-4" :class="collapsed ? 'flex-col' : 'flex-row'">
      <button @click="toggleSidebar" type="button"
        class="inline-flex items-center p-2 me-1 text-sm text-gray-500 rounded-lg hidden md:block hover:text-primary-500">
        <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg">
          <path clip-rule="evenodd" fill-rule="evenodd"
            d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z">
          </path>
        </svg>
      </button>
      <a href="/" class="flex items-center">
        <img v-if="collapsed" src="/images/logo.png" class="h-8 w-8" alt="TPJ" />
        <img v-else src="/images/logo-text-horizontal.png" class="h-10" alt="TPJ" />
      </a>
    </div>
    <div class="h-screen px-3 pb-[40%] overflow-y-auto bg-white dark:bg-gray-800 pt-6">
      <ul class="space-y-1">
        <template v-for="(item, index) in navItems" :key="index">
          <template v-if="item.authorized">
            <li v-if="item?.isDivider">
              <div class="py-2 text-xs text-gray-500 dark:text-gray-400">
                {{ collapsed ? shortLabel(item.label) : item.label }}
              </div>
            </li>
            <li v-else-if="item.children && item.children.length > 0" class="static group">
              <!-- Parent Item -->
              <button v-show="!collapsed" type="button" :class="{
                'text-primary-500 font-bold': item.isActive,
                'text-primary-500 hover:bg-green-100 hover:text-primary-500': !item.isActive,
              }" class="flex items-center w-full p-2 rounded" :aria-controls="item.key"
                :data-collapse-toggle="item.key">
                <i :class="item.icon"></i>
                <span class="flex-1 ms-3 text-left rtl:text-right whitespace-nowrap">{{ item.label
                }}</span>
                <span v-if="item.badge"
                  class="inline-flex items-center justify-center w-4 h-4 ms-3 me-1 text-xs text-white font-bold bg-red-400 rounded-full">
                  {{ item.badge }}
                </span>
                <i class="ri-arrow-down-s-line"></i>
              </button>
              <button v-show="collapsed" type="button" :class="{
                'text-primary-500 font-bold': item.isActive,
                'text-primary-500 hover:bg-green-100 hover:text-primary-500': !item.isActive,
              }" class="flex items-center w-full p-2 rounded" @mouseenter="showSubMenu(item.key)"
                @mouseleave="hideDelaySubMenu(item.key)">
                <i :class="item.icon"></i>
                <span v-if="item.badge"
                  class="inline-flex items-center justify-center w-4 h-4 px-1 mx-1 text-xs text-white font-bold bg-red-400 rounded-full">
                  {{ item.badge }}
                </span>
                <i class="ri-arrow-down-s-line"></i>
              </button>

              <!-- Submenu Popup -->
              <ul v-show="collapsed && showPopup === item.key" @mouseenter="showSubMenu(item.key)"
                @mouseleave="hideSubMenu(item.key)"
                class="absolute left-full w-40 bg-white rounded shadow p-2 space-y-1 z-50" style="margin-top: -35px;">
                <li v-for="(itemChild, indexChild) in item.children" :key="indexChild">
                  <template v-if="itemChild.authorized">
                    <Link :class="{
                      'bg-green-100 text-primary-500': itemChild.isActive,
                      'text-primary-500 hover:bg-green-100 hover:text-primary-500': !itemChild.isActive,
                    }" :href="route(itemChild.routeName)" class="flex items-center p-2 rounded dark:text-white group">
                    <i :class="itemChild.icon"></i>
                    <span class="ms-3">{{ itemChild.label }}</span>
                    <span v-if="itemChild.badge"
                      class="inline-flex items-center justify-center w-4 h-4 ms-auto me-4 text-xs text-white font-bold bg-red-400 rounded-full">
                      {{ itemChild.badge }}
                    </span>
                    </Link>
                  </template>
                </li>
              </ul>

              <!-- Submenu when not collapsed -->
              <ul :id="item.key" v-show="!collapsed" class="hidden py-2 space-y-1 ms-3">
                <li v-for="(itemChild, indexChild) in item.children" :key="indexChild">
                  <template v-if="itemChild.authorized">
                    <Link :class="{
                      'bg-green-400 text-white font-bold': itemChild.isActive,
                      'text-primary-500 hover:bg-green-100 hover:text-primary-500': !itemChild.isActive,
                    }" :href="route(itemChild.routeName)" class="flex items-center p-2 rounded dark:text-white group">
                    <i :class="itemChild.icon"></i>
                    <span class="ms-3">{{ itemChild.label }}</span>
                    <span v-if="itemChild.badge"
                      class="inline-flex items-center justify-center w-4 h-4 ms-auto me-4 text-xs text-white font-bold bg-red-400 rounded-full">
                      {{ itemChild.badge }}
                    </span>
                    </Link>
                  </template>
                </li>
              </ul>
            </li>
            <li v-else>
              <Link :class="{
                'bg-green-400 text-white font-bold': item.isActive,
                'text-primary-500 hover:bg-green-100 hover:text-primary-500': !item.isActive,
              }" :href="route(item.routeName)" class="flex items-center p-2 rounded dark:text-white group">
              <i :class="[item.icon, {
                'me-1': collapsed
              }]"></i>
              <span v-if="!collapsed" class="ms-3">{{ item.label }}</span>
              <span v-if="item.badge"
                class="inline-flex items-center justify-center w-4 h-4 px-1 ms-auto me-4 text-xs text-white font-bold bg-red-400 rounded-full">
                {{ item.badge }}
              </span>
              </Link>
            </li>
          </template>
        </template>
      </ul>
    </div>
  </aside>
</template>

<script>
import { initFlowbite } from "flowbite";
import Sidebar from "@/Mixins/Sidebar.js";

export default {
  mixins: [Sidebar],
  props: {
    waitingConsultationCount: {
      type: Number,
      default: 0,
    },
    waitingVerificationCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      collapsed: false,
      showPopup: localStorage.getItem("showPopup"),
      showPopupDelay: null,
      menus: [
        {
          label: "Dashboard",
          key: "dashboard",
          routeName: "dashboard",
          authorized: true,
          icon: "ri-home-5-line",
        },
        {
          label: 'MANAGEMENT',
          isDivider: true,
          routeName: "dashboard",
          authorized: this.authCan("view users") || this.authCan("view roles") ||
            this.authCan("view consultation-categories") ||
            this.authCan("view news") || this.authCan("view service-improvements") || this.authCan("view events") ||
            this.authCan("view promotions") || this.authCan("view tpj-edu") ||
            this.authCan("view customers") || this.authCan("view customer-channels"),
        },
        {
          label: "Manajemen Staff",
          key: "user-parent",
          routeName: "users.index",
          authorized: this.authCan("view users") || this.authCan("view roles"),
          icon: "ri-user-settings-line",
          children: [
            {
              label: "Staff",
              key: "users",
              routeName: "users.index",
              authorized: this.authCan("view users"),
            }, {
              label: "Role & Permission",
              key: "roles",
              routeName: "roles.index",
              authorized: this.authCan("view roles"),
            },
          ]
        },
        {
          label: "Master Layanan",
          key: "service-master",
          routeName: "consultation-categories.index",
          authorized: this.authCan("view consultation-categories"),
          icon: "ri-database-2-line",
          children: [
            {
              label: "Kategori Konsultasi",
              key: "consultation-categories",
              routeName: "consultation-categories.index",
              authorized: this.authCan("view consultation-categories"),
            },
          ]
        },
        {
          label: "Informasi",
          key: "information-parent",
          routeName: "users.index",
          authorized: this.authCan("view news") || this.authCan("view service-improvements") || this.authCan("view events") || this.authCan("view promotions") || this.authCan("view tpj-edu"),
          icon: "ri-news-line",
          children: [
            {
              label: "Berita",
              key: "news",
              routeName: "news.index",
              authorized: this.authCan("view news"),
            },
            {
              label: "Penyempurnaan Layanan",
              key: "service-improvements",
              routeName: "service-improvements.index",
              authorized: this.authCan("view service-improvements"),
            },
            {
              label: "TPJ EDU",
              key: "roles",
              routeName: "tpj-edu.index",
              authorized: this.authCan("view tpj-edu"),
            },
            {
              label: "Promo",
              key: "promotions",
              routeName: "promotions.index",
              authorized: this.authCan("view promotions"),
            },
            {
              label: "Event",
              key: "events",
              routeName: "events.index",
              authorized: this.authCan("view events"),
            },
            {
              label: "Splash",
              key: "splashes",
              routeName: "splashes.index",
              authorized: this.authCan("view splashes"),
            },
          ]
        },
        {
          label: "Manajemen User",
          key: "management-user",
          routeName: "customer-channels.index",
          authorized: this.authCan("view customers") || this.authCan("view customer-channels"),
          icon: "ri-group-3-line",
          badge: this.waitingVerificationCount,
          children: [
            {
              label: "User",
              key: "customers",
              routeName: "customers.index",
              authorized: this.authCan("view customers"),
              badge: this.waitingVerificationCount,
            }, {
              label: "Customer",
              key: "customer-channels",
              routeName: "customer-channels.index",
              authorized: this.authCan("view customer-channels"),
            },
          ]
        },
        {
          label: 'LAYANAN',
          isDivider: true,
          routeName: "dashboard",
          authorized: this.authCan("view consultations") || this.authCan("view customer-registrations") || this.authCan("view change-names"),
        },
        {
          label: "Konsultasi",
          key: "consultation",
          routeName: "consultations.index",
          authorized: this.authCan("view consultations"),
          icon: "ri-customer-service-2-line",
          badge: this.waitingConsultationCount,
        },
        {
          label: "Pasang Baru",
          key: "customer-registration",
          routeName: "customer-registrations.index",
          authorized: this.authCan("view customer-registrations"),
          icon: "ri-apps-2-add-line",
        },
        {
          label: "Balik Nama",
          key: "change-name",
          routeName: "change-names.index",
          authorized: this.authCan("view change-names"),
          icon: "ri-info-card-line",
        },
      ],
    };
  },
  watch: {
    collapsed: {
      handler() {
        this.$emit("toggleCollapse", this.collapsed);
      },
    },
    waitingConsultationCount: {
      handler() {
        this.menus.find((item) => item.key === "consultation").badge = this.waitingConsultationCount;
      },
    },
    waitingVerificationCount: {
      handler() {
        this.menus.find((item) => item.key === "management-user").badge = this.waitingVerificationCount;
        this.menus.find((item) => item.key === "management-user").children.find((item) => item.key === "customers").badge = this.waitingVerificationCount;
      },
    }
  },
  methods: {
    toggleSidebar() {
      this.collapsed = !this.collapsed;
    },
    showSubMenu(key) {
      this.showPopup = key;
      this.showPopupDelay = null;

      localStorage.setItem("showPopup", key);
    },
    hideSubMenu() {
      this.showPopup = null;
      this.showPopupDelay = null;

      localStorage.removeItem("showPopup");
    },
    hideDelaySubMenu() {
      this.showPopupDelay = true;
      setTimeout(() => {
        if (this.showPopupDelay) {
          this.showPopup = null;

          localStorage.removeItem("showPopup");
        }
      }, 300)
    },
    shortLabel(label) {
      return label.slice(0, 3) + "...";
    },
  },
};
</script>

<style scoped>
/* Transition for the sidebar */
#logo-sidebar {
  transition: width 0.3s ease;
}
</style>
