<template>
  <div :id="modalId" tabindex="-1" aria-hidden="true" data-modal-backdrop="static" data-modal-keyboard="false"
    class="fixed top-0 left-0 right-0 z-50 hidden p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
    <div class="relative w-full max-w-md max-h-full mx-auto">
      <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
        <button @click="hideModal()" type="button"
          class="absolute top-0 right-0 mt-3 mr-4 text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500 transition ease-in-out duration-150">
          <i class="ri-close-circle-fill text-lg"></i>
        </button>
        <div class="p-6 text-center">
          <h3 class="mb-5 text-lg font-bold whitespace-pre-line" :class="titleColor">
            {{ title }}
          </h3>
          <h3 class="mb-5 font-normal text-gray-500 whitespace-pre-line">
            {{ message }}
          </h3>

          <div class="text-start">
            <text-area v-if="status === 'CANCELED_BY_ADMIN'" label="Alasan Pembatalan" placeholder="Alasan Pembatalan"
              name="cancel_reason" v-model="form.cancel_reason" :errors="$page.props.errors.cancel_reason" required />
            <div v-if="status === 'PROGRESS'">
              <div class="text-sm mb-2">Waktu Konsultasi <span class="text-red-500">*</span></div>
              <div class="flex mb-2" v-if="status === 'PROGRESS'">
                <text-field type="date" placeholder="Tanggal Konsultasi" name="approved_consultation_time_date"
                  v-model="form.approved_consultation_time_date" :errors="$page.props.errors.approved_consultation_time"
                  required :min="minConsultationDate.format('YYYY-MM-DD')" containerClass="flex-grow me-1"
                  @click="$event.target.showPicker && $event.target.showPicker()" />
                <text-field type="time" placeholder="Waktu Konsultasi" name="approved_consultation_time_time"
                  v-model="form.approved_consultation_time_time" required
                  @click="$event.target.showPicker && $event.target.showPicker()" />
              </div>
            </div>
            <text-area v-if="status === 'PROGRESS'" label="Link Konsultasi" placeholder="Link Konsultasi" name="note"
              v-model="form.note" :errors="$page.props.errors.note" required />
          </div>
        </div>
        <div class="flex space-x-2 border-t p-4 border-gray-300">
          <button @click="isProcessing ? forceCloseModal() : hideModal()" type="button"
            class="flex-grow text-white bg-gray-400 hover:bg-gray-500 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 focus:z-10">
            {{ isProcessing ? 'Force Close' : 'Batal' }}
          </button>
          <button type="button" @click="confirm()" :disabled="isProcessing"
            class="flex-grow font-medium rounded-lg text-sm px-5 py-2.5 text-center"
            :class="[modalClass, { 'opacity-50 cursor-not-allowed': isProcessing }]">
            <span v-if="isProcessing" class="inline-flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              Processing...
            </span>
            <span v-else>{{ actionText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useForm } from '@inertiajs/vue3';
import { Modal, initModals } from 'flowbite';
import TextArea from "@/Components/Global/Fields/TextArea.vue";
import TextField from "@/Components/Global/Fields/TextField.vue";
import SelectInput from "@/Components/Global/Fields/SelectInput.vue";
import moment from 'moment';

const props = defineProps({
  modalId: {
    type: String,
    default: 'modal-confirm-id'
  },
  resourceId: {
    type: [Number, String],
    default: null
  },
  status: {
    type: String,
    default: 'WAITING'
  },
  isDetail: {
    type: Boolean,
    default: true
  },
  consultationTime: {
    type: String,
    default: null
  }
});

const form = useForm({
  id: props.resourceId,
  status: props.status,
  is_detail: props.isDetail,
  note: '',
  cancel_reason: '',
  approved_consultation_time: '',
  approved_consultation_time_date: '',
  approved_consultation_time_time: ''
});

const statusConfig = {
  WAITING: {
    label: 'Menunggu Konfirmasi',
    color: 'lime',
    actionText: 'Konfirmasi',
    titleColorClass: 'text-lime-600',
    modalButtonClass: 'text-white bg-lime-600 hover:bg-lime-700 focus:ring-4 focus:outline-none focus:ring-lime-300 dark:focus:ring-lime-800'
  },
  PROGRESS: {
    label: 'Proses',
    color: 'blue',
    actionText: 'Konfirmasi',
    titleColorClass: 'text-blue-600',
    modalButtonClass: 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800'
  },
  DONE: {
    label: 'Selesai',
    color: 'green',
    actionText: 'Selesai',
    titleColorClass: 'text-green-600',
    modalButtonClass: 'text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 dark:focus:ring-green-800'
  },
  CANCELED_BY_CUSTOMER: {
    label: 'Dibatalkan oleh Pelanggan',
    color: 'red',
    actionText: 'Batalkan',
    titleColorClass: 'text-red-600',
    modalButtonClass: 'text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800'
  },
  CANCELED_BY_ADMIN: {
    label: 'Dibatalkan oleh Admin',
    color: 'red',
    actionText: 'Batalkan',
    titleColorClass: 'text-red-600',
    modalButtonClass: 'text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800'
  }
};

const currentStatusInfo = computed(() => statusConfig[props.status] || statusConfig.WAITING);
const minConsultationDate = computed(() => {
  return moment().isAfter(props.consultationTime) ? moment() : moment(props.consultationTime);
})

watch(() => props.consultationTime, (newVal) => {
  if (newVal) {
    const momentDate = moment(newVal);
    form.approved_consultation_time_date = momentDate.format('YYYY-MM-DD');
    form.approved_consultation_time_time = momentDate.format('HH:mm');
  }
}, { immediate: true })

const title = computed(() => `Ubah Status ke ${currentStatusInfo.value.label}`);
const message = computed(() => `Apakah Anda yakin ingin mengubah status menjadi ${currentStatusInfo.value.label}?`);
const actionText = computed(() => currentStatusInfo.value.actionText);
const titleColor = computed(() => currentStatusInfo.value.titleColorClass);
const modalClass = computed(() => currentStatusInfo.value.modalButtonClass);

const modalInstance = ref(null);
const isProcessing = ref(false);
const isInitialized = ref(false);

const cleanupModal = () => {
  if (modalInstance.value) {
    try {
      if (modalInstance.value.destroy) {
        modalInstance.value.destroy();
      }
      if (modalInstance.value.hide) {
        modalInstance.value.hide();
      }
    } catch (error) {
    }
  }
  modalInstance.value = null;
  isInitialized.value = false;
};

const initializeModal = () => {
  cleanupModal();

  const targetEl = document.getElementById(props.modalId);
  if (!targetEl) {
    return false;
  }

  try {
    if (typeof initModals === 'function') {
      initModals();
    }

    modalInstance.value = new Modal(targetEl, {
      backdrop: 'static',
      keyboard: false,
      closable: true
    });

    isInitialized.value = true;
    return true;
  } catch (error) {
    isInitialized.value = false;
    return false;
  }
};

const showModal = () => {
  cleanupModal();

  nextTick(() => {
    const targetEl = document.getElementById(props.modalId);
    if (!targetEl) {
      return;
    }

    targetEl.classList.remove('hidden');
    targetEl.removeAttribute('style');
    targetEl.setAttribute('aria-hidden', 'false');

    try {
      if (typeof initModals === 'function') {
        initModals();
      }

      modalInstance.value = new Modal(targetEl, {
        backdrop: 'static',
        keyboard: false,
        closable: true
      });

      isInitialized.value = true;
      modalInstance.value.show();

    } catch (error) {
      isInitialized.value = false;

      setTimeout(() => {
        try {
          if (typeof initModals === 'function') {
            initModals();
          }
          modalInstance.value = new Modal(targetEl, {
            backdrop: 'static',
            keyboard: false,
            closable: true
          });
          isInitialized.value = true;
          modalInstance.value.show();
        } catch (retryError) {
        }
      }, 100);
    }
  });
};

const hideModal = () => {
  if (modalInstance.value) {
    try {
      modalInstance.value.hide();
    } catch (error) {
    }
  }

  setTimeout(() => {
    cleanupModal();
    isProcessing.value = false;
  }, 300);
};

const confirm = () => {
  if (isProcessing.value) return;

  if (!props.resourceId) {
    return;
  }

  isProcessing.value = true;

  form.status = props.status;
  form.is_detail = props.isDetail;
  form.id = props.resourceId;

  if (props.status === 'PROGRESS') {
    if (form.approved_consultation_time_date && form.approved_consultation_time_time) {
      form.approved_consultation_time = `${form.approved_consultation_time_date} ${form.approved_consultation_time_time}`;
    }

    if (!form.note || !form.approved_consultation_time) {
      isProcessing.value = false;
      return;
    }
  }

  if (props.status === 'CANCELED_BY_ADMIN' && !form.cancel_reason) {
    isProcessing.value = false;
    return;
  }



  const fallbackTimeout = setTimeout(() => {
    isProcessing.value = false;
  }, 30000);

  form.put(route("consultations.update-status", props.resourceId), {
    preserveScroll: true,
    preserveState: false,
    onSuccess: () => {
      clearTimeout(fallbackTimeout);
      isProcessing.value = false;
      forceCloseModal();

      setTimeout(() => {
        forceCloseModal();
      }, 100);
    },
    onError: () => {
      clearTimeout(fallbackTimeout);
      isProcessing.value = false;
    },
    onFinish: () => {
      clearTimeout(fallbackTimeout);
      isProcessing.value = false;
    }
  });
};

onMounted(() => {
  nextTick(() => {
    initModals();
    initializeModal();
  });

  const handleNavigation = () => {
    cleanupModal();
  };

  window.addEventListener('beforeunload', handleNavigation);
  window.addEventListener('popstate', handleNavigation);

  onUnmounted(() => {
    window.removeEventListener('beforeunload', handleNavigation);
    window.removeEventListener('popstate', handleNavigation);
  });
});

onUnmounted(() => {
  cleanupModal();
});

watch(() => props.modalId, () => {
  cleanupModal();
  isProcessing.value = false;

  nextTick(() => {
    initializeModal();
  });
}, { flush: 'post' });

watch(() => props.resourceId, (newVal) => {
  form.id = newVal;
});

watch(() => props.status, (newVal) => {
  form.status = newVal;
});

watch(() => props.isDetail, (newVal) => {
  form.is_detail = newVal;
});

watch(() => [props.modalId, props.resourceId], () => {
  if (isInitialized.value) {
    nextTick(() => {
      initializeModal();
    });
  }
}, { flush: 'post' });

const forceCloseModal = () => {
  const targetEl = document.getElementById(props.modalId);
  if (targetEl) {
    targetEl.classList.add('hidden');
    targetEl.setAttribute('aria-hidden', 'true');
    targetEl.style.display = 'none';
  }

  document.body.classList.remove('overflow-hidden');
  document.body.style.overflow = '';
  document.body.style.paddingRight = '';
  document.documentElement.classList.remove('overflow-hidden');

  const backdrops = document.querySelectorAll('[modal-backdrop], .modal-backdrop, [data-modal-backdrop], .bg-gray-900');
  backdrops.forEach(backdrop => {
    if (backdrop.classList.contains('bg-opacity-50') || backdrop.classList.contains('bg-gray-900')) {
      backdrop.remove();
    }
  });

  cleanupModal();
  isProcessing.value = false;
};

const reinitializeModal = () => {
  cleanupModal();
  return initializeModal();
};

defineExpose({
  showModal,
  hideModal,
  forceCloseModal,
  reinitializeModal,
  initializeModal
});
</script>
