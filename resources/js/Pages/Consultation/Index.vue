<template>
  <div class="w-full mb-4">
    <div class="p-3">
      <div class="grid grid-cols-1 sm:grid-cols-10 gap-2 mb-2">
        <div class="col-span-1 sm:col-span-10 flex items-center space-x-2">
          <slot name="filter" :query="query" :filters="localFilters">
            <filter-panel v-model="query" :filters="localFilters" />
          </slot>
          <div class="flex-grow mb-2 mr-2">
            <search v-model="query.search" />
          </div>
        </div>
      </div>

      <div class="rounded bg-white border">
        <div class="overflow-x-auto px-4 py-3">
          <pagination :per-page-options="[10, 20, 50, 100]" :query="query" @changePerPage="changePerPage" />
          <table class="bg-white w-full rounded-lg text-sm">
            <thead>
              <tr class="text-xs text-gray-700 dark:bg-gold-700 dark:text-gray-400">
                <table-header-column label="No" :enable-sorting="false" />
                <table-header-column label="ID Layanan" :enable-sorting="false" />
                <table-header-column label="Nama" :enable-sorting="false" />
                <table-header-column label="Saluran" :enable-sorting="false" />
                <table-header-column label="Status" :enable-sorting="true" sorting-key="status" :query-sort="query.sort"
                  :query-order="query.order" @sort="sort" />
                <table-header-column label="Tanggal Diajukan" :enable-sorting="true" sorting-key="consultation_time"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Tanggal Disetujui" :enable-sorting="true"
                  sorting-key="approved_consultation_time" :query-sort="query.sort" :query-order="query.order"
                  @sort="sort" />
                <table-header-column label="Aksi" :enable-sorting="false" />
              </tr>
            </thead>
            <tbody>
              <tr class="bg-white border-t hover:bg-gray-50" v-for="(item, index) in resources.data" :key="index">
                <td class="px-3 py-2">
                  <Link :href="route('consultations.show', item.id)" class="text-dark text-decoration-none">{{
                    rowNumber(index)
                  }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('consultations.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.activity_code || '-' }}
                  <div class="text-xs text-gray-500">
                    {{ item.consultation_category_name || '-' }}
                  </div>
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('consultations.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.customer_name || '-' }}
                  <div class="text-xs text-gray-500">
                    {{ item.customer_email || '' }}
                  </div>
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('consultations.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.channel || '-' }}
                  <div class="text-xs text-gray-500">
                    {{ item.channel_fullname || '' }}
                  </div>
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('consultations.show', item.id)"
                    class="flex justify-center items-center px-2 py-1 rounded-full px-2 py-1 rounded-full text-xs text-nowrap"
                    :class="{
                      'bg-lime-100 text-lime-700': item.status === 'WAITING',
                      'bg-blue-100 text-blue-700': item.status === 'PROGRESS',
                      'bg-green-100 text-green-700': item.status === 'DONE',
                      'bg-red-100 text-red-700': item.status === 'CANCELED_BY_CUSTOMER' || item.status === 'CANCELED_BY_ADMIN',
                    }">
                  {{ item.status_description || '-' }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('consultations.show', item.id)"
                    class="text-dark text-decoration-none flex flex-col">
                  <span>
                    {{ moment(item.consultation_time).format('DD MMMM YYYY') }}
                  </span>
                  <span class="text-primary-500 text-xs">
                    {{ moment(item.consultation_time).format('HH:mm') }} WIB
                  </span>
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('consultations.show', item.id)"
                    class="text-dark text-decoration-none flex flex-col">
                  <div v-if="item.approved_consultation_time" class="flex flex-col">
                    <span>
                      {{ moment(item.approved_consultation_time).format('DD MMMM YYYY') }}
                    </span>
                    <span class="text-primary-500 text-xs">
                      {{ moment(item.approved_consultation_time).format('HH:mm') }} WIB
                    </span>
                  </div>
                  <div v-else>-</div>
                  </Link>
                </td>
                <td class="flex items-center px-3 py-2 space-x-1">
                  <Link :href="route('consultations.show', item.id)"
                    class="h-6 w-6 bg-green-400 text-white hover:bg-green-800 p-1 rounded flex items-center justify-center"
                    title="Detail">
                  <i class="ri-eye-fill"></i>
                  </Link>
                  <button v-if="this.authCan('edit consultations') && item.status === 'WAITING'"
                    @click="showVerificationModal(item.id, 'PROGRESS')"
                    class="h-6 w-6 bg-blue-500 text-white hover:bg-blue-700 p-1 rounded-md flex items-center justify-center"
                    title="Proses">
                    <i class="ri-check-fill"></i>
                  </button>
                  <button v-if="this.authCan('edit consultations') && item.status === 'WAITING'"
                    @click="showVerificationModal(item.id, 'CANCELED_BY_ADMIN')"
                    class="h-6 w-6 bg-red-500 text-white hover:bg-red-700 p-1 rounded-md flex items-center justify-center"
                    title="Batalkan">
                    <i class="ri-prohibited-line"></i>
                  </button>
                  <button v-if="this.authCan('edit consultations') && item.status === 'PROGRESS'"
                    @click="showVerificationModal(item.id, 'DONE')"
                    class="h-6 w-6 bg-green-500 text-white hover:bg-green-700 p-1 rounded-md flex items-center justify-center"
                    title="Selesai">
                    <i class="ri-check-fill"></i>
                  </button>
                </td>
              </tr>
              <tr
                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                v-if="resources.total === 0">
                <td colspan="8" class="text-center py-16 text-primary-500 font-bold">
                  <span class="icon-[hugeicons--bookmark-block-01] w-9 h-9"></span>
                  <div>
                    Oops!
                  </div>
                  <div class="mt-8">
                    <span v-if="query.search">Data tidak ditemukan</span>
                    <span v-else>Data belum tersedia</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="border-t">
          <pagination :links="resources.links" :showPerPage=false :per-page-options="[10, 20, 50, 100]" :query="query"
            :total="resources.total" :to="resources.to" :from="resources.from" @changePerPage="changePerPage" />
        </div>
      </div>
    </div>
  </div>

  <ConfirmModal modal-id="confirm-verification" :resource-id="form.id" ref="confirmModalVerification"
    :status="form.status" :is-detail="false" :consultation-time="form.consultation_time" />
</template>

<script>
import Main from "@/Layouts/Main.vue";
import TableHeaderColumn from "@/Components/Global/Table/TableHeaderColumn.vue";
import Search from "@/Components/Global/Table/Search.vue";
import FilterPanel from "@/Components/Global/Table/Filter/FilterPanel.vue";
import Pagination from "@/Components/Global/Table/Pagination.vue";
import ConfirmModal from "./ConfirmModal.vue";

import ActiveStatusMixin from "@/Mixins/ActiveStatusMixin.js"
import PaginationMixin from "@/Mixins/PaginationMixin.js"

export default {
  layout: Main,
  mixins: [ActiveStatusMixin, PaginationMixin],
  components: {
    TableHeaderColumn,
    Search,
    FilterPanel,
    Pagination,
    ConfirmModal,
  },
  props: {
    title: String,
    resources: Object,
    breadcrumb: Array,
    filters: {
      type: Array,
      default: () => [],
    },
    requestQuery: {
      type: Object,
      default: () => { },
    },
  },
  data() {
    return {
      query: {
        search: this.requestQuery?.search || null,
        page: this.requestQuery?.page || 1,
        per_page: this.requestQuery?.per_page || 10,
        sort: this.requestQuery?.sort || 'name',
        order: this.requestQuery?.order,
      },
      key: 0,
      selectedId: null,
      localFilters: this.filters,
      categories: [],
      form: {
        id: null,
        status: null,
        is_detail: false,
        consultation_time: null
      },
    };
  },
  watch: {
    filters: {
      handler(newFilters) {
        this.setLocalFilters();
      },
      deep: true,
    },
    query: {
      handler() {
        this.handleQueryChange();
        this.key++;
      },
      deep: true,
    },
    "query.search"(val) {
      this.query.page = 1;
    },
    requestQuery: {
      handler() {
        this.query = {
          ...this.requestQuery,
          search: this.requestQuery?.search || null,
          page: this.requestQuery?.page || 1,
          per_page: this.requestQuery?.per_page || 10,
          sort: this.requestQuery?.sort,
          order: this.requestQuery?.order,
        };
        this.key = 0;
      },
      deep: true,
    },
  },
  methods: {
    getCategories() {
      if (this.categories.length > 0) return;
      axios.get(route("consultation-categories.get-all"))
        .then(response => {
          this.categories = response.data.data;

          this.setLocalFilters();
        })
        .catch(error => {
          console.error("Error fetching consultation categories:", error);
        });
    },
    setLocalFilters() {
      this.localFilters = this.localFilters.map(filter => {
        if (filter.name === 'category') {
          return {
            ...filter,
            options: this.categories.map(category => ({
              name: category.id,
              value: category.name,
            })),
          };
        }
        return filter;
      });
    },
    showVerificationModal(id, status) {
      this.form.id = id;
      this.form.status = status;
      this.form.consultation_time = this.resources.data.find(item => item.id === id).consultation_time;

      this.$nextTick(() => {
        const modalRef = this.$refs.confirmModalVerification;
        if (modalRef) {
          modalRef.showModal();
        }
      });
    },
  },
  mounted() {
    setTimeout(() => {
      this.key = 1;
      this.getCategories();
    }, 100);
  },
};
</script>
