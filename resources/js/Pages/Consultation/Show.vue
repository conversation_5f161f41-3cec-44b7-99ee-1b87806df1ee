<template>
  <div class="p-3">
    <Link :href="route('consultations.index')"
      class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Ke<PERSON>li
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Informasi General
        </div>

        <div class="flex items-center space-x-2">
          <button v-if="this.authCan('edit consultations') && resource.status === 'WAITING'"
            @click="showVerificationModal('PROGRESS')"
            class="h-6 w-6 bg-blue-500 text-white hover:bg-blue-700 p-1 rounded-md flex items-center justify-center"
            title="Proses">
            <i class="ri-check-fill"></i>
          </button>
          <button v-if="this.authCan('edit consultations') && resource.status === 'WAITING'"
            @click="showVerificationModal('CANCELED_BY_ADMIN')"
            class="h-6 w-6 bg-red-500 text-white hover:bg-red-700 p-1 rounded-md flex items-center justify-center"
            title="Batalkan">
            <i class="ri-prohibited-line"></i>
          </button>
          <button v-if="this.authCan('edit consultations') && resource.status === 'PROGRESS'"
            @click="showVerificationModal('DONE')"
            class="h-6 text-xs px-2 bg-green-500 text-white hover:bg-green-700 p-1 rounded-md flex items-center justify-center"
            title="Selesai">
            <i class="ri-check-fill me-1"></i> Selesai
          </button>
        </div>
      </div>
      <div class="p-3 border-b text-sm">
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">ID Layanan</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.activity?.code || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Nama</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">
            :
            <Link :href="route('customers.show', resource.customer_id)" class="ms-1 hover:text-blue-700">{{
              resource.customer?.name || '-'
            }}</Link>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Email</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.customer?.email || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Telepon</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.customer?.phone || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Saluran</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.customer_channel?.channel || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Kategori</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.category?.name || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Judul</div>
          <div class="col-span-1 sm:col-span-3 break-all">
            : {{ resource.title || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Deskripsi</div>
          <div class="col-span-1 sm:col-span-3 break-all">
            : {{ resource.description || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Status</div>
          <div class="flex items-center col-span-1 sm:col-span-3">:
            <div
              class="flex justify-center items-center px-2 py-1 rounded-full px-2 py-1 ms-1 rounded-full text-xs text-nowrap"
              :class="{
                'bg-lime-100 text-lime-700': resource.status === 'WAITING',
                'bg-blue-100 text-blue-700': resource.status === 'PROGRESS',
                'bg-green-100 text-green-700': resource.status === 'DONE',
                'bg-red-100 text-red-700': resource.status === 'CANCELED_BY_CUSTOMER' || resource.status === 'CANCELED_BY_ADMIN',
              }">
              {{ resource.status_description || '-' }}
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Media Konsultasi</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.media || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Link Konsultasi</div>
          <div class="col-span-1 sm:col-span-3">
            : <a :href="resource.note" target="_blank" class="text-blue-500 hover:underline" v-if="resource.note">
              {{ resource.note || '-' }}
            </a>
            <span v-else>-</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Alasan Dibatalkan</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.cancel_reason || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Tanggal Diajukan</div>
          <div class="flex items-center col-span-1 sm:col-span-3">:
            <span class="text-gray-500 ms-1">{{ moment(resource.consultation_time).format('DD MMMM YYYY HH:mm')
              }} WIB</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Tanggal Disetujui</div>
          <div class="flex items-center col-span-1 sm:col-span-3">:
            <span class="text-gray-500 ms-1">{{ resource.approved_consultation_time ?
              `${moment(resource.approved_consultation_time).format('DD MMMM YYYY HH: mm')} WIB` : '-' }} </span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Tanggal Dibuat</div>
          <div class="flex items-center col-span-1 sm:col-span-3">:
            <span class="text-gray-500 ms-1">{{ moment(resource.created_at).format('DD MMMM YYYY HH:mm') }} WIB</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Terakhir Diubah</div>
          <div class="flex items-center col-span-1 sm:col-span-3">:
            <span class="text-gray-500 ms-1">{{ moment(resource.updated_at).format('DD MMMM YYYY HH:mm') }} WIB</span>
          </div>
        </div>
      </div>
    </div>

    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Informasi Progress Layanan
        </div>
      </div>
      <div class="px-5 py-4 text-sm">
        <ol class="relative border-l border-gray-300 ml-4">
          <li class="mb-5 ml-6" v-for="(item, index) in resource.activity.timelines" :key="index">
            <span class="absolute flex items-center justify-center w-5 h-5 rounded-full -left-2.5"
              :class="index == 0 ? 'bg-green-500' : 'bg-gray-300'">
              <i class="ri-check-line w-4 h-4 mb-0.5 text-white"></i>
            </span>
            <div class="flex justify-between items-start">
              <div class="-mt-1">
                <h3 class="flex items-center font-semibold text-gray-600">
                  {{ moment(item.created_at).locale('id').format('dddd, D MMMM YYYY') }}</h3>
                <p class="text-gray-500">
                  {{ item.description || '-' }}</p>
              </div>
              <time class="block font-normal leading-none text-gray-500">{{
                moment(item.created_at).locale('id').format('HH:mm') }} WIB</time>
            </div>
          </li>
        </ol>
      </div>
    </div>
  </div>

  <ConfirmModal modal-id="confirm-verification-detail" :resource-id="resource.id" ref="confirmModalVerification"
    :status="form.status" :is-detail="true" :consultation-time="resource.consultation_time" />
</template>

<script>
import Main from "@/Layouts/Main.vue";
import ConfirmModal from "./ConfirmModal.vue";

import { initFlowbite } from 'flowbite'

export default {
  layout: Main,
  components: {
    ConfirmModal,
  },
  props: {
    resource: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      loading: false,
      form: {
        status: 'WAITING',
      },
    };
  },
  methods: {
    showVerificationModal(status) {
      this.form.status = status;

      this.$nextTick(() => {
        const modalRef = this.$refs.confirmModalVerification;
        if (modalRef) {
          modalRef.showModal();
        }
      });
    },
  },
  mounted() {
    initFlowbite();
  }
}
</script>

<style>
[data-accordion-icon].rotate-180 {
  @apply -rotate-90;
}
</style>
