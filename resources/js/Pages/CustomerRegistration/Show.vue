<template>
  <div class="p-3">
    <Link :href="route('customer-registrations.index')"
      class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Ke<PERSON><PERSON>
    </Link>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
      <div class="col-span-1">
        <div class="rounded bg-white border h-full">
          <div class="flex justify-between p-3 border-b">
            <div class="text-secondary-1 text-lg">
              Detail Layanan
            </div>

            <div class="flex items-center space-x-2">
              <button @click="syncCustomerChannel()"
                class="h-6 w-6 bg-green-600 text-white hover:bg-green-800 p-1 rounded flex items-center justify-center"
                title="Sinkronkan Data">
                <i class="ri-refresh-line" v-if="!loadingSync"></i>
                <i v-else class="ri-loader-4-line animate-spin text-white"></i>
              </button>
            </div>
          </div>
          <div class="p-3 text-sm">
            <div class="grid grid-cols-1 gap-2">
              <div class="col-span-1">
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">ID Layanan</div>
                  <div class="col-span-1 sm:col-span-2 flex items-center">
                    : {{ resource.subject?.id_transaction ?? '-' }}
                  </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Layanan</div>
                  <div class="col-span-1 sm:col-span-2 flex items-center">
                    : {{ resource.title ?? '-' }}
                  </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Tanggal Pengajuan</div>
                  <div class="col-span-1 sm:col-span-2 flex items-center">
                    : {{ moment(resource.created_at).format('DD MMMM YYYY HH:mm') }} WIB
                  </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Nama Pemohon</div>
                  <div class="col-span-1 sm:col-span-2 flex items-center">
                    :
                    <Link :href="route('customers.show', resource.customer_id)" class="ms-1 hover:text-blue-700">{{
                      resource.customer?.name || '-' }}</Link>
                  </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Profesi</div>
                  <div class="col-span-1 sm:col-span-2 flex items-center">
                    : {{ resource.customer?.profession || '-' }}
                  </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Alamat</div>
                  <div class="col-span-1 sm:col-span-2 flex items-center">
                    : {{ resource.customer?.address || '-' }}
                  </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Status Aktifitas</div>
                  <div class="col-span-1 sm:col-span-2 flex items-center">
                    : <div class="ms-1 px-2 py-0 rounded-full text-xs" title="Lakukan sinkronisasi jika tidak sesuai"
                      :class="[getActStatusBgColor(resource?.status), getActStatusColor(resource?.status)]">
                      {{ resource.status_description || '-' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-1">
        <div class="rounded bg-white border h-full">
          <div class="flex justify-between p-3 border-b">
            <div class="text-secondary-1 text-lg">
              Rincian Pembayaran
            </div>
          </div>
          <div class="p-3 text-sm">
            <div class="grid grid-cols-1 gap-2">
              <div class="col-span-1">
                <div class="grid grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Rekening Pembayaran</div>
                  <div class="col-span-2 flex items-center justify-end">
                    {{ paymentData?.bank_account_number || '-' }}
                  </div>
                </div>
                <div class="grid grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Status Bayar</div>
                  <div class="col-span-2 flex items-center justify-end">
                    {{ latestPayment?.status_description || '-' }}
                  </div>
                </div>
                <div class="grid grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Tanggal Bayar</div>
                  <div class="col-span-2 flex items-center justify-end">
                    {{ latestPayment?.status === 'APPROVED' ?
                      `${moment(latestPayment?.created_at).format('DD MMMM YYYY HH:mm')} WIB` : '-' }}
                  </div>
                </div>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1" v-if="paymentData?.promo_name">
                  <div class="col-span-1 text-gray-500 me-2">Promo</div>
                  <div class="col-span-1 sm:col-span-2 flex items-center justify-end">
                    {{ paymentData?.promo_name }}
                  </div>
                </div>
                <div class="grid grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Biaya Pasang Baru</div>
                  <div class="col-span-2 flex items-center justify-end">
                    {{ toRupiah(paymentData?.new_installation_cost || 0) }}
                  </div>
                </div>
                <div class="grid grid-cols-3 gap-1 mb-1">
                  <div class="col-span-1 text-gray-500 me-2">Biaya Tersier</div>
                  <div class="col-span-2 flex items-center justify-end">
                    {{ toRupiah(paymentData?.tertiary_cost || 0) }}
                  </div>
                </div>
                <div class="grid grid-cols-3 gap-1 mb-1" v-if="paymentData?.discount">
                  <div class="col-span-1 text-gray-500 me-2">Diskon</div>
                  <div class="col-span-2 flex items-center justify-end">
                    {{ toRupiah(paymentData?.discount || 0) }}
                  </div>
                </div>
                <div
                  class="grid grid-cols-3 text-gray-600 text-base font-semibold gap-1 mb-1 border-t border-dashed border-gray-400 pt-2 mt-2">
                  <div class="col-span-1 me-2">Total Bayar</div>
                  <div class="col-span-2 flex items-center justify-end">
                    {{ toRupiah(paymentData?.total_cost || 0) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <CustomerChannel title="Data Calon Pelanggan" :customer_channel="resource.subject" />

    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Informasi Gambar
        </div>
      </div>
      <div class="col-span-1 sm:col-span-3 mt-2">
        <div class="grid grid-cols-1 sm:grid-cols-5 gap-2">
          <div class="col-span-1">
            <div class="text-gray-500 mb-1">KTP</div>
            <a v-if="resource.subject?.id_card_photo" :href="resource.subject?.id_card_photo" target="_blank"
              class="ms-1 flex items-start justify-start">
              <div class="relative group flex items-start justify-start border rounded">
                <img :src="resource.subject?.id_card_photo" alt="KTP"
                  class="h-48 object-contain rounded transition-opacity duration-300 group-hover:opacity-75"
                  onerror="this.onerror=null; this.src='/images/no-image.png';">
                <div
                  class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded flex items-center justify-center">
                  <i class="ri-eye-line text-gray-200 text-xl"></i>
                </div>
              </div>
            </a>
            <span v-else class="text-gray-500 italic">Tidak ada gambar</span>
          </div>
          <div class="col-span-1">
            <div class="text-gray-500 mb-1">Gambar Banner</div>
            <a v-if="resource.subject?.family_card_photo" :href="resource.subject?.family_card_photo" target="_blank"
              class="ms-1 flex items-start justify-start">
              <div class="relative group flex items-start justify-start border rounded">
                <img :src="resource.subject?.family_card_photo" alt="Gambar Banner"
                  class="h-48 object-contain rounded transition-opacity duration-300 group-hover:opacity-75"
                  onerror="this.onerror=null; this.src='/images/no-image.png';">
                <div
                  class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded flex items-center justify-center">
                  <i class="ri-eye-line text-gray-200 text-xl"></i>
                </div>
              </div>
            </a>
            <span v-else
              class="text-gray-500 italic flex items-center justify-center border rounded h-48 hover:bg-gray-100">Tidak
              ada gambar</span>
          </div>
        </div>
      </div>
    </div>

    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Tracking History
        </div>
      </div>

      <div v-if="trackingLoading" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="trackingError" class="text-center py-8 text-red-600 p-3">
        <p>{{ trackingError }}</p>
        <button @click="loadTracking" class="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
          Retry
        </button>
      </div>

      <div v-else-if="trackingData" class="p-3 space-y-3">
        <div class="border border-l-2 border-l-blue-600 rounded-lg p-4 text-sm">
          <div class="flex justify-between items-start mb-4">
            <div>
              <h3 class="font-medium">{{ trackingData.name }}</h3>
              <p class="text-gray-600">ID Transaction: {{ trackingData.id_transaction }}</p>
              <p class="text-gray-600">ID Customer: {{ trackingData.id_customer }}</p>
            </div>
          </div>

          <div class="space-y-3">
            <h4 class="font-medium">Timeline:</h4>
            <div v-for="(track, trackIndex) in trackingData.tracks" :key="trackIndex"
              class="flex items-start space-x-3 pl-4">
              <div class="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <div class="flex-1">
                <div class="flex justify-between items-start">
                  <div>
                    <p class="font-medium">{{ track.step }}</p>
                    <p class="text-gray-600">{{ track.status }}</p>
                    <p class="text-xs text-gray-500">by {{ track.performed_by }} ({{ track.performed_position }})</p>
                  </div>
                  <span class="text-xs text-gray-500">{{ moment(track.performed_at).format('DD MMMM YYYY HH:mm')
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8 text-gray-500 p-3">
        No tracking data available
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Main from "@/Layouts/Main.vue"
import { toRupiah } from '@/Utils/CurrencyUtility.js'
import CustomerChannel from "@/Components/Pages/CustomerChannel/CustomerChannel.vue"
import { usePage } from '@inertiajs/vue3'
import ActivityStatusMixin from "@/Mixins/ActivityStatusMixin.js"

export default {
  layout: Main,
  mixins: [ActivityStatusMixin],

  props: {
    title: String,
    resource: Object
  },

  components: {
    CustomerChannel
  },

  setup() {
    const page = usePage()
    return { page }
  },

  data() {
    return {
      paymentData: null,
      trackingData: null,
      paymentLoading: false,
      paymentError: null,
      trackingLoading: false,
      trackingError: null,
      loadingSync: false
    }
  },

  computed: {
    latestPayment() {
      return this.paymentData
        ? this.paymentData.registration_payments.sort((a, b) => {
          return new Date(b.created_at) - new Date(a.created_at)
        })[0]
        : null
    }
  },

  mounted() {
    if (this.resource.subject?.id) {
      this.loadPayment()
      this.loadTracking()
    }
  },

  methods: {
    toRupiah,
    async loadPayment() {
      this.paymentLoading = true
      this.trackingError = null

      try {
        const response = await axios.get(
          route('customer-registrations.get-payment', this.resource.subject?.id)
        )
        this.paymentData = response.data.data
      } catch (error) {
        this.paymentError = error.response?.data?.message || 'Failed to load payment data'
      } finally {
        this.paymentLoading = false
      }
    },

    async loadTracking() {
      this.trackingLoading = true
      this.trackingError = null

      try {
        const response = await axios.get(
          route('customer-registrations.get-tracking', this.resource.subject?.id)
        )
        this.trackingData = response.data.data
      } catch (error) {
        this.trackingError = error.response?.data?.message || 'Failed to load tracking data'
      } finally {
        this.trackingLoading = false
      }
    },

    async syncCustomerChannel() {
      this.loadingSync = true

      try {
        const response = await axios.post(
          route('customer-registrations.sync', this.resource.subject?.id)
        )

        this.page.props.flash.success = response.data.meta.message
        this.$inertia.reload({
          only: ['resource']
        })
      } catch (error) {
        this.page.props.flash.error = error.response.data.meta.message
      } finally {
        this.loadingSync = false

        setTimeout(() => {
          this.page.props.flash.success = null
          this.page.props.flash.error = null
        }, 3000)
      }
    }
  }
}
</script>
