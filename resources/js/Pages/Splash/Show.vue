<template>
  <div class="p-3">
    <Link :href="route('splashes.index')" class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Kembali
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Informasi General
        </div>

        <div class="flex items-center space-x-2">
          <Link v-if="this.authCan('edit splashes')" :href="route('splashes.edit', resource.id)"
            class="h-6 w-6 bg-primary-500 text-white hover:bg-primary-700 p-1 rounded-md flex items-center justify-center">
          <i class="ri-edit-box-fill"></i>
          </Link>
          <button v-if="this.authCan('delete splashes')" @click="showDeleteModal()"
            class="h-6 w-6 bg-red-600 text-white hover:bg-red-800 p-1 rounded flex items-center justify-center">
            <i class="ri-delete-bin-fill"></i>
          </button>
        </div>
      </div>
      <div class="p-3 grid grid-cols-1 sm:grid-cols-3 gap-5 border-b text-sm">
        <div class="col-span-1 sm:col-span-2">
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Judul</div>
            <div class="col-span-1 sm:col-span-3 break-all">
              : {{ resource.title ?? '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Status</div>
            <div class="flex items-center">:
              <div class="ms-1 px-2 py-0 rounded-full"
                :class="resource.is_active ? `bg-blue-100 text-primary-500` : `bg-red-100 text-red-700`">
                {{ resource.is_active ? 'Active' : 'Inactive' }}
              </div>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Terakhir Diubah</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <span class="text-gray-500 ms-1">{{ moment(resource.updated_at).format('DD MMMM YYYY HH:mm') }}</span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Tanggal Ditambahkan</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <span class="text-gray-500 ms-1">{{ moment(resource.created_at).format('DD MMMM YYYY HH:mm') }}</span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Dibuat Oleh</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <span class="text-gray-500 ms-1">{{ resource.creator ? resource.creator.name : '-' }}</span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Diubah Oleh</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <span class="text-gray-500 ms-1">{{ resource.updater ? resource.updater.name : '-' }}</span>
            </div>
          </div>
        </div>
        <div class="col-span-1">
          <div class="w-48 h-48 flex items-center justify-center border border-dashed border-gray-300">
            <img v-if="resource.image" :src="resource.image" alt="Gambar Edu" class="h-48 object-cover rounded"
              onerror="this.onerror=null; this.src='/images/no-image.png';">
            <span v-else class="text-gray-500">Tidak ada gambar</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ConfirmModal modal-id="confirm-delete-splash-detail" title="Hapus Role?" message="Apakah Anda yakin ingin menghapus data ini?
        data yang dihapus tidak dapat dipulihkan." color="red" ref="confirmModalDelete" @confirm="confirmDelete" />
</template>

<script>
import Main from "@/Layouts/Main.vue";
import ConfirmModal from "@/Components/Global/Modal/ConfirmModal.vue";

import { initFlowbite } from 'flowbite'

export default {
  layout: Main,
  components: {
    ConfirmModal,
  },
  props: {
    resource: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    showDeleteModal() {
      this.$refs.confirmModalDelete.showModal();
    },
    confirmDelete() {
      this.$inertia.delete(route("splashes.destroy", this.resource.id));
    },
  },
  mounted() {
    initFlowbite();
  }
}
</script>

<style>
[data-accordion-icon].rotate-180 {
  @apply -rotate-90;
}
</style>
