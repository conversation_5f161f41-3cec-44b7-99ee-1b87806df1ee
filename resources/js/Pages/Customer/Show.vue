<template>
  <div class="p-3">
    <Link :href="route('customers.index')"
      class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Ke<PERSON>li
    </Link>
    <div class="grid grid-cols-1 sm:grid-cols-4 gap-2 mt-2">
      <div class="col-span-1">
        <div class="rounded bg-white border">
          <div class="p-3">
            <div class="w-100 h-36 rounded bg-no-repeat bg-center bg-contain"
              :style="{ backgroundImage: `url(${resource.avatar ? resource.avatar : '/images/avatar.png'})` }"
              role="img" aria-label="Avatar"></div>
          </div>
          <div class="w-100 border-b borde-2 text-xs text-center" :class="{
            'border-blue-700': resource.verification_status === 'VERIFIED',
            'border-orange-700': resource.verification_status === 'UNVERIFIED',
            'border-lime-700': resource.verification_status === 'WAITING',
            'border-red-700': resource.verification_status === 'REJECTED'
          }"></div>
          <div class="px-4 py-2">
            <div class="font-semibold text-gray-600 mb-1">{{ resource.name }}</div>
            <div class="text-sm text-gray-500 mb-1"><i class="ri-mail-fill"></i> {{ resource.email || '-' }}</div>
            <div class="text-sm text-gray-500 mb-1"><i class="ri-smartphone-fill"></i> {{ resource.phone || '-' }}</div>
            <div class="text-sm text-gray-500 mb-1"><i class="ri-coin-fill"></i> <span class="font-semibold">{{
              resource.point || '0' }}</span> Poin</div>
          </div>
        </div>
        <div class="rounded bg-white border mt-2" v-if="resource.id_card_photo">
          <div class="p-3">
            <div class="w-100 h-36 rounded bg-no-repeat bg-center bg-contain cursor-pointer"
              @click="visitImage(resource.id_card_photo)" :style="{ backgroundImage: `url(${resource.id_card_photo})` }"
              role="img" aria-label="Avatar"></div>
          </div>
          <div class="w-full text-center text-white rounded-b cursor-pointer" :class="{
            'bg-blue-500 hover:bg-blue-600': resource.verification_status === 'VERIFIED',
            'bg-orange-500 hover:bg-orange-600': resource.verification_status === 'UNVERIFIED',
            'bg-lime-500 hover:bg-lime-600': resource.verification_status === 'WAITING',
            'bg-red-500 hover:bg-red-600': resource.verification_status === 'REJECTED'
          }" @click="visitImage(resource.id_card_photo)">KTP</div>
        </div>
      </div>
      <div class="col-span-1 sm:col-span-3 rounded bg-white border">
        <div class="flex justify-between p-3 border-b">
          <div class="text-secondary-1 text-lg">
            Informasi General
          </div>

          <div class="flex items-center space-x-2" v-if="resource.verification_status === 'WAITING'">
            <button v-if="this.authCan('edit customers')" @click="showVerificationModal('VERIFIED')"
              class="bg-blue-600 text-white hover:bg-blue-800 py-1 px-2 rounded flex items-center text-xs">
              <i class="ri-check-fill h-6 w-6 flex items-center justify-center"></i>
              Verifikasi
            </button>
            <button v-if="this.authCan('edit customers')" @click="showVerificationModal('REJECTED')"
              class="bg-red-600 text-white hover:bg-red-800 py-1 px-2 rounded flex items-center text-xs">
              <i class="ri-prohibited-line h-6 w-6 flex items-center justify-center"></i>
              Tolak Verifikasi
            </button>
          </div>
        </div>
        <div class="p-3 text-sm">
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">No KTP</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.no_ktp ?? '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Nama</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.name ?? '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Email</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.email ?? '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Telepon</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.phone ?? '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Jenis Kelamin</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.gender_description || '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Tanggal Lahir / Usia</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ moment(resource.birth_date).format('DD MMMM YYYY') || '-' }}
              <span v-if="resource.age" class="text-gray-500 ms-1">
                ({{ resource.age }} tahun)
              </span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Profesi</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.profession || '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Alamat</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.address || '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Provinsi</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.province ? resource.province.name : '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Kabupaten/Kota</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.regency ? resource.regency.name : '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Kecamatan</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.district ? resource.district.name : '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Desa/Kelurahan</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">
              : {{ resource.village ? resource.village.name : '-' }}
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Status Verifikasi</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <div class="ms-1 px-2 py-0 rounded-full" :class="{
                'bg-blue-100 text-blue-700': resource.verification_status === 'VERIFIED',
                'bg-orange-100 text-orange-700': resource.verification_status === 'UNVERIFIED',
                'bg-lime-100 text-lime-700': resource.verification_status === 'WAITING',
                'bg-red-100 text-red-700': resource.verification_status === 'REJECTED',
              }">
                {{ resource.verification_status_description || '-' }}
              </div>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Diverifikasi Oleh</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <span class="text-gray-500 ms-1">{{ resource.verificator ? resource.verificator.name : '-' }}</span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Tanggal Verifikasi</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <span class="text-gray-500 ms-1">{{ resource.verified_at ?
                moment(resource.verified_at).format('DD MMMM YYYY HH:mm') : ' - ' }} WIB
              </span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Tanggal Dibuat</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <span class="text-gray-500 ms-1">{{ resource.created_at ?
                moment(resource.created_at).format('DD MMMM YYYY HH:mm') : ' - ' }} WIB
              </span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Terakhir Diubah</div>
            <div class="col-span-1 sm:col-span-3 flex items-center">:
              <span class="text-gray-500 ms-1">{{ resource.updated_at ?
                moment(resource.updated_at).format('DD MMMM YYYY HH:mm') : ' - ' }} WIB
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Daftar Saluran
        </div>
      </div>
      <div class="p-3 border-b text-sm">
        <div class="overflow-x-auto px-4 py-3">
          <table class="bg-white w-full rounded-lg text-sm">
            <thead>
              <tr class="text-gray-600 bg-gray-50 font-semibold dark:bg-gold-700 dark:text-gray-400">
                <td class="px-3 py-2">No</td>
                <td class="px-3 py-2">Nomor Saluran</td>
                <td class="px-3 py-2">Nama</td>
                <td class="px-3 py-2">Alamat</td>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(channel, index) in resource.channels" :key="channel.id"
                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', channel.id)" class="hover:text-blue-500">{{ index + 1
                  }}</Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', channel.id)" class="hover:text-blue-500">{{
                    channel.channel || '-' }}</Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', channel.id)" class="hover:text-blue-500">{{
                    channel.fullname || '-' }}</Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', channel.id)" class="hover:text-blue-500">{{
                    channel.address || '-' }}</Link>
                </td>
              </tr>
              <tr v-if="resource.channels.length === 0"
                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td colspan="4" class="px-3 py-2 text-center text-gray-500">Tidak ada saluran yang terdaftar</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <ConfirmModal modal-id="confirm-verification" :title="verificationTitle" :message="verificationMessage"
    actionText="Konfirmasi" :color="verificationColor" ref="confirmModalVerification" @confirm="confirmVerification" />
</template>

<script>
import Main from "@/Layouts/Main.vue";
import ConfirmModal from "@/Components/Global/Modal/ConfirmModal.vue";

export default {
  layout: Main,
  components: {
    ConfirmModal,
  },
  props: {
    resource: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      form: {
        verification_status: null,
        _method: 'PUT',
      },
      verificationTitle: '',
      verificationMessage: '',
      verificationColor: 'blue',
      verificationStatuses: [
        { value: 'VERIFIED', label: 'Diverifikasi' },
        { value: 'REJECTED', label: 'Ditolak' },
      ]
    };
  },
  computed: {
    verificationStatus() {
      return this.verificationStatuses.find(status => status.value === this.form.verification_status) || {};
    },
  },
  methods: {
    showVerificationModal(status) {
      this.form.verification_status = status;
      this.verificationTitle = `Ubah Status Verifikasi ke ${this.verificationStatus.label}`;
      this.verificationMessage = `Apakah Anda yakin ingin mengubah status verifikasi menjadi ${this.verificationStatus.label}?`;
      this.verificationColor = status === 'VERIFIED' ? 'blue' : 'red';

      this.$refs.confirmModalVerification.showModal();
    },
    confirmVerification() {
      this.$inertia.post(route("customers.update-verification", this.resource.id), this.form, {
        onSuccess: () => {
          this.$refs.confirmModalVerification.hideModal();
        },
        onError: () => {
          this.$refs.confirmModalVerification.hideModal();
        }
      });
    },
    visitImage(photoUrl) {
      if (!photoUrl) {
        return;
      }
      window.open(photoUrl, '_blank');
    }
  }
}
</script>
