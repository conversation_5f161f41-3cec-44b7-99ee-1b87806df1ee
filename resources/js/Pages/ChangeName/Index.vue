<template>
  <div class="w-full mb-4">
    <div class="p-3">
      <div class="grid grid-cols-1 sm:grid-cols-10 gap-2 mb-2">
        <div class="col-span-1 sm:col-span-10 flex items-center space-x-2">
          <slot name="filter" :query="query" :filters="filters">
            <filter-panel v-model="query" :filters="filters" />
          </slot>
          <div class="flex-grow mb-2 mr-2">
            <search v-model="query.search" />
          </div>
        </div>
      </div>

      <div class="rounded bg-white border">
        <div class="overflow-x-auto px-4 py-3">
          <pagination :per-page-options="[10, 20, 50, 100]" :query="query" @changePerPage="changePerPage" />
          <table class="bg-white w-full rounded-lg text-sm">
            <thead>
              <tr class="text-xs text-gray-700 bg-white">
                <table-header-column label="No" :enable-sorting="false" />
                <table-header-column label="Transaction ID" :enable-sorting="true" sorting-key="id_transaction"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Pelanggan" :enable-sorting="true" sorting-key="old_name"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Nama Baru" :enable-sorting="true" sorting-key="new_name"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Status" :enable-sorting="true" sorting-key="current_status"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Diajukan Pada" :enable-sorting="true" sorting-key="created_at"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Actions" :enable-sorting="false" />
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in resources.data" :key="item.id" class="bg-white border-t hover:bg-gray-50">
                <td class="px-3 py-2">
                  <Link :href="route('change-names.show', item.id)" class="text-dark text-decoration-none">
                  {{ rowNumber(index) }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('change-names.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.id_transaction }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('change-names.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.old_name }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('change-names.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.new_name }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('change-names.show', item.id)" class="text-dark text-decoration-none">
                  <span class="px-2 py-1 text-xs rounded-full" :class="getStatusClass(item.current_status)">
                    {{ item.current_status_description }}
                  </span>
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('change-names.show', item.id)"
                    class="text-dark text-decoration-none flex flex-col">
                  <span>
                    {{ moment(item.created_at).format('DD MMMM YYYY') }}
                  </span>
                  <span class="text-primary-500">
                    {{ moment(item.created_at).format('HH:mm') }} WIB
                  </span>
                  </Link>
                </td>
                <td class="flex items-center px-3 py-2 space-x-3">
                  <Link :href="route('change-names.show', item.id)"
                    class="h-6 w-6 bg-green-400 text-white hover:bg-green-800 p-1 rounded flex items-center justify-center"
                    title="Detail">
                  <i class="ri-eye-fill"></i>
                  </Link>
                </td>
              </tr>
              <tr v-if="resources.data.length === 0"
                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td colspan="7" class="text-center py-16 text-blue-700 font-bold">
                  <span class="icon-[hugeicons--bookmark-block-01] w-9 h-9"></span>
                  <div>Oops!</div>
                  <div class="mt-8">
                    <span v-if="query.search">Data tidak ditemukan</span>
                    <span v-else>Data permohonan balik nama belum tersedia</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="border-t">
          <pagination :links="resources.links" :showPerPage="false" :per-page-options="[10, 20, 50, 100]" :query="query"
            :total="resources.total" :to="resources.to" :from="resources.from" @changePerPage="changePerPage" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import Main from "@/Layouts/Main.vue"
import TableHeaderColumn from "@/Components/Global/Table/TableHeaderColumn.vue"
import Search from "@/Components/Global/Table/Search.vue"
import FilterPanel from "@/Components/Global/Table/Filter/FilterPanel.vue"
import Pagination from "@/Components/Global/Table/Pagination.vue"

defineOptions({
  layout: Main
})

const props = defineProps({
  resources: Object,
  requestQuery: Object,
  filters: Object
})

const key = ref(0)
const query = ref({
  search: props.requestQuery?.search || null,
  page: props.requestQuery?.page || 1,
  per_page: props.requestQuery?.per_page || 10,
  sort: props.requestQuery?.sort,
  order: props.requestQuery?.order,
  "filter-status": props.requestQuery?.["filter-status"] || null
})

const sort = (sortKey, order) => {
  query.value.sort = sortKey
  query.value.order = order
  handleQueryChange()
}

const handleQueryChange = () => {
  if (key.value > 0) {
    router.visit(window.location.pathname, {
      data: query.value,
      preserveScroll: true,
      preserveState: true,
      replace: true,
    })
  }
}

const changePerPage = (perPage) => {
  query.value.per_page = perPage
  handleQueryChange()
}

const changeSearch = (search) => {
  query.value.search = search
  handleQueryChange()
}

const rowNumber = (index) => {
  let page = props.requestQuery?.page || 1
  const newIndex = Number(index)
  return page * query.value.per_page - query.value.per_page + 1 + newIndex
}

const getStatusClass = (status) => {
  const statusClasses = {
    'PAYMENT': 'bg-yellow-100 text-yellow-800',
    'APPROVED': 'bg-green-100 text-green-800',
    'REJECTED': 'bg-red-100 text-red-800',
    'PENDING': 'bg-gray-100 text-gray-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

watch(query, () => {
  handleQueryChange()
  key.value++
}, { deep: true })

watch(() => query.value.search, () => {
  query.value.page = 1
})

watch(() => props.requestQuery, () => {
  query.value = {
    search: props.requestQuery?.search || null,
    page: props.requestQuery?.page || 1,
    per_page: props.requestQuery?.per_page || 10,
    sort: props.requestQuery?.sort,
    order: props.requestQuery?.order,
    "filter-status": props.requestQuery?.["filter-status"] || null
  }
  key.value = 0
}, { deep: true })

onMounted(() => {
  setTimeout(() => {
    key.value = 1
  }, 100)
})
</script>
