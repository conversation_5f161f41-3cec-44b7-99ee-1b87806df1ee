<template>
  <div class="p-3">
    <Link :href="route('change-names.index')"
      class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> <PERSON><PERSON>li
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Detail Permohonan Balik Nama
        </div>

        <div class="flex items-center space-x-2">
          <button @click="syncChangeName()"
            class="h-6 w-6 bg-green-600 text-white hover:bg-green-800 p-1 rounded flex items-center justify-center"
            title="Sinkronkan Data">
            <i class="ri-refresh-line" v-if="!loadingSync"></i>
            <i v-else class="ri-loader-4-line animate-spin text-white"></i>
          </button>
        </div>
      </div>
      <div class="p-3 border-b text-sm">
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Transaction ID</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.id_transaction || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Pelanggan</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.old_name || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Nama Baru</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.new_name || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Alasan</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.reason || '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Status</div>
          <div class="flex items-center col-span-1 sm:col-span-3">:
            <div
              class="flex justify-center items-center px-2 py-1 rounded-full px-2 py-1 ms-1 rounded-full text-xs text-nowrap"
              :class="getStatusClass(resource.current_status)">
              {{ resource.current_status_description || '-' }}
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Status Aktifitas</div>
          <div class="flex items-center col-span-1 sm:col-span-3">
            : <div class="ms-1 px-2 py-0 rounded-full text-xs" title="Lakukan sinkronisasi jika tidak sesuai"
              :class="[getActStatusBgColor(resource.activity?.status), getActStatusColor(resource.activity?.status)]">
              {{ resource.activity?.status_description || '-' }}
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Tanggal Diajukan</div>
          <div class="flex items-center col-span-1 sm:col-span-3">:
            <span class="text-gray-500 ms-1">{{ moment(resource.created_at).format('DD MMMM YYYY HH:mm') }}</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Terakhir Diubah</div>
          <div class="flex items-center col-span-1 sm:col-span-3">:
            <span class="text-gray-500 ms-1">{{ moment(resource.updated_at).format('DD MMMM YYYY HH:mm') }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Tracking History
        </div>
      </div>

      <div v-if="trackingLoading" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="trackingError" class="text-center py-8 text-red-600 p-3">
        <p>{{ trackingError }}</p>
        <button @click="loadTracking" class="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
          Retry
        </button>
      </div>

      <div v-else-if="trackingData" class="p-3 space-y-3 text-sm">
        <div v-for="(history, index) in trackingData.history" :key="history.id"
          class="border border-l-2 border-l-blue-600 rounded-lg p-4">
          <div class="flex justify-between items-start mb-4">
            <div>
              <h3 class="font-medium">{{ history.old_name }} → {{ history.new_name }}</h3>
              <p class="text-gray-600">{{ history.reason }}</p>
              <p class="text-gray-600">Biaya: {{ toRupiah(history.cost) }}</p>
            </div>
            <span class="px-2 py-0 text-xs rounded-full" :class="getStatusClass(history.current_status)">
              {{ history.current_status }}
            </span>
          </div>

          <div class="space-y-3">
            <h4 class="font-medium">Timeline:</h4>
            <div v-for="(track, trackIndex) in history.tracks" :key="trackIndex"
              class="flex items-start space-x-3 pl-4">
              <div class="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
              <div class="flex-1">
                <div class="flex justify-between items-start">
                  <div>
                    <p class="font-medium">{{ track.step }}</p>
                    <p class="text-gray-600">{{ track.status }}</p>
                    <p class="text-xs text-gray-500">by {{ track.performed_by }}</p>
                  </div>
                  <span class="text-xs text-gray-500">{{ moment(track.performed_at).format('DD MMMM YYYY HH:mm')
                    }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="text-center py-8 text-gray-500 p-3">
        No tracking data available
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import Main from "@/Layouts/Main.vue"
import { toRupiah } from '@/Utils/CurrencyUtility.js'
import { usePage } from '@inertiajs/vue3'
import ActivityStatusMixin from "@/Mixins/ActivityStatusMixin.js"

export default {
  layout: Main,
  mixins: [ActivityStatusMixin],

  props: {
    resource: Object,
    activity: Object,
  },

  setup() {
    const page = usePage()
    return { page }
  },

  data() {
    return {
      trackingData: null,
      trackingLoading: false,
      trackingError: null,
      loadingSync: false
    }
  },

  mounted() {
    this.loadTracking()
  },

  methods: {
    toRupiah,
    async loadTracking() {
      this.trackingLoading = true
      this.trackingError = null

      try {
        const response = await axios.get(
          route('change-names.get-tracking', this.resource.external_customer_id)
        )
        this.trackingData = response.data.data
      } catch (error) {
        this.trackingError =
          error.response?.data?.message || 'Failed to load tracking data'
      } finally {
        this.trackingLoading = false
      }
    },

    async syncChangeName() {
      this.loadingSync = true

      try {
        const response = await axios.post(
          route('change-names.sync', this.resource.id)
        )

        this.page.props.flash.success = response.data.meta.message
        this.$inertia.reload({
          only: ['resource']
        })
      } catch (error) {
        this.page.props.flash.error = error.response.data.meta.message
      } finally {
        this.loadingSync = false

        setTimeout(() => {
          this.page.props.flash.success = null
          this.page.props.flash.error = null
        }, 3000)
      }
    },

    getStatusClass(status) {
      const statusClasses = {
        PAYMENT: 'bg-yellow-100 text-yellow-800',
        APPROVED: 'bg-green-100 text-green-800',
        REJECTED: 'bg-red-100 text-red-800',
        PENDING: 'bg-gray-100 text-gray-800'
      }
      return statusClasses[status] || 'bg-gray-100 text-gray-800'
    }
  }
}
</script>
