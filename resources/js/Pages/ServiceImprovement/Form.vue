<template>
  <div class="p-3">
    <Link :href="route('service-improvements.index')"
      class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> <PERSON><PERSON><PERSON>
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="text-secondary-1 text-lg p-3 border-b">
        Informasi General
      </div>
      <div class="p-3 grid grid-cols-1 sm:grid-cols-2 gap-5 border-b">
        <div class="col-span-1">
          <text-field label="Judul Penyempurnaan Layanan" placeholder="Judul Penyempurnaan Layanan" name="title"
            v-model="form.title" :errors="$page.props.errors.title" required containerClass="mb-3" />
          <text-field label="Tanggal Penyempurnaan Layanan" placeholder="Tanggal Penyempurnaan Layanan" name="date"
            v-model="form.date" :errors="$page.props.errors.date" required type="date" containerClass="mb-3" />
          <select-input label="Zona Wilayah" placeholder="Zona Wilayah" name="zone_id"
            v-model="form.zone_id" :current-val="form.zone_id"
            :errors="$page.props.errors.zone_id" required type="zone_id" containerClass="mb-3"
            :options="zones" />
        </div>
        <div class="col-span-1">
          <div class="flex flex-col mb-6">
            <span class="text-sm text-gray-900 dark:text-gray-300 mb-2">Setel Status Aktif</span>
            <label class="inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="form.is_active" name="is_active" class="sr-only peer">
              <div
                class="relative w-9 h-5 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-primary-500">
              </div>
            </label>
          </div>

          <file-input label="Gambar Penyempurnaan Layanan" name="image" v-model="form.image"
            :errors="$page.props.errors.image" required helpText="Max 2MB, format: jpg, jpeg, png" accept="image/*"
            id="image" />
        </div>
      </div>
      <div class="w-full border-t flex items-center justify-end p-3">
        <button @click="$inertia.visit(route('service-improvements.index'), { replace: true })" type="button"
          class="border bg-white hover:bg-gray-100 rounded-md text-sm px-3 py-2 inline-flex items-center mr-2 font-medium">
          Batal
        </button>
        <action-button :label="buttonLabel" :loading="loading" @click="submit()" />
      </div>
    </div>
  </div>
</template>

<script>
import Main from "@/Layouts/Main.vue";
import TextField from "@/Components/Global/Fields/TextField.vue";
import Message from "@/Components/Global/Fields/Message.vue";
import ActionButton from "@/Components/Global/Fields/ActionButton.vue";
import RichTextEditorTinymce from "@/Components/Global/Fields/RichTextEditorTinymce.vue";
import FileInput from "@/Components/Global/Fields/FileInput.vue";
import SelectInput from "@/Components/Global/Fields/SelectInput.vue";
import RadioGroup from "@/Components/Global/Fields/RadioGroup.vue";
import DatetimePicker from "@/Components/Global/Fields/DatetimePicker.vue";
import { initFlowbite } from 'flowbite'

export default {
  layout: Main,
  components: {
    TextField,
    Message,
    ActionButton,
    RichTextEditorTinymce,
    FileInput,
    SelectInput,
    RadioGroup,
    DatetimePicker,
  },
  props: {
    resource: {
      type: Object,
      default: () => null
    },
    zones: {
      type: Array,
      default: () => []
    },
  },
  watch: {
    'form.title': function (newTitle) {
      this.form.slug = newTitle.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
    }
  },
  data() {
    return {
      loading: false,
      form: {
        id: this.resource ? this.resource.id : null,
        title: this.resource ? this.resource.title : "",
        slug: this.resource ? this.resource.slug : "-",
        image: this.resource ? this.resource.image : "",
        date: this.resource ? this.resource.date : null,
        zone_id: this.resource ? this.resource.zone_id : null,
        zone_name: this.resource ? this.resource.zone_name : null,
        is_active: this.resource ? this.resource.is_active ? true : false : true,
        _method: this.resource ? "put" : "post"
      },
    };
  },
  computed: {
    submitRoute() {
      return this.resource ? route('service-improvements.update', this.form.id) : route('service-improvements.store');
    },
    buttonLabel() {
      return this.title?.includes('Edit') ? "Simpan Perubahan" : "Simpan";
    }
  },
  methods: {
    submit() {
      if (this.loading) {
        return;
      }

      this.loading = true;
      this.$inertia.post(this.submitRoute, this.form, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
          this.loading = false;
        },
        onError: () => {
          this.loading = false;
        },
      });
    }
  },
  mounted() {
    initFlowbite();
  }
}
</script>

<style>
[data-accordion-icon].rotate-180 {
  @apply -rotate-90;
}
</style>
