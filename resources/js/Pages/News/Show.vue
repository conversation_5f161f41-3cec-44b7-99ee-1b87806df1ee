<template>
  <div class="p-3">
    <Link :href="route('news.index')" class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Ke<PERSON>li
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Informasi General
        </div>

        <div class="flex items-center space-x-2">
          <Link v-if="this.authCan('edit news')" :href="route('news.edit', resource.id)"
            class="h-6 w-6 bg-primary-500 text-white hover:bg-primary-700 p-1 rounded-md flex items-center justify-center">
          <i class="ri-edit-box-fill"></i>
          </Link>
          <button v-if="this.authCan('delete news')" @click="showDeleteModal()"
            class="h-6 w-6 bg-red-600 text-white hover:bg-red-800 p-1 rounded flex items-center justify-center">
            <i class="ri-delete-bin-fill"></i>
          </button>
        </div>
      </div>
      <div class="p-3 border-b text-sm">
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Judul Berita</div>
          <div class="col-span-1 sm:col-span-3 break-all">
            : {{ resource.title ?? '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Kategori</div>
          <div class="col-span-1 sm:col-span-3">
            : {{ resource.category_description ?? '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Tanggal Berita</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <span class="text-gray-500 ms-1">{{ moment(resource.date).format('DD MMMM YYYY') }}</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Konten</div>
          <div class='col-span-1 sm:col-span-3 flex'>
            : <p v-if="resource.content" v-html="resource.content" class="text-gray-700 ms-1"></p>
            <span v-else class="text-gray-500 ms-1">Tidak ada konten</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Status</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <div class="ms-1 px-2 py-0 rounded-full"
              :class="resource.is_active ? `bg-blue-100 text-primary-500` : `bg-red-100 text-red-700`">
              {{ resource.is_active ? 'Active' : 'Inactive' }}
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Tanggal Ditambahkan</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <span class="text-gray-500 ms-1">{{ moment(resource.created_at).format('DD MMMM YYYY HH:mm') }}</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Terakhir Diubah</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <span class="text-gray-500 ms-1">{{ moment(resource.updated_at).format('DD MMMM YYYY HH:mm') }}</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Dibuat Oleh</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <span class="text-gray-500 ms-1">{{ resource.creator ? resource.creator.name : '-' }}</span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Diubah Oleh</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <span class="text-gray-500 ms-1">{{ resource.updater ? resource.updater.name : '-' }}</span>
          </div>
        </div>

        <div class="col-span-1 sm:col-span-3 mt-2">
          <div class="text-gray-500 font-semibold mb-1">Daftar Gambar</div>
          <div class="grid grid-cols-1 sm:grid-cols-5 gap-2">
            <div class="col-span-1">
              <div class="text-gray-500 mb-1">Gambar Berita</div>
              <a v-if="resource.image" :href="resource.image" target="_blank"
                class="ms-1 flex items-start justify-start">
                <div class="relative group flex items-start justify-start border rounded">
                  <img :src="resource.image" alt="Gambar Berita"
                    class="h-48 object-contain rounded transition-opacity duration-300 group-hover:opacity-75"
                    onerror="this.onerror=null; this.src='/images/no-image.png';">
                  <div
                    class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded flex items-center justify-center">
                    <i class="ri-eye-line text-gray-200 text-xl"></i>
                  </div>
                </div>
              </a>
              <span v-else class="text-gray-500 italic">Tidak ada gambar</span>
            </div>
            <div class="col-span-1">
              <div class="text-gray-500 mb-1">Gambar Banner</div>
              <a v-if="resource.image_banner" :href="resource.image_banner" target="_blank"
                class="ms-1 flex items-start justify-start">
                <div class="relative group flex items-start justify-start border rounded">
                  <img :src="resource.image_banner" alt="Gambar Banner"
                    class="h-48 object-contain rounded transition-opacity duration-300 group-hover:opacity-75"
                    onerror="this.onerror=null; this.src='/images/no-image.png';">
                  <div
                    class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded flex items-center justify-center">
                    <i class="ri-eye-line text-gray-200 text-xl"></i>
                  </div>
                </div>
              </a>
              <span v-else
                class="text-gray-500 italic flex items-center justify-center border rounded h-48 hover:bg-gray-100">Tidak
                ada
                gambar</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ConfirmModal modal-id="confirm-delete" title="Hapus Role?" message="Apakah Anda yakin ingin menghapus data ini?
        data yang dihapus tidak dapat dipulihkan." color="red" ref="confirmModalDelete" @confirm="confirmDelete" />
</template>

<script>
import Main from "@/Layouts/Main.vue";
import ConfirmModal from "@/Components/Global/Modal/ConfirmModal.vue";

import { initFlowbite } from 'flowbite'

export default {
  layout: Main,
  components: {
    ConfirmModal,
  },
  props: {
    resource: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    showDeleteModal() {
      this.$refs.confirmModalDelete.showModal();
    },
    confirmDelete() {
      this.$inertia.delete(route("news.destroy", this.resource.id));
    },
    getCategoryName(categoryId) {
      const category = this.categories.find(cat => cat.id === categoryId);
      return category ? category.name : 'Tidak ada kategori';
    },
  },
  mounted() {
    initFlowbite();
  }
}
</script>
