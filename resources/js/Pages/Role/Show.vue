<template>
  <div class="p-3">
    <Link :href="route('roles.index')" class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Ke<PERSON>li
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Informasi General
        </div>

        <div class="flex items-center space-x-2">
          <Link v-if="this.authCan('edit users')" :href="route('roles.edit', resource.id)"
            class="h-6 w-6 bg-primary-500 text-white hover:bg-primary-700 p-1 rounded-md flex items-center justify-center">
          <i class="ri-edit-box-fill"></i>
          </Link>
          <button v-if="this.authCan('delete users')" @click="showDeleteModal()"
            class="h-6 w-6 bg-red-600 text-white hover:bg-red-800 p-1 rounded flex items-center justify-center">
            <i class="ri-delete-bin-fill"></i>
          </button>
        </div>
      </div>
      <div class="p-3 grid grid-cols-1 sm:grid-cols-2 gap-5 border-b text-sm">
        <div class="col-span-2">
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Nama Role</div>
            <div class="flex items-center">:
              <span class="text-gray-500 ms-1">{{ resource.name }}</span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Status</div>
            <div class="flex items-center">:
              <div class="ms-1 px-2 py-0 rounded-full"
              :class="resource.is_active ? `bg-blue-100 text-primary-500` : `bg-red-100 text-red-700`">
              {{ resource.is_active ? 'Active' : 'Inactive' }}
            </div>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Ditambahkan Oleh</div>
            <div class="flex items-center">:
              <span class="text-gray-500 ms-1">{{ createdBy ? createdBy.name : '-' }}</span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Diubah Oleh</div>
            <div class="flex items-center">:
              <span class="text-gray-500 ms-1">{{ updatedBy ? updatedBy.name : '-' }}</span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Tanggal Ditambahkan</div>
            <div class="flex items-center">:
              <span class="text-gray-500 ms-1">{{ moment(resource.created_at).format('DD MMMM YYYY HH:mm') }}</span>
            </div>
          </div>
          <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
            <div class="col-span-1 text-gray-500 me-2">Terakhir Diubah</div>
            <div class="flex items-center">:
              <span class="text-gray-500 ms-1">{{ moment(resource.updated_at).format('DD MMMM YYYY HH:mm') }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="text-secondary-1 text-lg p-3">
        Menu Permission
      </div>
      <div class="p-3">
        <div id="accordion-collapse" data-accordion="open">
          <template v-for="parentItem, parentIndex in permissions" :key="parentIndex">
            <h2 :id="`accordion-collapse-heading-${parentIndex}`">
              <button type="button"
                class="flex items-center space-x-2 w-full p-3 font-medium rtl:text-right text-primary-500 bg-gray-100 hover:bg-gray-200 gap-3 border transition-colors duration-200"
                :class="{
                  'rounded-t-xl': parentIndex === 0,
                  'border-b-0': parentIndex < permissions.length - 1
                }" :data-accordion-target="`#accordion-collapse-body-${parentIndex}`" aria-expanded="true"
                :aria-controls="`accordion-collapse-body-${parentIndex}`">
                <i class="ri-arrow-down-s-fill text-primary-500 transition-transform duration-300"
                  data-accordion-icon></i>

                <span>{{ parentItem.name }}</span>
              </button>
            </h2>
            <div :id="`accordion-collapse-body-${parentIndex}`" class="hidden"
              :aria-labelledby="`accordion-collapse-heading-${parentIndex}`">
              <div class="p-5 border border-gray-200 dark:border-gray-700 dark:bg-gray-900"
                :class="parentIndex < permissions.length - 1 ? 'border-b-0' : 'border-t-0'">

                <div class="relative overflow-x-auto">
                  <table class="w-full text-sm text-left rtl:text-right">
                    <thead class="text-xs text-primary-500 bg-gray-100">
                      <tr>
                        <th class="p-3">No.</th>
                        <th class="p-3">Menu</th>
                        <th class="p-3">Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in parentItem.children" :key="index"
                        class="bg-white border-b hover:bg-gray-50">
                        <td class="p-3" valign="top">{{ index + 1 }}</td>
                        <td class="p-3 w-1/3" valign="top">{{ item.name }}</td>
                        <td class="p-3">
                          <div class="flex flex-wrap items-center">
                            <div class="me-3 mb-3" v-for="(permission, indexPermission) in item.permissions"
                              :key="`permission-${indexPermission}`">
                              <label :for="`permission-input-${parentIndex}t-${index}-${indexPermission}`" class="peer">
                                <input type="checkbox" class="rounded focus:ring-0 checked:ring-0 disabled"
                                  :id="`permission-input-${parentIndex}t-${index}-${indexPermission}`"
                                  :value="permission" v-model="form.permissions" disabled />
                                {{ getPermissionAction(permission) }}
                              </label>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>

  <ConfirmModal modal-id="confirm-delete" title="Hapus Role?" message="Apakah Anda yakin ingin menghapus data ini?
        data yang dihapus tidak dapat dipulihkan." color="red" ref="confirmModalDelete" @confirm="confirmDelete" />
</template>

<script>
import Main from "@/Layouts/Main.vue";
import ConfirmModal from "@/Components/Global/Modal/ConfirmModal.vue";

import { initFlowbite } from 'flowbite'

export default {
  layout: Main,
  components: {
    ConfirmModal,
  },
  props: {
    permissions: Array,
    resource: {
      type: Object,
      default: () => ({})
    },
    savedPermissions: {
      type: Array,
      default: () => []
    },
    createdBy: {
      type: Object,
      default: () => ({})
    },
    updatedBy: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      loading: false,
      form: {
        permissions: this.savedPermissions,
        is_active: true,
      },
    };
  },
  methods: {
    getPermissionAction(permission) {
      const label = permission.split(' ')[0];

      return label.charAt(0).toUpperCase() + label.slice(1);
    },
    showDeleteModal() {
      this.$refs.confirmModalDelete.showModal();
    },
    confirmDelete() {
      this.$inertia.delete(route("roles.destroy", this.resource.id));
    },
  },
  mounted() {
    initFlowbite();
  }
}
</script>

<style>
[data-accordion-icon].rotate-180 {
  @apply -rotate-90;
}
</style>
