<template>
  <div class="p-3">
    <Link :href="route('roles.index')" class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Ke<PERSON>li
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="text-secondary-1 text-lg p-3 border-b">
        Informasi General
      </div>
      <div class="p-3 grid grid-cols-1 sm:grid-cols-2 gap-5 border-b">
        <div class="col-span-1">
          <text-field label="Nama Role" placeholder="e.g Superadmin" name="name" v-model="form.name"
            :errors="$page.props.errors.name" required />
        </div>
        <div class="col-span-1 flex flex-col">
          <span class="text-sm text-gray-900 dark:text-gray-300 mb-1">Setel Status Aktif</span>
          <label class="inline-flex items-center cursor-pointer">
            <input type="checkbox" v-model="form.is_active" name="is_active" class="sr-only peer">
            <div
              class="relative w-9 h-5 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-primary-500">
            </div>
          </label>
        </div>
      </div>
      <div class="text-secondary-1 text-lg p-3">
        Menu Permission
      </div>
      <div class="p-3">
        <div id="accordion-collapse" data-accordion="open">
          <template v-for="parentItem, parentIndex in permissions" :key="parentIndex">
            <h2 :id="`accordion-collapse-heading-${parentIndex}`">
              <button type="button"
                class="flex items-center space-x-2 w-full p-3 font-medium rtl:text-right text-primary-500 bg-gray-100 hover:bg-gray-200 gap-3 border transition-colors duration-200"
                :class="{
                  'rounded-t-xl': parentIndex === 0,
                  'border-b-0': parentIndex < permissions.length - 1
                }" :data-accordion-target="`#accordion-collapse-body-${parentIndex}`" aria-expanded="true"
                :aria-controls="`accordion-collapse-body-${parentIndex}`">
                <i class="ri-arrow-down-s-fill text-primary-500 transition-transform duration-300"
                  data-accordion-icon></i>

                <span>{{ parentItem.name }}</span>
              </button>
            </h2>
            <div :id="`accordion-collapse-body-${parentIndex}`" class="hidden"
              :aria-labelledby="`accordion-collapse-heading-${parentIndex}`">
              <div class="p-5 border border-gray-200 dark:border-gray-700 dark:bg-gray-900"
                :class="parentIndex < permissions.length - 1 ? 'border-b-0' : 'border-t-0'">

                <div class="relative overflow-x-auto">
                  <table class="w-full text-sm text-left rtl:text-right">
                    <thead class="text-xs text-primary-500 bg-gray-100">
                      <tr>
                        <th class="p-3">
                          <input type="checkbox" class="rounded focus:ring-0 checked:ring-0"
                            :id="`permission-parent-input-${parentIndex}`" :checked="areAllChildSelected(parentItem)"
                            @click="
                              toggleAllChild(parentItem, $event.target.checked)
                              " />
                        </th>
                        <th class="p-3">No.</th>
                        <th class="p-3">Menu</th>
                        <th class="p-3">Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(item, index) in parentItem.children" :key="index"
                        class="bg-white border-b hover:bg-gray-50">
                        <td class="p-3" valign="top">
                          <input type="checkbox" class="rounded focus:ring-0 checked:ring-0"
                            :id="`permission-child-input-${index}`" :checked="areAllChildPermissionsSelected(item)"
                            @click="
                              toggleAllChildPermissions(item, $event.target.checked)
                              " />
                        </td>
                        <td class="p-3" valign="top">{{ index + 1 }}</td>
                        <td class="p-3 w-1/3" valign="top">{{ item.name }}</td>
                        <td class="p-3">
                          <div class="flex flex-wrap items-center">
                            <div class="me-3 mb-3" v-for="(permission, indexPermission) in item.permissions"
                              :key="`permission-${indexPermission}`">
                              <label :for="`permission-input-${parentIndex}t-${index}-${indexPermission}`" class="peer">
                                <input type="checkbox" class="rounded focus:ring-0 checked:ring-0"
                                  :id="`permission-input-${parentIndex}t-${index}-${indexPermission}`"
                                  :value="permission" v-model="form.permissions" />
                                {{ getPermissionAction(permission) }}
                              </label>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </template>
        </div>

        <message class="mt-1 mb-2" :errors="$page.props.errors.permissions" />
      </div>
      <div class="w-full border-t flex items-center justify-end p-3">
        <button @click="$inertia.visit(route('roles.index'), { replace: true })" type="button"
          class="border bg-white hover:bg-gray-100 rounded-md text-sm px-3 py-2 inline-flex items-center mr-2 font-medium">
          Batal
        </button>
        <action-button :label="buttonLabel" :loading="loading" @click="submit()" />
      </div>
    </div>
  </div>
</template>

<script>
import Main from "@/Layouts/Main.vue";
import TextField from "@/Components/Global/Fields/TextField.vue";
import Message from "@/Components/Global/Fields/Message.vue";
import ActionButton from "@/Components/Global/Fields/ActionButton.vue";
import { initFlowbite } from 'flowbite'

export default {
  layout: Main,
  components: {
    TextField,
    Message,
    ActionButton
  },
  props: {
    permissions: Array,
    resource: {
      type: Object,
      default: () => null
    },
    savedPermissions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      form: {
        id: this.resource ? this.resource.id : null,
        name: this.resource ? this.resource.name : "",
        permissions: this.savedPermissions,
        is_active: this.resource ? this.resource.is_active ? true : false : true,
        _method: this.resource ? "put" : "post"
      },
    };
  },
  computed: {
    submitRoute() {
      return this.resource ? route('roles.update', this.form.id) : route('roles.store');
    },
    buttonLabel() {
      return this.title?.includes('Edit') ? "Simpan Perubahan" : "Simpan";
    }
  },
  methods: {
    getPermissionAction(permission) {
      const label = permission.split(' ')[0];

      return label.charAt(0).toUpperCase() + label.slice(1);
    },
    areAllChildSelected(parent) {
      return parent.children.every((child) => {
        return child.permissions.every((permission) =>
          this.form.permissions.includes(permission)
        );
      });
    },
    toggleAllChild(parent, isChecked) {
      parent.children.forEach((child) => {
        child.permissions.forEach((permission) => {
          const permissionIndex = this.form.permissions.indexOf(permission);

          if (isChecked && permissionIndex === -1) {
            this.form.permissions.push(permission);
          } else if (!isChecked && permissionIndex !== -1) {
            this.form.permissions.splice(permissionIndex, 1);
          }
        });
      });
    },
    areAllChildPermissionsSelected(parentPermission) {
      return parentPermission.permissions.every((permission) =>
        this.form.permissions.includes(permission)
      );
    },
    toggleAllChildPermissions(parentPermission, isChecked) {
      parentPermission.permissions.forEach((permission) => {
        const permissionIndex = this.form.permissions.indexOf(permission);

        if (isChecked && permissionIndex === -1) {
          this.form.permissions.push(permission);
        } else if (!isChecked && permissionIndex !== -1) {
          this.form.permissions.splice(permissionIndex, 1);
        }
      });
    },
    submit() {
      if (this.loading) {
        return;
      }

      this.loading = true;
      this.$inertia.post(this.submitRoute, this.form, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
          this.loading = false;
        },
        onError: () => {
          this.loading = false;
        },
      });
    }
  },
  mounted() {
    initFlowbite();
  }
}
</script>

<style>
[data-accordion-icon].rotate-180 {
  @apply -rotate-90;
}
</style>
