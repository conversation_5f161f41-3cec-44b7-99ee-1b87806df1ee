<template>
  <div class="p-3">
    <Link :href="route('users.index')" class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Ke<PERSON>li
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="text-secondary-1 text-lg p-3 border-b">Informasi General</div>
      <div class="p-3 grid grid-cols-1 sm:grid-cols-2 gap-5 border-b">
        <div class="col-span-1">
          <select-multi containerClass="mb-3" label="Nama" placeholder="Pilih staff" name="name" v-model="form.name"
            :currentVal="form.name" :errors="$page.props.errors.name" required :options="employees" :searchable="true"
            v-if="!form.id" />
          <text-field containerClass="mb-3" label="Nama" placeholder="Nama staff" name="name" v-model="form.name"
            :errors="$page.props.errors.name" disabled requiredLabelOnly v-else />
          <text-field containerClass="mb-3" label="Email" placeholder="Email staff" name="email" v-model="form.email"
            :errors="$page.props.errors.email" disabled requiredLabelOnly />
          <select-input label="Role" placeholder="Pilih role" name="role_id" v-model="form.role_id"
            :currentVal="form.role_id" :errors="$page.props.errors.role_id" required :options="roles" />
        </div>
        <div class="col-span-1">
          <text-field containerClass="mb-3" label="Password" placeholder="*****" name="password_confirmation"
            requiredLabelOnly type="password" v-model="form.password_confirmation"
            :errors="$page.props.errors.password_confirmation"
            :help-text="form.id ? 'Kosongkan jika tidak ingin mengubah password' : 'Kosongkan untuk menggunakan default: Password123#'" />
          <text-field containerClass="mb-3" label="Konfirmasi Password" placeholder="*****" name="password"
            requiredLabelOnly type="password" v-model="form.password" :errors="$page.props.errors.password" />
          <div class="text-sm text-gray-900 dark:text-gray-300 mb-2 mt-3">
            Setel Status Aktif
          </div>
          <div>
            <label class="inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="form.is_active" name="is_active" class="sr-only peer" />
              <div
                class="relative w-9 h-5 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-800">
              </div>
            </label>
          </div>
        </div>
      </div>
      <div class="w-full flex items-center justify-end p-3">
        <button @click="$inertia.visit(route('users.index'), { replace: true })" type="button"
          class="border bg-white hover:bg-gray-100 rounded-md text-sm px-3 py-2 inline-flex items-center mr-2 font-medium">
          Batal
        </button>
        <action-button :label="buttonLabel" :loading="loading" @click="submit()" />
      </div>
    </div>
  </div>
</template>

<script>
import Main from "@/Layouts/Main.vue";
import TextField from "@/Components/Global/Fields/TextField.vue";
import SelectInput from "@/Components/Global/Fields/SelectInput.vue";
import SelectMulti from "@/Components/Global/Fields/SelectMulti.vue";
import Message from "@/Components/Global/Fields/Message.vue";
import ActionButton from "@/Components/Global/Fields/ActionButton.vue";
import axios from "axios";

export default {
  layout: Main,
  components: {
    TextField,
    SelectInput,
    SelectMulti,
    Message,
    ActionButton,
  },
  props: {
    resource: {
      type: Object,
      default: () => null,
    },
    roles: {
      type: Array,
      default: () => [],
    },
    employees: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: "Tambah Staff",
    },
  },
  data() {
    return {
      loading: false,
      form: {
        id: this.resource ? this.resource.id : null,
        name: this.resource ? this.resource.name : "",
        email: this.resource ? this.resource.email : "",
        role_id:
          this.resource && this.resource.roles.length > 0
            ? this.resource.roles[0].id
            : "",
        is_active: this.resource ? !!this.resource.is_active : true,
        _method: this.resource ? "put" : "post",
      },
    };
  },
  watch: {
    "form.name": {
      handler() {
        this.getStaffEmail();
      },
      immediate: true,
    }
  },
  computed: {
    submitRoute() {
      return this.resource
        ? route("users.update", this.form.id)
        : route("users.store");
    },
    buttonLabel() {
      return this.title?.includes("Edit") ? "Simpan Perubahan" : "Simpan";
    },
  },
  methods: {
    getStaffEmail() {
      if (this.form.name) {
        const selectedEmployee = this.employees.find(
          (employee) => employee.value === this.form.name
        );
        if (selectedEmployee) {
          this.form.email = selectedEmployee.email;
        }
      }
    },
    submit() {
      if (this.loading) {
        return;
      }

      this.loading = true;
      this.$inertia.post(this.submitRoute, this.form, {
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
          this.loading = false;
        },
        onError: () => {
          this.loading = false;
        },
      });
    },
  },
};
</script>
