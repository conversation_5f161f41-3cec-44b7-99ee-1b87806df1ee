<template>
  <div class="w-full mb-4">
    <div class="p-3">
      <div class="grid grid-cols-1 lg:grid-cols-10 gap-2 mb-2">
        <div class="col-span-1 sm:col-span-7 flex items-center space-x-2">
          <slot name="filter" :query="query" :filters="filters">
            <filter-panel v-model="query" :filters="filters" />
          </slot>
          <div class="flex-grow mb-2 mr-2">
            <search v-model="query.search" />
          </div>
        </div>

        <div class="col-span-1 lg:col-span-3 flex justify-end space-x-2">
          <Link :href="route('users.create')" v-if="this.authCan('create users')"
            class="text-white font-medium bg-primary-500 hover:bg-primary-700 rounded-lg text-sm px-4 py-2 inline-flex items-center mb-2">
          <i class="ri-add-line mr-1"></i>
          Tambah Staff
          </Link>
        </div>
      </div>

      <div class="rounded bg-white border">
        <div class="overflow-x-auto px-4 py-3">
          <pagination :per-page-options="[10, 20, 50, 100]" :query="query" @changePerPage="changePerPage" />
          <table class="bg-white w-full rounded-lg text-sm">
            <thead>
              <tr class="text-xs text-gray-700 bg-white dark:bg-gold-700 dark:text-gray-400">
                <table-header-column label="No" :enable-sorting="false" />
                <table-header-column label="Nama" :enable-sorting="true" sorting-key="users.name"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Email" :enable-sorting="true" sorting-key="users.email"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Role" :enable-sorting="true" sorting-key="roles.roles"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Status" :enable-sorting="true" sorting-key="users.is_active"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Aksi" :enable-sorting="false" />
              </tr>
            </thead>
            <tbody>
              <tr class="bg-white border-t hover:bg-gray-50" v-for="(item, index) in resources.data" :key="index">
                <td class="px-3 py-2">
                  <Link :href="route('users.show', item.id)" class="text-dark text-decoration-none">{{ rowNumber(index)
                  }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('users.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.name }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('users.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.email }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('users.show', item.id)" class="text-dark text-decoration-none d-flex">
                  <span class="badge bg-primary-subtle text-primary mx-1">
                    {{ item.roles }}
                  </span>
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('users.show', item.id)"
                    class="flex justify-center items-center px-2 py-1 rounded-full">
                  <span class="px-2 py-1 rounded-full" :class="item.is_active
                    ? `bg-blue-100 text-blue-700`
                    : `bg-red-100 text-red-700`
                    ">
                    {{ item.is_active ? "Active" : "Inactive" }}
                  </span>
                  </Link>
                </td>
                <td class="flex items-center px-3 py-2 space-x-3">
                  <Link :href="route('users.show', item.id)"
                    class="h-6 w-6 bg-green-400 text-white hover:bg-green-800 p-1 rounded flex items-center justify-center">
                  <i class="ri-eye-fill"></i>
                  </Link>
                  <Link v-if="this.authCan('edit users')" :href="route('users.edit', item.id)"
                    class="h-6 w-6 bg-blue-800 text-white hover:bg-blue-900 p-1 rounded-md flex items-center justify-center">
                  <i class="ri-edit-box-fill"></i>
                  </Link>
                  <button v-if="this.authCan('delete users')" @click="showDeleteModal(item.id)"
                    class="h-6 w-6 bg-red-600 text-white hover:bg-red-800 p-1 rounded flex items-center justify-center">
                    <i class="ri-delete-bin-fill"></i>
                  </button>
                </td>
              </tr>
              <tr
                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                v-if="resources.total === 0">
                <td colspan="6" class="text-center py-16 text-blue-700 font-bold">
                  <span class="icon-[hugeicons--bookmark-block-01] w-9 h-9"></span>
                  <div>Oops!</div>
                  <div class="mt-8">
                    <span v-if="query.search">Data tidak ditemukan</span>
                    <span v-else>Data Internal Staff belum tersedia</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="border-t">
          <pagination :links="resources.links" :showPerPage="false" :per-page-options="[10, 20, 50, 100]" :query="query"
            :total="resources.total" :to="resources.to" :from="resources.from" @changePerPage="changePerPage" />
        </div>
      </div>
    </div>
  </div>

  <ConfirmModal modal-id="confirm-delete" title="Hapus Data?" message="Apakah Anda yakin ingin menghapus data ini?
  data yang dihapus tidak dapat dipulihkan." color="red" ref="confirmModalDelete" @confirm="confirmDelete" />
</template>

<script>
import Main from "@/Layouts/Main.vue";
import TableHeaderColumn from "@/Components/Global/Table/TableHeaderColumn.vue";
import Search from "@/Components/Global/Table/Search.vue";
import FilterPanel from "@/Components/Global/Table/Filter/FilterPanel.vue";
import Pagination from "@/Components/Global/Table/Pagination.vue";
import ConfirmModal from "@/Components/Global/Modal/ConfirmModal.vue";
import InfoModal from "@/Components/Global/Modal/InfoModal.vue";
import Modal from "@/Components/Modal.vue";
import ActiveStatusMixin from "@/Mixins/ActiveStatusMixin.js";
import PaginationMixin from "@/Mixins/PaginationMixin.js";
import TextField from "@/Components/Global/Fields/TextField.vue";
import ActionButton from "@/Components/Global/Fields/ActionButton.vue";
import Form from "@/Pages/User/Form.vue";
import Show from "@/Pages/User/Show.vue";

export default {
  layout: Main,
  mixins: [ActiveStatusMixin, PaginationMixin],
  components: {
    TableHeaderColumn,
    Search,
    FilterPanel,
    Pagination,
    ConfirmModal,
    InfoModal,
    Modal,
    TextField,
    ActionButton,
    Form,
    Show,
  },
  props: {
    title: String,
    resources: Object,
    breadcrumb: Array,
    filters: {
      type: Array,
      default: () => [],
    },
    requestQuery: {
      type: Object,
      default: () => { },
    },
    roles: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      query: {
        search: this.requestQuery?.search || null,
        page: this.requestQuery?.page || 1,
        per_page: this.requestQuery?.per_page || 10,
        sort: this.requestQuery?.sort || "name",
        order: this.requestQuery?.order,
        "filter-status": this.requestQuery?.["filter-status"] || null,
        "filter-role": this.requestQuery?.["filter-role"] || null,
      },
      key: 0,
      selectedId: null,
      selectedData: null,
    };
  },
  watch: {
    query: {
      handler() {
        this.handleQueryChange();
        this.key++;
      },
      deep: true,
    },
    "query.search"(val) {
      this.query.page = 1;
    },
    requestQuery: {
      handler() {
        this.query = {
          search: this.requestQuery?.search || null,
          page: this.requestQuery?.page || 1,
          per_page: this.requestQuery?.per_page || 10,
          sort: this.requestQuery?.sort,
          order: this.requestQuery?.order,
          "filter-status": this.requestQuery?.["filter-status"] || null,
          "filter-role": this.requestQuery?.["filter-role"] || null,
        };
        this.key = 0;
      },
      deep: true,
    },
  },
  methods: {
    showDeleteModal(id) {
      this.selectedId = id;
      this.$refs.confirmModalDelete.showModal();
    },
    confirmDelete() {
      if (this.selectedId) {
        this.$inertia.delete(route("users.destroy", this.selectedId));
      }
    },
    download() {
      var request = this.requestQuery;
      window.open(this.route("users.export", request), "_blank");
    },
  },
  mounted() {
    setTimeout(() => {
      this.key = 1;
    }, 100);
  },
};
</script>
