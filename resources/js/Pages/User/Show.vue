<template>
  <div class="p-3">
    <Link :href="route('users.index')" class="text-sm space-x-2 text-gray-500 hover:text-gray-700 items-center mb-2">
    <i class="ri-arrow-left-line"></i> Ke<PERSON>li
    </Link>
    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          Informasi General
        </div>

        <div class="flex items-center space-x-2">
          <Link v-if="this.authCan('edit users')" :href="route('users.edit', resource.id)"
            class="h-6 w-6 bg-blue-800 text-white hover:bg-blue-900 p-1 rounded-md flex items-center justify-center">
          <i class="ri-edit-box-fill"></i>
          </Link>
          <button v-if="this.authCan('delete users')" @click="showDeleteModal()"
            class="h-6 w-6 bg-red-600 text-white hover:bg-red-800 p-1 rounded flex items-center justify-center">
            <i class="ri-delete-bin-fill"></i>
          </button>
        </div>
      </div>
      <div class="p-3 border-b text-sm">
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Nama</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">
            : {{ resource.name ?? '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Email</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">
            : {{ resource.email ?? '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Role</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">
            : {{ resource.roles.length > 0 ? resource.roles[0].name : '-' }}
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Status</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <div class="ms-1 px-2 py-0 rounded-full"
              :class="resource.is_active ? `bg-blue-100 text-blue-700` : `bg-red-100 text-red-700`">
              {{ resource.is_active_description }}
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Tanggal Ditambahkan</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <span class="text-gray-500 ms-1">{{ resource.created_at ?
              moment(resource.created_at).format('DD MMMM YYYY HH:mm') : ' - ' }} WIB
            </span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Terakhir Diubah</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <span class="text-gray-500 ms-1">{{ resource.updated_at ?
              moment(resource.updated_at).format('DD MMMM YYYY HH:mm') : ' - ' }} WIB
            </span>
          </div>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-1 mb-1">
          <div class="col-span-1 text-gray-500 me-2">Terakhir Login</div>
          <div class="col-span-1 sm:col-span-3 flex items-center">:
            <span class="text-gray-500 ms-1">{{ resource.last_login ?
              moment(resource.last_login).format('DD MMMM YYYY HH:mm') : ' - ' }} WIB
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ConfirmModal modal-id="confirm-delete" title="Hapus Data?" message="Apakah Anda yakin ingin menghapus data ini?
        data yang dihapus tidak dapat dipulihkan." color="red" ref="confirmModalDelete" @confirm="confirmDelete" />
</template>

<script>
import Main from "@/Layouts/Main.vue";
import ConfirmModal from "@/Components/Global/Modal/ConfirmModal.vue";

export default {
  layout: Main,
  components: {
    ConfirmModal,
  },
  props: {
    resource: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    showDeleteModal() {
      this.$refs.confirmModalDelete.showModal();
    },
    confirmDelete() {
      this.$inertia.delete(route("users.destroy", this.resource.id));
    },
  }
}
</script>
