<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import Checkbox from '@/Components/Checkbox.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import { ref } from 'vue';

defineProps({
  canResetPassword: Boolean,
  status: String,
});

const form = useForm({
  email: '',
  password: '',
  remember: false,
});

const showPassword = ref(false);

const year = new Date().getFullYear();

const submit = () => {
  form.transform(data => ({
    ...data,
    remember: form.remember ? 'on' : '',
  })).post(route('login'), {
    onFinish: () => form.reset('password'),
  });
};
</script>

<template>

  <Head title="Log in" />

  <div class="grid grid-cols-1 md:grid-cols-2 bg-gray-100">
    <div class="col-span-1 min-h-screen p-10">
      <div class="flex flex-col md:justify-center items-center mt-8">
        <img src="/images/logo-text-vertical.png" alt="Perumdam Tirta Pandalungan" class="h-32 mb-8" />

        <div class="w-full sm:max-w-md px-6 py-4 bg-white overflow-hidden sm:rounded-lg">
          <div v-if="status" class="mb-4 font-medium text-sm text-green-600">
            {{ status }}
          </div>

          <form @submit.prevent="submit">
            <div class="text-navy-600 font-bold text-xl mb-2">Selamat Datang Kembali!</div>
            <div class="text-navy-600 text-sm mb-4">Masukkan Email dan Password Anda untuk melanjutkan.</div>
            <div class="mt-2">
              <TextInput id="email" v-model="form.email" type="email" class="mt-1 block w-full" required autofocus
                autocomplete="username" placeholder="Email" />
              <InputError class="mt-2" :message="form.errors.email" />
            </div>

            <div class="mt-6 relative">
              <TextInput id="password" v-model="form.password" :type="showPassword ? 'text' : 'password'"
                class="mt-1 block w-full" required autocomplete="current-password" placeholder="Password" />
              <button type="button" @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 focus:outline-none" tabindex="-1"
                aria-label="Toggle password visibility">
                <i v-if="showPassword" class="ri-eye-fill"></i>
                <i v-else class="ri-eye-off-fill"></i>
              </button>
              <InputError class="mt-2" :message="form.errors.password" />
            </div>

            <div class="block mt-6">

              <label class="inline-flex items-center cursor-pointer">
                <input type="checkbox" v-model="form.remember" name="remember" class="sr-only peer">
                <div
                  class="relative w-9 h-5 bg-gray-200 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-green-400">
                </div>
                <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Remember me</span>
              </label>
            </div>

            <button
              class="w-full bg-green-400 hover:bg-green-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150 my-5"
              :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
              Log in
            </button>
            <div class="flex items-center justify-center text-blue space-x-1 text-sm">
              <span>
                Lupa password?
              </span>
              <Link v-if="canResetPassword" :href="route('password.request')"
                class="underline text-sm text-blue-600 font-bold hover:text-blue-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              Klik disini
              </Link>
              <span>
                untuk bantuan
              </span>
            </div>
          </form>
        </div>

        <div class="mt-16 text-xs text-navy-600">
          Copyright © {{ year }} Perumdam Tirta Pandalungan Kabupaten Jember. All rights reserved.
        </div>
      </div>
    </div>
    <div class="col-span-1 p-2 hidden md:block">
      <div class="rounded-md flex flex-col items-center justify-center"
        style="background-image: url('/images/banner-auth.jpg'); background-size: cover; background-position: center; height: 100%; width: 100%;">
        <span class="text-white text-2xl font-bold">PERUMDAM TIRTA PANDALUNGAN</span>
        <span class="text-white text-lg">Kabupaten Jember</span>
      </div>
    </div>
  </div>
</template>
