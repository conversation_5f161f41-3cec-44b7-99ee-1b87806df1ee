<template>
  <div class="w-full mb-4">
    <div class="p-3">
      <div class="grid grid-cols-1 lg:grid-cols-10 gap-2 mb-2">
        <div class="col-span-1 sm:col-span-7 flex items-center space-x-2">
          <slot name="filter" :query="query" :filters="filters">
            <filter-panel v-model="query" :filters="filters" />
          </slot>
          <div class="flex-grow mb-2 mr-2">
            <search v-model="query.search" />
          </div>
        </div>

        <div class="col-span-1 lg:col-span-3 flex justify-end space-x-2">
        </div>
      </div>

      <div class="rounded bg-white border">
        <div class="overflow-x-auto px-4 py-3">
          <pagination :per-page-options="[10, 20, 50, 100]" :query="query" @changePerPage="changePerPage" />
          <table class="bg-white w-full rounded-lg text-sm">
            <thead>
              <tr class="text-xs text-gray-700 bg-white dark:bg-gold-700 dark:text-gray-400">
                <table-header-column label="No" :enable-sorting="false" />
                <table-header-column label="No KTP" :enable-sorting="true" sorting-key="no_ktp" :query-sort="query.sort"
                  :query-order="query.order" @sort="sort" />
                <table-header-column label="Nama" :enable-sorting="true" sorting-key="fullname" :query-sort="query.sort"
                  :query-order="query.order" @sort="sort" />
                <table-header-column label="Profesi" :enable-sorting="true" sorting-key="profession"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Alamat" :enable-sorting="true" sorting-key="address"
                  :query-sort="query.sort" :query-order="query.order" @sort="sort" />
                <table-header-column label="Status" :enable-sorting="true" sorting-key="status" :query-sort="query.sort"
                  :query-order="query.order" @sort="sort" />
                <table-header-column label="Aksi" :enable-sorting="false" />
              </tr>
            </thead>
            <tbody>
              <tr class="bg-white border-t hover:bg-gray-50" v-for="(item, index) in resources.data" :key="index">
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', item.id)" class="text-dark text-decoration-none">{{
                    rowNumber(index)
                  }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.no_ktp || '-' }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.name || '-' }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', item.id)" class="text-dark text-decoration-none">
                  {{ item.profession || '-' }}
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', item.id)" class="text-dark text-decoration-none d-flex">
                  <span class="badge bg-primary-subtle text-primary mx-1">
                    {{ item.address || '-' }}
                  </span>
                  </Link>
                </td>
                <td class="px-3 py-2">
                  <Link :href="route('customer-channels.show', item.id)"
                    class="flex justify-center items-center px-2 py-1 rounded-full">
                  <span class="px-2 py-1 rounded-full"
                    :class="[getStatusBgColor(item.status), getStatusColor(item.status)]">
                    {{ item.status_description }}
                  </span>
                  </Link>
                </td>
                <td class="flex items-center px-3 py-2 space-x-3">
                  <Link :href="route('customer-channels.show', item.id)"
                    class="h-6 w-6 bg-green-400 text-white hover:bg-green-800 p-1 rounded flex items-center justify-center">
                  <i class="ri-eye-fill"></i>
                  </Link>
                </td>
              </tr>
              <tr
                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                v-if="resources.total === 0">
                <td colspan="7" class="text-center py-16 text-blue-700 font-bold">
                  <span class="icon-[hugeicons--bookmark-block-01] w-9 h-9"></span>
                  <div>Oops!</div>
                  <div class="mt-8">
                    <span v-if="query.search">Data tidak ditemukan</span>
                    <span v-else>Data Internal Staff belum tersedia</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="border-t">
          <pagination :links="resources.links" :showPerPage="false" :per-page-options="[10, 20, 50, 100]" :query="query"
            :total="resources.total" :to="resources.to" :from="resources.from" @changePerPage="changePerPage" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Main from "@/Layouts/Main.vue";
import TableHeaderColumn from "@/Components/Global/Table/TableHeaderColumn.vue";
import Search from "@/Components/Global/Table/Search.vue";
import FilterPanel from "@/Components/Global/Table/Filter/FilterPanel.vue";
import Pagination from "@/Components/Global/Table/Pagination.vue";
import CustomerChannelRegStatusMixin from "@/Mixins/CustomerChannelRegStatusMixin.js";
import PaginationMixin from "@/Mixins/PaginationMixin.js";

export default {
  layout: Main,
  mixins: [CustomerChannelRegStatusMixin, PaginationMixin],
  components: {
    TableHeaderColumn,
    Search,
    FilterPanel,
    Pagination,
  },
  props: {
    title: String,
    resources: Object,
    breadcrumb: Array,
    filters: {
      type: Array,
      default: () => [],
    },
    requestQuery: {
      type: Object,
      default: () => { },
    },
    roles: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      query: {
        search: this.requestQuery?.search || null,
        page: this.requestQuery?.page || 1,
        per_page: this.requestQuery?.per_page || 10,
        sort: this.requestQuery?.sort || "name",
        order: this.requestQuery?.order,
        "filter-status": this.requestQuery?.["filter-status"] || null,
        "filter-role": this.requestQuery?.["filter-role"] || null,
      },
      key: 0,
    };
  },
  watch: {
    query: {
      handler() {
        this.handleQueryChange();
        this.key++;
      },
      deep: true,
    },
    "query.search"(val) {
      this.query.page = 1;
    },
    requestQuery: {
      handler() {
        this.query = {
          search: this.requestQuery?.search || null,
          page: this.requestQuery?.page || 1,
          per_page: this.requestQuery?.per_page || 10,
          sort: this.requestQuery?.sort,
          order: this.requestQuery?.order,
          "filter-status": this.requestQuery?.["filter-status"] || null,
          "filter-role": this.requestQuery?.["filter-role"] || null,
        };
        this.key = 0;
      },
      deep: true,
    },
  },
  mounted() {
    setTimeout(() => {
      this.key = 1;
    }, 100);
  },
};
</script>
