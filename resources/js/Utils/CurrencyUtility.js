export function toRupiah(value, withIdentifier = true) {
    let rupiah
    if (value === null) {
        rupiah = 'Rp 0'
    } else {
        var formatter = new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
        });
        rupiah = formatter.format(value).split(',')[0] + ''; //floor decimal number


    }

    if (!withIdentifier) {
        rupiah = rupiah.replace('Rp', '').trim()
    }
    return rupiah
}

export function toThousand(value, withIdentifier = true) {
    const numberFormatter = new Intl.NumberFormat("id-ID", { style: "decimal" });
    if (isNaN(value)) {
        return numberFormatter.format(Number(value));
    }

    return numberFormatter.format(value);
}