<template>
  <div>
    <div class="rounded bg-white border mt-2">
      <div class="flex justify-between p-3 border-b">
        <div class="text-secondary-1 text-lg">
          {{ title }}
        </div>
      </div>
      <div class="p-3 border-b text-sm" v-if="customer_channel">
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
          <div class="col-span-1">
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">ID SPL</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.no_spl ?? '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Nomor <PERSON></div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.channel ?? '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">No KTP</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.no_ktp ?? '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Nama</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.name ?? '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Nama Lengkap</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.fullname ?? '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Telepon</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.telephone ?? '-' }} / {{ customer_channel?.telephone_2 ?? '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Alamat</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.address || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Alamat KTP</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.address_ktp || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Gambar KTP</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : <a v-if="customer_channel?.id_card_photo || customer_channel?.id_card_photo"
                  :href="customer_channel?.id_card_photo || customer_channel?.id_card_photo" target="_blank"
                  class="text-primary-500 hover:underline ms-1"><i class="ri-image-line"></i> Lihat Gambar</a>
                <span v-else class="text-gray-500 ms-1">Tidak ada gambar</span>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Gambar KK</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : <a v-if="customer_channel?.family_card_photo || customer_channel?.family_card_photo"
                  :href="customer_channel?.family_card_photo || customer_channel?.family_card_photo" target="_blank"
                  class="text-primary-500 hover:underline ms-1"><i class="ri-image-line"></i> Lihat Gambar</a>
                <span v-else class="text-gray-500 ms-1">Tidak ada gambar</span>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Profesi</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.profession || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Status Tanah/Rumah</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.land_status || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Dokumen Tanah</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.land_document || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Jumlah Penghuni</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.occupant || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Luas Bangunan</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.building_area || '-' }} m2
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Gambar Rumah</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : <span v-if="customer_channel?.house_image || customer_channel?.house_image_registration">
                  <a v-if="customer_channel?.house_image_registration"
                    :href="customer_channel?.house_image_registration" target="_blank"
                    class="text-primary-500 hover:underline ms-1"><i class="ri-image-line"></i> Lihat Gambar</a>
                  <a v-if="customer_channel?.house_image" :href="customer_channel?.house_image" target="_blank"
                    class="text-primary-500 hover:underline ms-1">
                    <span v-if="customer_channel?.house_image_registration" class="me-1"> | </span>
                    <i class="ri-image-line"></i> Lihat Gambar</a>
                </span>
                <span v-else class="text-gray-500 ms-1">Tidak ada gambar</span>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1"
              v-if="(customer_channel?.lat && customer_channel?.long) || (customer_channel?.lat_registration && customer_channel?.long_registration)">
              <div class="col-span-1 text-gray-500 me-2">Lokasi Rumah</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : <div v-if="customer_channel?.lat && customer_channel?.long"
                  @click="goToMaps(customer_channel?.lat, customer_channel?.long)"
                  class="cursor-pointer text-primary-500 hover:underline ms-1"><i class="ri-map-pin-line"></i> Lihat
                  Lokasi</div>
                <div v-else-if="customer_channel?.lat_registration && customer_channel?.long_registration"
                  @click="goToMaps(customer_channel?.lat_registration, customer_channel?.long_registration)"
                  class="cursor-pointer text-primary-500 hover:underline ms-1"><i class="ri-map-pin-line"></i> Lihat
                  Lokasi</div>
              </div>
            </div>
          </div>
          <div class="col-span-1">
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Pengunaan</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.used_for || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Penggunaan Lainnya</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.etc || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">No Urut</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.sequence_number || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Tenaga Listrik</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.electrical_power || '-' }} VA
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Nomor Meter</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.meter_number || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Gambar Meter</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : <a v-if="customer_channel?.meter_image || customer_channel?.meter_image"
                  :href="customer_channel?.meter_image || customer_channel?.meter_image" target="_blank"
                  class="text-primary-500 hover:underline ms-1"><i class="ri-image-line"></i> Lihat Gambar</a>
                <span v-else class="text-gray-500 ms-1">Tidak ada gambar</span>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Region</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.region_code || '-' }} / {{ customer_channel?.region_name || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Zona</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.zone_name || '-' }} / {{ customer_channel?.zone_service_area_name || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Desa, Kecamatan</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.name_village || '-' }}, {{ customer_channel?.name_sub_district || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Kelompok Kategori</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.group_name || '-' }} / {{ customer_channel?.group_category || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Kelompok Tipe</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.group_description || '-' }} / {{ customer_channel?.group_customer_type || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Unit</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.unit_name || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Water Meter</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.water_meter_brand || '-' }} / {{ customer_channel?.water_meter_size || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1" v-if="customer_channel?.decline_reason">
              <div class="col-span-1 text-gray-500 me-2">Alasan Ditolak</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">
                : {{ customer_channel?.decline_reason || '-' }}
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Status</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">:
                <div class="ms-1 px-2 py-0 rounded-full text-xs" title="Lakukan sinkronisasi jika tidak sesuai"
                  :class="[getStatusBgColor(customer_channel?.status), getStatusColor(customer_channel?.status)]">
                  {{ customer_channel?.status_description || '-' }}
                </div>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Status Registrasi</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">:
                <div class="ms-1 px-2 py-0 rounded-full text-xs" title="Lakukan sinkronisasi jika tidak sesuai"
                  :class="[getRegStatusBgColor(customer_channel?.status_registration), getRegStatusColor(customer_channel?.status_registration)]">
                  {{ customer_channel?.status_registration_description || '-' }}
                </div>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Tanggal Ditambahkan</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">:
                <span class="text-gray-500 ms-1">{{ customer_channel?.created_at ?
                  moment(customer_channel?.created_at).format('DD MMMM YYYY HH:mm') : ' - ' }} WIB
                </span>
              </div>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-1 mb-1">
              <div class="col-span-1 text-gray-500 me-2">Terakhir Diubah</div>
              <div class="col-span-1 sm:col-span-2 flex items-center">:
                <span class="text-gray-500 ms-1">{{ customer_channel?.updated_at ?
                  moment(customer_channel?.updated_at).format('DD MMMM YYYY HH:mm') : ' - ' }} WIB
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomerChannelRegStatusMixin from "@/Mixins/CustomerChannelRegStatusMixin.js";

export default {
  props: {
    title: String,
    customer_channel: {
      type: Object,
      default: () => ({})
    },
  },
  mixins: [CustomerChannelRegStatusMixin],
  methods: {
    goToMaps(lat, long) {
      window.open(`https://www.google.com/maps/search/?api=1&query=${lat},${long}`, '_blank')
    }
  }
};
</script>
