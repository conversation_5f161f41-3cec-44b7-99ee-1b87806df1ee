<template>
  <div :id="modalId" tabindex="-1"
    class="fixed top-0 left-0 right-0 z-50 hidden p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full animate-bounce-in">
    <div class="relative w-full max-w-md max-h-full" :class="modalAnimationClasses">
      <div class="relative rounded-lg shadow dark:bg-gray-700 overflow-hidden bg-white">
        <button @click="hideModal()" type="button"
          class="absolute top-0 right-0 mt-3 mr-4 text-gray-400 hover:text-gray-500 focus:outline-none focus:text-gray-500 transition ease-in-out duration-150">
          <i class="ri-close-circle-fill text-lg"></i>
        </button>
        <div class="p-6 text-center">
          <div class="flex justify-center">
            <i class="ri-error-warning-fill text-red-500 text-8xl"></i>
          </div>
          <h3 class="mb-5 text-lg font-bold whitespace-pre-line text-red-500">
            Oops!
          </h3>
          <div class="mb-5 text-sm text-gray-500 whitespace-pre-line">
            {{ message }}
          </div>

          <div class="flex justify-center">
            <button @click="hideModal()" type="button"
              class="text-white bg-gray-400 hover:bg-gray-500 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5">
              Tutup
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Modal } from "flowbite";

const props = defineProps({
  modalId: {
    type: String,
    default: "modal-alert-id",
  },
  title: {
    type: String,
    required: true,
  },
  message: {
    type: String,
    required: true,
  },
});

const isEntering = ref(false)
const isExiting = ref(false)

const modalAnimationClasses = computed(() => ({
  'scale-95 opacity-0': isExiting.value,
  'scale-100 opacity-100': isEntering.value
}))

const showModal = () => {
  const targetEl = document.getElementById(props.modalId)
  const modalInstance = new Modal(targetEl)

  isEntering.value = true
  isExiting.value = false

  modalInstance.show()

  setTimeout(() => {
    isEntering.value = false
  }, 300)
}

const hideModal = () => {
  const targetEl = document.getElementById(props.modalId)
  const modalInstance = new Modal(targetEl)

  isExiting.value = true

  setTimeout(() => {
    modalInstance.hide()
  }, 300)
}

defineExpose({
  showModal,
  hideModal
})
</script>
