<template>
  <div :id="uid" ref="dropdownContainer" :class="containerClass">
    <div ref="label" class="flex h-full items-center" @click="toggle">
      <div class="flex h-full w-full">
        <slot name="label" :is-show="show" />
      </div>
    </div>
    <div
      v-show="show"
      ref="dropmenu"
      class="absolute my-2 rounded-md shadow-lg z-10 bg-white border-gray-50"
      :class="[
        {
          'right-0': anchor === anchors[1],
          'left-0': anchor === anchors[0],
          'min-width-0': share,
        },
        dropdownClass,
      ]"
      :style="[
        isTouchBottom ? `bottom: ${heightLabel}px` : null,
        {
          'min-width': `${minWidth}rem`,
        },
      ]"
    >
      <div @click="onDropdownClick">
        <slot name="dropdown" />
      </div>
    </div>
  </div>
</template>

<script>
import { getCurrentInstance } from "vue";

const ANCHORS = ["left", "right"];
const DISPOSABLES = ["passive", "active", "aggressive"];

export default {
  props: {
    anchor: {
      type: String,
      default: "right",
      validator: (val) => ANCHORS.includes(val),
    },
    disposable: {
      type: String,
      default: "active",
      validator: (val) => DISPOSABLES.includes(val),
    },
    disabled: Boolean,
    dropdownClass: String,
    share: {
      type: Boolean,
      default: false,
    },
    containerClass: {
      type: String,
      default: "relative",
    },
    minWidth: {
      type: Number,
      default: 12,
    },
  },
  data() {
    return {
      show: false,
      disposables: DISPOSABLES,
      anchors: ANCHORS,
      heightLabel: 0,
      isTouchBottom: false,
      isTouchTop: false,
      position: "", // top, middle, bottom
      uid: getCurrentInstance().uid,
    };
  },
  mounted() {
    this.updateHeightLabel();

    window.addEventListener("click", this.clickOutside, false);
  },
  updated() {
    this.updateHeightLabel();
  },
  beforeDestroy() {
    window.removeEventListener("click", this.clickOutside, false);
  },
  methods: {
    toggle() {
      if (this.disabled) return;
      this.show = !this.show;
    },
    showMenu() {
      setTimeout(() => {
        this.show = true;

        this.$nextTick(() => {
          this.onDropdownOveflow();
        });
      }, 200);
    },
    hideMenu({ uid, callback }) {
      //is self
      if (uid === this.uid) {
        this.show = false;
        this.$emit("menu-hide");
      }

      if (callback) callback();
    },
    updateHeightLabel() {
      this.heightLabel = this.$refs.label.offsetHeight;
    },
    onDropdownClick() {
      if (this.disposable === this.disposables[2]) {
        this.hideMenu({ uid: this.uid });
      }
    },
    clickOutside() {
      if (!this.show) return;

      const specifiedElement = this.$refs.dropdownContainer;
      if (specifiedElement) {
        const isClickInside = specifiedElement.contains(event.target);
        if (!isClickInside) {
          this.hideMenu({ uid: this.uid });
        }
      }

      this.$nextTick(() => {
        this.onDropdownOveflow();
      });
    },
    onDropdownOveflow() {
      let limitBottomCoor;
      let limitTopCoor;
      let containerCoor;

      // const { height: dropmenuHeight, top: dropdownTop } =
      //   this.$refs.dropmenu.getBoundingClientRect();

      // const { top: containerTop, bottom: containerBottom } =
      //   this.$refs.dropdownContainer.getBoundingClientRect();

      // containerCoor = (containerTop + containerBottom) / 2;

      // const containerNearby = this.hasClass(
      //   this.$refs.dropmenu,
      //   "container-wrap"
      // );
      // if (containerNearby) {
      //   const { bottom, top } = containerNearby.getBoundingClientRect();
      //   limitBottomCoor = bottom;
      //   limitTopCoor = top;
      // } else {
      //   limitBottomCoor = window.innerHeight;
      //   limitTopCoor = 0;
      // }

      // if (
      //   containerCoor + dropmenuHeight > limitBottomCoor &&
      //   containerCoor - dropmenuHeight < limitTopCoor
      // ) {
      //   this.isTouchBottom = true;
      //   this.isTouchTop = true;
      // } else if (containerCoor + dropmenuHeight > limitBottomCoor) {
      //   this.isTouchBottom = true;
      // } else if (containerCoor - dropmenuHeight < limitTopCoor) {
      //   this.isTouchTop = true;
      // } else {
      //   this.isTouchBottom = false;
      //   this.isTouchTop = false;
      // }
    },
    hasClass(element, className) {
      do {
        if (element.className?.split(" ").includes(className)) {
          return element;
        }
        element = element.parentNode;
      } while (element);
      return false;
    },
  },
};
</script>

<style lang="postcss" scoped>
.min-width-0 {
  min-width: 0;
}
</style>
