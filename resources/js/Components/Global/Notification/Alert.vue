<template>
  <InfoModal modal-id="modal-info" :title="messages.success" color="green" ref="infoModal" />
  <AlertModal modal-id="modal-alert" :message="messages.error" color="red" ref="alertModal" />
</template>

<script>
import InfoModal from "@/Components/Global/Modal/InfoModal.vue";
import AlertModal from "@/Components/Global/Modal/AlertModal.vue";

export default {
  components: {
    InfoModal,
    AlertModal
  },
  data() {
    return {
      messages: {},
      alertInterval: null,
    };
  },
  watch: {
    "$page.props.flash": {
      handler() {
        this.showErrorPopup();
      },
      deep: true,
    },
    "$page.props.errors": {
      handler() {
        this.showErrorPopup();
      },
      deep: true,
    },
  },
  methods: {
    showErrorPopup() {
      setTimeout(() => {
        if (
          !this.$page.props.flash.success &&
          !this.$page.props.flash.error &&
          Object.keys(this.$page.props.errors).length === 0
        ) return;

        this.messages = {};
        if (this.$page.props.flash.success || this.$page.props.flash.error) {
          this.messages = this.$page.props.flash;
        } else if (Object.keys(this.$page.props.errors).length > 0) {
          this.messages.error = "Terdapat kesalahan pada form yang Anda isi.";
          this.messages.success = null;
        }

        if (this.$page.props.flash.success || this.messages.success) {
          this.$refs.infoModal.showModal();
        }

        if (this.$page.props.flash.error || this.messages.error) {
          this.$refs.alertModal.showModal();
        }

        this.alertInterval = setTimeout(() => {
          this.$refs.infoModal.hideModal();
          this.$refs.alertModal.hideModal();
        }, 5000);
      }, 100);
    },
    clear() {
      this.messages = {};
      clearInterval(this.alertInterval);
    },
  },
};
</script>
