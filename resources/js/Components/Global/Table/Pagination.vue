<template>
  <div class="sm:flex-1 sm:flex sm:items-center m-3 space-y-2" :class="{
    'sm:justify-center': justifyCenter,
    'sm:justify-between': !justifyCenter,
  }">
    <div class="flex items-center">
      <p class="text-xs leading-5 text-gray-500 flex" v-if="showPerPage">
        <select-input selector-key="value" :options="perPageOptionsValue" v-model="perPage" :current-val="perPage" />
        <span class="ml-2 my-auto">entri per halaman</span>
      </p>
      <div class="flex ms-4">
        <p class="text-xs leading-5 text-gray-500" v-if="total > 0">
          Showing <span class="font-bold">{{ from }}</span> to <span class="font-bold">{{ to }}</span> of
          <span class="font-bold">{{ total }}</span> results
        </p>
      </div>
    </div>
    <nav aria-label="Page navigation example">
      <ul class="inline-flex -space-x-px text-sm">
        <li v-for="(link, index) in links" :key="index" class="cursor-pointer">
          <span v-if="search(link.label, 'laquo')" @click="visit(link.url)"
            class="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-primary-500 bg-white border border-e-0 border-gray-300 hover:border-primary-500 rounded-s-lg hover:bg-primary-500 hover:text-white">
            Previous
          </span>

          <span v-else-if="search(link.label, 'raquo')" @click="visit(link.url)"
            class="flex items-center justify-center px-3 h-8 leading-tight text-primary-500 bg-white border border-s-0 border-gray-300 hover:border-primary-500 rounded-e-lg hover:bg-primary-500 hover:text-white">
            Next
          </span>

          <span v-else class="flex items-center justify-center px-3 h-8 leading-tight border" @click="visit(link.url)"
            :class="link.active
              ? 'border-primary-500 bg-primary-500 text-white'
              : 'border-gray-300 text-gray-500 hover:bg-primary-500 hover:text-white'
              ">{{ link.label }}
          </span>
        </li>
      </ul>
    </nav>
  </div>
</template>

<script>
import SelectInput from "@/Components/Global/Fields/SelectInput.vue";

export default {
  components: {
    SelectInput,
  },
  props: {
    links: {
      type: Array,
      default: () => [],
    },
    query: {
      type: Object,
      default: () => { },
    },
    perPageOptions: {
      type: Array,
      default: () => [10, 20, 50],
    },
    from: {
      type: Number,
      default: () => 0,
    },
    to: {
      type: Number,
      default: () => 0,
    },
    total: {
      type: Number,
      default: () => 0,
    },
    showPerPage: {
      type: Boolean,
      default: () => true,
    },
    justifyCenter: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      perPage: 10,
    };
  },
  computed: {
    perPageOptionsValue() {
      // label value keys
      return this.perPageOptions.map((option) => {
        return {
          label: option,
          value: option,
        };
      });
    },
  },
  watch: {
    perPage: {
      handler: function (val) {
        this.changeFilterPerPage(val);
      },
      immediate: true,
    },
  },
  methods: {
    visit(route) {
      if (route) {
        this.$inertia.visit(route, {
          preserveScroll: true,
          preserveState: true,
        });
      }
    },
    changeFilterPerPage(perPageFilter) {
      this.$emit("changePerPage", perPageFilter);
    },
    search(text, search) {
      return text.search(search) == -1 ? false : true;
    },
  },
};
</script>
