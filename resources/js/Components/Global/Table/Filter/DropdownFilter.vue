<template>
  <div class="flex w-full text-sm">
    <select
      ref="input"
      v-model="selected"
      class="form-select w-full bg-white border border-gray-300 rounded-md p-1.5 text-sm"
    >
      <option :value="null">Select {{ defaultLabel }}</option>
      <option v-for="(option, key) in options" :key="key" :value="option.name">
        {{ option.value }}
      </option>
    </select>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: [Number, String, Boolean],
      default: null,
    },
    paramName: String,
    options: Array,
    defaultLabel: String,
  },
  data() {
    return {
      selected: this.value,
    };
  },
  watch: {
    selected: function (val) {
      this.$emit("filter-changed", {
        key: this.paramName,
        value: val,
      });
    },
    value: function (val) {
      this.selected = val;
    },
  },
  methods: {
    focus() {
      this.$refs.input.focus();
    },
    select() {
      this.$refs.input.select();
    },
  },
};
</script>
