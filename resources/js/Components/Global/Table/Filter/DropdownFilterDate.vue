<template>
  <div class="w-full">
    <VueDatePicker
      v-model="date"
      range
      auto-apply
      :enable-time-picker="false"
      placeholder="<PERSON><PERSON><PERSON>"
    />
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: [Number, String, Boolean],
      default: null,
    },
    paramName: String,
  },
  data() {
    return {
      date: this.value,
      form: {
        start_date: null,
        end_date: null,
      },
    };
  },
  watch: {
    value: {
      handler: function (val) {
        if(!val){
          if(Array.isArray(this.date)){
            this.date = null;
          }
        }
      },
      immediate: true,
    },
    date: function (val) {
      this.form.start_date =
        val && val.length > 0 ? this.moment(val[0]).format("YYYY-MM-DD") : null;
      this.form.end_date =
        val && val.length > 0 ? this.moment(val[1]).format("YYYY-MM-DD") : null;

      this.$emit("filter-changed", {
        key: this.paramName,
        value: {
          0: this.form.start_date,
          1: this.form.end_date,
        },
      });
    },
  },
  mounted() {
    this.date = this.value;
  },
};
</script>

<style >
.dp__range_end, .dp__range_start, .dp__active_date {
    background-color: #8CC43D !important;
    }
    .dp__range_between {
    background: #044C8C !important;
    /* disable border */
    border: none !important;
    /* text color white */
    color: white !important;
    }
    .dp__today {
    /* background-color: #8CC43D !important; */
    color: #8CC43D !important;
    border: none !important;
    }
    .dp__date_hover_end:hover{
    background: #8CC43D !important;
    color: white !important;
    border: none !important;
    }
    .dp__month_year_select {
        color: #044C8C !important;
    }
    .dp__month_year_select:hover {
        background: white !important;
        color: #8CC43D !important;
    }
    .dp__calendar_header {
        color: #A0AEC0 !important;
    }
    .dp__calendar_item {
        color: #044C8C !important;
    }
    .dp__inner_nav {
        color: #044C8C !important;
    }
    .dp__overlay_cell_active {
        background: #044C8C !important;
        color: white !important;
    }
    .dp__overlay_cell:hover {
        background: white !important;
        color: #8CC43D !important;
    }
    .dp__overlay_cell{
        color: #044C8C !important;
    }
</style>
