<template>
  <dropdown-base anchor="left" v-show="filters.length > 0" minWidth="20">
    <template v-slot:label>
      <button type="button"
        class="rounded focus:outline-none focus:border-green-300 focus:shadow-outline-green flex items-center py-2 px-2 mb-2 text-sm"
        :class="`${classes} ${filtered ? 'bg-green-500 hover:bg-primary-500 text-white' : 'bg-white hover:bg-green-100 border border-gray-300'
          }`">
        <span>Filter</span>
        <i class="ml-1 ri-arrow-down-s-line"></i>
      </button>
    </template>
    <template v-slot:dropdown>
      <div class="py-2 w-full shadow">
        <div class="flex px-1">
          <label class="text-gray-500 text-xs"> Filters</label>
          <button class="text-xs text-gray-500 ml-auto" v-if="filtered" @click="clear()">
            (clear)
          </button>
        </div>
        <template v-for="(filter, key) in filtersAndValues">
          <dropdown-filter v-if="filter.componentName === 'dropdown-filter'" class="mt-2 px-1" :value="filter.value"
            :defaultLabel="filter.label" :key="key" :options="filter.options" :paramName="filter.paramName"
            @filter-changed="setFilter" />
          <dropdown-filter-date v-if="filter.componentName === 'dropdown-filter-date'" class="mt-2 px-1"
            :value="filter.value" :key="key" :paramName="filter.paramName" @filter-changed="setFilter" />
        </template>
        <slot name="append"></slot>
      </div>
    </template>
  </dropdown-base>
</template>
<script>
import FieldLabel from "@/Components/Global/Fields/Label.vue";
import DropdownBase from "@/Components/Global/Dropdowns/Base.vue";
import DropdownFilter from "@/Components/Global/Table/Filter/DropdownFilter.vue";
import DropdownFilterDate from "@/Components/Global/Table/Filter/DropdownFilterDate.vue";

export default {
  components: {
    DropdownBase,
    DropdownFilter,
    DropdownFilterDate,
    FieldLabel,
  },
  props: {
    filters: {
      type: Array,
      default: [],
    },
    modelValue: {
      type: Object,
      default: {},
    },
    classes: {
      type: String,
      default: "py-1.5",
    },
  },
  data() {
    return {
      localValue: this.modelValue,
    };
  },
  // check props changes
  watch: {
    modelValue: {
      handler(value) {
        this.localValue['search'] = value['search'];
        this.localValue['per_page'] = value['per_page']
      },
      deep: true,
    },
  },
  methods: {
    setFilter(filter) {
      delete this.localValue[filter.key];
      if (filter.value != null) {
        this.localValue[filter.key] = filter.value;
      }
      this.$emit("update:modelValue", this.localValue);
    },
    clear() {
      for (let i in this.filters) {
        if (this.get(this.localValue, this.filters[i].paramName) != null) {
          delete this.localValue[this.filters[i].paramName];
        }
        if (this.get(this.modelValue, this.filters[i].paramName) != null) {
          delete this.modelValue[this.filters[i].paramName];
        }
      }
    },
    get(object, key) {
      return object[key] ?? null;
    },
  },
  computed: {
    filtered() {
      for (let i in this.filters) {
        if (this.get(this.localValue, this.filters[i].paramName) != null) {
          return true;
        }
      }
      return false;
    },
    filtersAndValues() {
      return this.filters.map((filter) => {
        filter.value = this.get(this.localValue, filter.paramName);
        return filter;
      });
    },
  },
};
</script>
