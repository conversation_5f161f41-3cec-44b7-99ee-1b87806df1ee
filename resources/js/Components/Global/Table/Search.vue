<template>
  <div class="relative w-full">
    <div class="absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none">
      <i class="ri-search-line"></i>
    </div>
    <input type="text" name="search" id="search" :value="modelValue"
      class="w-full focus:shadow-outline border border-gray-300 ps-10 pe-2 py-2 rounded text-sm" :class="classes"
      autocomplete="off" :placeholder="placeholder" @input="changed($event.target.value)" />
  </div>
</template>

<script>
import { debounce } from "lodash";
export default {
  props: {
    modelValue: String,
    placeholder: {
      type: String,
      default: "Search...",
    },
    classes: {
      type: String,
      default: "py-1.5",
    },
  },
  methods: {
    changed: debounce(function (val) {
      this.$emit("update:modelValue", val);
    }, 300),
  },
};
</script>
