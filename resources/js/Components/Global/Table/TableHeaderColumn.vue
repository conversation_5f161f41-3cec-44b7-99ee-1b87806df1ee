<template>
  <th class="py-3 px-3 lg:table-cell text-primary-500" :class="classes">
    <div class="flex items-center cursor-pointer"
      @click="$emit('sort', sortingKey, queryOrder == 'desc' ? 'asc' : 'desc')">
      {{ label }}
      <div v-if="enableSorting" class="ml-2 flex">
        <svg xmlns="http://www.w3.org/2000/svg" width="1.2em" height="1.2em" viewBox="0 0 24 24" :class="querySort == sortingKey && (queryOrder == 'desc' || queryOrder == 'asc')
          ? 'text-primary-500'
          : 'text-gray-400'
          ">
          <!-- <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.2"
            d="M15 19V6.659c0-1.006 0-1.51.309-1.634c.308-.125.672.23 1.398.941L19 8.211M9 5v12.341c0 1.006 0 1.51-.309 1.634c-.308.125-.672-.23-1.398-.941L5 15.789"
            color="currentColor" /> -->
          <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.2"
            d="M4.00008 17.27L0.154077 13.423L0.873076 12.703L4.00008 15.832L7.12708 12.705L7.84708 13.424L4.00008 17.27ZM0.873076 5.32L0.153076 4.6L4.00008 0.753998L7.84608 4.6L7.12708 5.32L4.00008 2.191L0.873076 5.32Z"
            color="currentColor" />
        </svg>
      </div>
    </div>
  </th>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: null,
    },
    enableSorting: {
      type: Boolean,
      default: false,
    },
    sortingKey: {
      type: String,
      default: null,
    },
    querySort: {
      type: String,
      default: null,
    },
    queryOrder: {
      type: String,
      default: "desc",
    },
    classes: {
      type: String,
      default: null,
    },
  },
};
</script>
