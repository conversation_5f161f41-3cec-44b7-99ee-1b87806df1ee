<template>
  <button type="submit" :disabled="loading || disabled"
    class="shadow-sm flex items-center justify-center rounded-md text-white transition duration-150 ease-in-out py-2 px-4 bg-primary-500 hover:bg-blue-700 focus:border-primary-500 border border-transparent leading-5 font-medium text-sm"
    :class="classes">
    <i v-show="loading" class="ri-loader-4-line animate-spin text-white mr-2"></i>
    {{ label }}
  </button>
</template>

<script>
export default {
  props: {
    label: String,
    loading: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    classes: {
      type: String,
      default: "",
    },
  },
};
</script>
