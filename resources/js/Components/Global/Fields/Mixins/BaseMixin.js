import Field from "@/Components/Global/Fields/Field.vue";
export default {
    inheritAttrs: false,
    components: {
        Field,
    },
    props: {
        id: {
            type: String,
        },
        errors: {
            type: [Array, String],
            default: () => [],
        },
        placeholder: {
            type: String,
            default: "",
        },
        inline: {
            type: Boolean,
            default: false,
        },
        labelClass: {
            type: String,
        },
        containerClass: {
            default:
                "focus-within:border-orange-100 focus-within:shadow-none border-grey-40 text-grey-100",
        },
        errorClass: {
            default:
                "border-red-500 text-red-500 placeholder-red-500 focus:border-red-500",
        },
        disabled: Boolean,
    },
    methods: {
        focus() {
            this.$refs.input.focus();
        },
        select() {
            this.$refs.input.select();
        },
        setSelectionRange(start, end) {
            this.$refs.input.setSelectionRange(start, end);
        },
    },
};
