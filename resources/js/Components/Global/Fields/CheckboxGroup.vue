<template>
  <Field v-bind="{ ...$attrs, ...$props }" :helperPosition="helperPosition" borderless>
    <div class="flex flex-wrap " :class="{
      'flex-col -mt-2': align === 'col',
      'flex-row': align === 'row',
    }">
      <label v-for="(option, index) in options" :key="index" class="flex items-center justify-start"
        :class="[optionClass, { 'mt-1': label }]">
        <input :name="id" :value="option.value" :disabled="disabled" :key="'input-' + index" ref="input" v-bind="$attrs"
          v-model="selected" type="checkbox"
          class="form-checkbox transition duration-150 text-primary-500 focus:ring-0 ease-in-out focus:shadow-none shadow-none"
          :class="errors.length && errorClass" />
        <div :key="'label-' + index" class="ml-2 mr-4 block text-sm leading-5 text-gray-900" :class="{
          uppercase: isCapitalize,
        }">
          {{ option.label }}
        </div>
      </label>
    </div>
  </Field>
</template>

<script>
import Field from "@/Components/Global/Fields/Field.vue";
import FieldlessMixin from "./Mixins/FieldlessMixin.js";

export default {
  mixins: [FieldlessMixin],
  components: {
    Field,
  },
  props: {
    options: Array,
    modelValue: [String, Number],
    align: {
      type: String,
      default: "row",
      validator: (val) => ["row", "col"].includes(val),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    helperPosition: String,
    label: String,
    optionClass: String,
    isCapitalize: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selected: this.modelValue || [],
    };
  },
};
</script>
