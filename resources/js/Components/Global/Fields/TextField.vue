<template>
  <Field v-bind="{ ...$attrs, ...$props }">
    <slot name="prepend"></slot>
    <input :id="id" :placeholder="placeholder" v-model="localValue" v-bind="$attrs" :disabled="disabled"
      :required="required" @input="input" :type="localType"
      class="flex-1 block w-full transition duration-150 ease-in-out border-0 py-1.5 text-sm" :class="[inputClass, {
        'bg-transparent': !disabled,
        'bg-gray-50 cursor-not-allowed': disabled,
      }]" />
    <slot name="append"></slot>
  </Field>
</template>

<script>
import Field from "@/Components/Global/Fields/Field.vue";
import FieldMixin from "./Mixins/FieldMixin.js";

export default {
  mixins: [FieldMixin],
  components: {
    Field,
  },
  props: {
    id: {
      type: String,
      default: "id-text-field1",
    },
    label: String,
    subLabel: {
      type: String,
      default: "",
    },
    placeholder: String,
    type: String,
    modelValue: [String, Number],
    disabled: {
      type: Boolean,
      default: false,
    },
    required: {
      type: Boolean,
      default: false,
    },
    requiredTextOnly: {
      type: Boolean,
      default: false,
    },
    icon: {
      type: Boolean,
      default: false,
    },
    iconClass: {
      type: String,
      default: "bx bxs-check-circle",
    },
    classes: {
      type: String,
      default: "mb-2",
    },
    helpText: {
      type: String,
      default: "",
    },
    errors: {
      type: [Array, String],
      default: () => [],
    },
    small: {
      type: Boolean,
      default: true,
    },
    prefix: {
      type: String,
      default: "Rp",
    },
    append: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      localValue: this.modelValue,
    };
  },
  computed: {
    localType() {
      if (this.type === "currency") {
        return "text";
      }
      return this.type;
    },

    options() {
      if (this.type === "currency") {
        return {
          numeral: true,
          numeralThousandsGroupStyle: "thousand",
          numeralDecimalMark: ".",
          delimiter: ".",
        };
      }
    },
  },
  watch: {
    modelValue(next) {
      this.localValue = next;
    },
    type() {
      this.localType = this.type;
    },
  },
  methods: {
    restrictToNumbers(val) {
      let value = val ? val.toString() : "";
      return value ? value.replace(/[^0-9]/g, "") : value;
    },
    toCurrency(value) {
      value = this.restrictToNumbers(value);
      value = value ? value.replace(/\B(?=(\d{3})+(?!\d))/g, ".") : value;
      return value;
    },
    formatValue(value) {
      if (this.type === "phone") {
        value = this.restrictToNumbers(value);
      }
      if (this.type === "currency") {
        value = this.toCurrency(value);
      }
      return value;
    },
    input(e) {
      let value = e.target.value;

      this.localValue = value;
      value = this.formatValue(value);

      this.$emit("update:modelValue", value);
    },
  },
  mounted() {
    this.$emit("update:modelValue", this.formatValue(this.localValue));
  },
};
</script>
