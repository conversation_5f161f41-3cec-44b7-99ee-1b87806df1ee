<template>
  <Field v-bind="{ ...$attrs, ...$props }" class="relative">
    <div
      class="absolute h-full"
      :class="{
        'right-0': iconPosition === iconPositions[1],
        'left-0': iconPosition === iconPositions[0],
      }"
    >
      <dropdown
        ref="dropdown"
        :class="{
          'ml-3': iconPosition === iconPositions[1],
          'mr-3': iconPosition === iconPositions[0],
        }"
        class="h-full"
        :anchor="iconPosition === iconPositions[1] ? 'right' : 'left'"
        :disabled="disabled"
      >
        <template v-slot:label>
          <button
            type="button"
            class="bg-white h-full"
            :class="[
              disabled ? 'outline-none' : 'focus:outline-none',
              {
                'border-l rounded-r-md': iconPosition === iconPositions[1],
                'border-r rounded-l-md': iconPosition === iconPositions[0],
              },
              iconClass,
            ]"
            :disabled="disabled"
          >
            <slot name="icon">
              <icon-base
                name="Calendar"
                class="
                  fill-current
                  text-gray-400
                  hover:text-gray-500
                  h-full
                  mx-2
                "
              ></icon-base>
            </slot>
          </button>
        </template>
        <template v-slot:dropdown>
          <div>
            <VueCtkDateTimePicker
              v-model="localValue"
              no-shortcuts
              :min-date="localMinDate"
              no-keyboard
              no-header
              locale="id"
              :format="format"
              :only-date="type === types[0]"
              :range="type === types[1]"
              :only-time="type === types[2]"
              inline
              class="z-50"
            />
          </div>
        </template>
      </dropdown>
    </div>
    <div class="w-full">
      <input
        :id="id"
        ref="input"
        v-bind="$attrs"
        @focus="onFocus"
        type="text"
        v-model="localVal"
        autocomplete="none"
        :placeholder="placeholder"
        class="
          flex-1
          form-input
          border-0
          block
          w-full
          transition
          duration-150
          ease-in-out
          focus:outline-none focus:shadow-none
        "
        :class="[
          inputClass,
          disabled ? 'text-grey-40' : '',
          { 'pl-14': iconPosition === iconPositions[0] },
        ]"
        :disabled="disabled"
      />
    </div>
  </Field>
</template>

<script>
import { getCurrentInstance } from "vue";

import Field from "@/Components/Global/Fields/Field.vue";
import Dropdown from "@/Components/Global/Dropdowns/Base.vue";
import FieldMixin from "./Mixins/FieldMixin.js";

const TYPES = ["date", "daterange", "time"];
const ICON_POSITIONS = ["left", "right"];

export default {
  mixins: [FieldMixin],
  components: {
    Field,
    Dropdown,
  },
  props: {
    modelValue: [String, Object],
    type: {
      type: String,
      validator(val) {
        return TYPES.includes(val);
      },
    },
    format: {
      type: String,
      default: "DD MM YYYY",
    },
    formatLabel: String,
    iconPosition: {
      type: String,
      validator(val) {
        return ICON_POSITIONS.includes(val);
      },
      default: "right",
    },
    iconClass: {
      type: String,
      default: "",
    },
    minDate: {
      type: String,
    },
    id: {
      type: String,
      default() {
        return `text-input-${getCurrentInstance().uid}`;
      },
    },
  },
  data() {
    return {
      types: TYPES,
      iconPositions: ICON_POSITIONS,
      localValue: this.modelValue,
    };
  },
  computed: {
    localVal() {
      if (this.type === this.types[1]) {
        const values = [];
        if (this.localValue?.start) values[0] = this.localValue?.start;
        if (this.localValue?.end) values[1] = this.localValue?.end;
        return values.join(" - ");
      } else {
        if (this.formatLabel) {
          return this.moment(this.localValue, this.format).format(
            this.formatLabel
          );
        } else {
          return this.localValue;
        }
      }
    },
    localMinDate() {
      if (this.minDate) {
        let date = moment(this.minDate, this.format || "DD/MM/YYYY").format(
          "YYYY-MM-DD"
        );
        return date;
      }
    },
  },
  watch: {
    modelValue(next) {
      this.localValue = next;
    },
    localValue(val) {
      if (this.type === this.types[1]) {
        if (val?.start && val?.end) {
          this.$emit("update:modelValue", val);
        }
      } else {
        this.$emit("update:modelValue", val);
      }
    },
  },
  methods: {
    onFocus() {
      if (this.disabled) return;
      this.$refs.dropdown.showMenu();
    },
  },
};
</script>
