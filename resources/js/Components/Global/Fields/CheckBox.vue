<template>
  <Field v-bind="{ ...$attrs, ...$props }" label :helperPosition="helperPosition" borderless>
    <label class="flex items-center">
      <input v-model="model" @click="$emit('click', $event)" :value="val" :disabled="disabled" :checked="checked"
        type="checkbox" class="h-4 w-4 focus:ring-0 rounded text-green-500" :class="[
          errors.length && errorClass,
          { 'bg-gray-200': disabled },
          inputClass,
        ]" />
      <div :class="[
        labelClass
          ? labelClass
          : 'block text-sm leading-5 text-gray-500 font-medium',
        label ? 'ml-2' : '',
      ]">
        {{ label }}
      </div>
    </label>
  </Field>
</template>

<script>
import FieldlessMixin from "./Mixins/FieldlessMixin.js";

export default {
  mixins: [FieldlessMixin],
  props: {
    checked: Boolean,
    val: [String, Number, Array, Object],
    disabled: Boolean,
    modelValue: {
      required: false,
    },
    label: String,
    helperPosition: String,
    noLabelWrap: Boolean,
  },
  computed: {
    model: {
      get() {
        return this.checked || this.value;
      },
      set(val) {
        this.$emit("update:modelValue", val);
      },
    },
  },
};
</script>
