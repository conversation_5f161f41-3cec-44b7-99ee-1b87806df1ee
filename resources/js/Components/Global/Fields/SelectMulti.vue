<template>
  <Field v-bind="{ ...$attrs, ...$props }">
    <label class="flex rounded items-center w-full" :class="{ 'bg-gray-200': disabled }">

      <Multiselect :placeholder="placeholder" v-model="localValue" :options="options" :mode="mode" :multiple="multiple"
        :close-on-select="closeOnSelect === null ? !multiple : closeOnSelect" :clear-on-select="true"
        @search-change="onSearchChange" :searchable="searchable" :classes="{
          spinner: loading ? 'multiselect-spinner' : 'null',
          'is-disabled': disabled,
        }" :disabled="disabled">
        <template v-slot:nooptions>
          <span class="p-2 text-gray-500 text-xs">{{ noOptionsText }}</span>
        </template>
        <template v-slot:noresults>
          <span class="p-2 text-gray-500 text-xs">{{ noOptionsText }}</span>
        </template>
      </Multiselect>
    </label>
  </Field>
</template>

<script>
import FieldMixin from "./Mixins/FieldMixin.js";
import Multiselect from '@vueform/multiselect'
import "@vueform/multiselect/themes/default.css";

export default {
  mixins: [FieldMixin],
  components: {
    Multiselect,
  },
  props: {
    label: String,
    placeholder: String,
    required: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "text",
    },
    noOptionsText: {
      type: String,
      default: "The list is empty",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    helpText: {
      type: String,
      default: "",
    },
    errors: {
      type: [Array, String],
      default: () => [],
    },
    currentVal: String | Number,
    remote: Boolean,
    options: {
      type: Array,
      default: () => [],
    },
    mode: String,
    searchable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      localValue: this.currentVal ? this.currentVal : this.value,
    };
  },
  watch: {
    value(next) {
      this.localValue = next;
    },
    currentVal(data) {
      this.localValue = data;
    },
    localValue(next) {
      this.$emit("update:modelValue", next);
    },
  },
  methods: {
    onSearchChange(val) {
      if (this.remote) {
        this.$emit("searchChange", val);
      }
    },
  }
};
</script>

<style>
.multiselect {
  border: 0px solid #ced4da !important;
  font-size: 0.875rem !important;
  min-height: auto !important;
  padding: 0 !important;
}

.multiselect-wrapper {
  min-height: 32px !important;
}

.multiselect.multiselect-tag {
  padding: 2px 1px !important;
  font-size: 0.875rem !important;
}

.multiselect-option {
  padding-top: .3rem !important;
  padding-bottom: .3rem !important;
  font-size: 0.875rem !important;
}

.multiselect.is-disabled {
  background-color: #f3f4f6 !important;
}
</style>