<template>
  <Field v-bind="{ ...$attrs, ...$props }">
    <textarea
      :id="id"
      ref="input"
      v-bind="$attrs"
      :value="modelValue"
      :maxlength="maxLength"
      :placeholder="placeholder"
      @input="input"
      :rows="rows"
      :disabled="disabled"
      class="form-textarea block w-full transition duration-150 ease-in-out border-0 -my-px"
      :class="[
        { 'bg-gray-100': disabled, 'bg-transparent': !disabled },
        inputClass,
      ]"
    ></textarea>
  </Field>
</template>
<script>
import Field from "@/Components/Global/Fields/Field.vue";
import FieldMixin from "./Mixins/FieldMixin.js";

export default {
  mixins: [FieldMixin],
  props: {
    modelValue: String,
    rows: {
      type: [String, Number],
      default: 3,
    },
    maxLength: {
      type: Number,
      default: 524288,
    },
  },
  components: {
    Field,
  },
  methods: {
    input(e) {
      this.$emit("update:modelValue", e.target.value);
    },
  },
};
</script>
