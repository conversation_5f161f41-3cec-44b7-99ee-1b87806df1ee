<template>
  <Field v-bind="{ ...$attrs, ...$props }">
    <label
      class="flex rounded items-center w-full"
      :class="{ 'bg-gray-200': disabled }"
    >
      <select
        class="
          block
          w-full
          transition
          duration-150
          ease-in-out
          py-2
          px-3
          rounded
          bg-transparent
          text-sm
        "
        @input="input"
        :value="this.localValue"
        :required="required"
        :disabled="disabled"
        v-model="localValue"
      >
        <option value="">- {{ placeholder }} -</option>
        <option
          v-for="(item, index) in options"
          :key="index"
          :value="item.value || item.id"
          :selected="item.value == 216 ? 'selected' : ''"
        >
          {{ item.label || item.name }}
        </option>
      </select>
    </label>
  </Field>
</template>
<script>
import FieldMixin from "./Mixins/FieldMixin.js";

export default {
  mixins: [FieldMixin],
  props: {
    label: String,
    placeholder: String,
    required: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    helpText: {
      type: String,
      default: "",
    },
    options: {
      type: Array,
      default: () => [],
    },
    errors: {
      type: [Array, String],
      default: () => [],
    },
    currentVal: String | Number,
  },
  data() {
    return {
      localValue: this.currentVal ? this.currentVal : this.value,
    };
  },
  watch: {
    value(next) {
      this.localValue = next;
    },
    currentVal(data) {
      this.localValue = data;
    },
  },
  methods: {
    input(e) {
      let value = e.target.value;
      this.localValue = value;
      this.$emit("update:modelValue", value);
    },
  },
};
</script>
