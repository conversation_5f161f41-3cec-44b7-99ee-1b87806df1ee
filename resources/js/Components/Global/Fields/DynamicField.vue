<template>
    <template v-for="(field, index) in allFields">
      <TextField v-if="field.type == 'text' || field.type == 'date' || field.type == 'number'" :key="index"
      v-model="items[index].value" :type="field.type"
       :errors="$page.props.errors[`items.${index}.value`]"
      :placeholder="field.label" :label="field.label"  :containerClass="`col-span-1 `+ (index)" :id="field.value" :required="field.is_required"/>
      <TextArea v-else-if="field.type === 'textarea'" :containerClass="`col-span-1`"
      :id="field.value" :required="field.is_required" v-model="items[index].value"  :errors="$page.props.errors[`items.${index}.value`]"/>
      <RadioGroup v-else-if="field.type === 'radio'" :label="field.label" :containerClass="`col-span-1`" :options="field.option" :required="field.is_required"
      :id="field.value"  v-model="items[index].value"  :errors="$page.props.errors[`items.${index}.value`]"/>
      <SelectInput v-else-if="field.type === 'select'" :placeholder="field.label" :label="field.label" :containerClass="`col-span-1`" :options="field.option" :required="field.is_required" :id="field.value"
      v-model="items[index].value"  :errors="$page.props.errors[`items.${index}.value`]"/>
      <file-input
        :id="field.value"
        v-else-if="field.type === 'file'"
        :label="field.label"
        :cols="1"
        :previewUrl="items[index].value"
        :errors="$page.props.errors[`items.${index}.value`]"
        v-model="items[index].value"/>
      <CheckboxGroup v-else-if="field.type === 'checkbox'" :label="field.label" :containerClass="`col-span-1`" :options="field.option" :id="field.value" :required="field.is_required"
      v-model="items[index].value"  :errors="$page.props.errors[`items.${index}.value`]"/>
    </template>
</template>
<script>
import TextField from "@/Components/Global/Fields/TextField.vue";
import TextArea from "@/Components/Global/Fields/TextArea.vue";
import RadioGroup from "@/Components/Global/Fields/RadioGroup.vue";
import SelectInput from "@/Components/Global/Fields/SelectInput.vue";
import Multiselect from '@vueform/multiselect'
import FileInput from "@/Components/Global/Fields/FileInput.vue";
import CheckboxGroup from "@/Components/Global/Fields/CheckboxGroup.vue";
export default{
  components: {
    TextField,
    TextArea,
    RadioGroup,
    SelectInput,
    Multiselect,
    FileInput,
    CheckboxGroup
  },
  props: {
    fields: {
      type: Object,
      default: () => { },
    }
  },
  data() {
    return {
      allFields: [],
      items: []
    }
  },
  methods: {
    popoulateFields(){
      // loop fields
      this.fields.map((field) => {
        if(field.options && Array.isArray(field.options)){
          var newOptions = [];
          if(Array.isArray(field.options)){
            field.options.map((option) => {
                var childOptions = {
                  label: option,
                  value: option
                }
                newOptions.push(childOptions);
            });
            field.option = newOptions;
          }else{
            field.option = field.options;
          }

        }
      });
      this.allFields = this.fields;
      this.items = this.fields.map((field) => {
        var selected = field.selected;
        return {
          value: field.selected ? selected :  null,
          label: field.label,
          options: field.options,
          type: field.type,
          is_required: field.is_required,
          id: field.id
        }
      });
    }
  },
  watch: {
    items: {
      handler: function (val) {
        this.$emit('update:modelValue', val);
      },
      deep: true
    }
  },
  mounted(){
    this.popoulateFields()
  }
}
</script>
