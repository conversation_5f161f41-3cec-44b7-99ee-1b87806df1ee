<template>
  <Field v-bind="{ ...$attrs, ...$props }" :borderless="mode === modes[1]">
    <vue-editor
      v-show="mode === modes[0]"
      class="bg-white w-full rounded"
      :value="modelValue"
      v-bind="$attrs"
      :placeholder="placeholder"
      @input="$emit('input', $event)"
    ></vue-editor>
    <div v-show="mode === modes[1]" class="w-full">
      <div class="ql-container ql-snow">
        <div class="ql-editor">
          <span v-html="value"></span>
        </div>
      </div>
    </div>
  </Field>
</template>

<script>
import { VueEditor } from "vue2-editor";
import Field from "@/Components/Global/Fields/Field";
import FieldMixin from "./Mixins/FieldMixin.js";

const MODES = ["edit", "show"];

export default {
  mixins: [FieldMixin],
  components: {
    Field,
    VueEditor,
  },
  props: {
    value: String,
    mode: {
      type: String,
      validator(val) {
        return MODES.includes(val);
      },
      default: MODES[0],
    },
  },
  data() {
    return {
      modes: MODES,
    };
  },
};
</script>

<style>
.ql-container {
  height: unset !important;
  font-family: Montserrat !important;
}

.ql-toolbar.ql-snow {
  border-radius: 0.375rem 0.375rem 0 0;
  border-top: 0 !important;
  border-left: 0 !important;
  border-right: 0 !important;
  border-color: #d2d6dc;
}
.ql-container.ql-snow:not(.read-only) {
  border-radius: 0 0 0.375rem 0.375rem;
  border-color: #d2d6dc;
}

.ql-snow .ql-tooltip {
  position: unset !important;
}

.ql-container {
  border: 0 !important;
}

.ql-editor {
  color: initial !important;
}
</style>

<style scoped>
@import url("https://cdn.quilljs.com/1.3.6/quill.snow.css");
.ql-editor {
  min-height: unset;
  padding: 0 !important;
}
</style>
