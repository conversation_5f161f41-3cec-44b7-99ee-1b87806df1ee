<template>
  <div
    class="flex justify-between items-center text-sm"
    @click="toggleActive = !toggleActive"
  >
    <div
      class="w-11 h-6 flex items-center bg-gray-300 rounded-full p-1 duration-300 ease-in-out"
      :class="{ 'bg-blue-400': toggleActive }"
    >
      <div
        class="bg-white w-4 h-4 rounded-full shadow-md transform duration-300 ease-in-out"
        :class="{ 'translate-x-5': toggleActive }"
      ></div>
    </div>
    <div v-show="labels.length > 0" class="ml-2">
      {{ value ? labels[1] : labels[0] }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    labels: {
      type: Array,
      default: () => [],
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      toggleActive: this.value,
    };
  },
  watch: {
    toggleActive: {
      handler() {
        this.$emit("update:modelValue", this.toggleActive);
      },
      deep: true,
    },
  },
};
</script>

<style>
</style>