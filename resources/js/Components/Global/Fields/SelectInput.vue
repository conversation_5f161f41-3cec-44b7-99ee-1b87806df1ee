<template>
  <Field v-bind="{ ...$attrs, ...$props }">
    <slot name="prepend"></slot>
    <select class="flex-1 block w-full transition duration-150 ease-in-out border-0 bg-transparent py-1.5 text-sm"
      :class="{
        'cursor-not-allowed disabled:bg-slate-100': disabled,
        uppercase: isCapitalize,
      }" @input="input" :value="localValue" :required="required" :disabled="disabled">
      <option value="">- {{ placeholder }} -</option>
      <option v-for="(item, index) in allOptions" :key="index" :value="item.value || item.id">
        {{ item.label || item.name }}
      </option>
    </select>
    <slot name="append"></slot>
  </Field>
</template>
<script>
import Field from "@/Components/Global/Fields/Field.vue";
import FieldMixin from "./Mixins/FieldMixin.js";
import { debounce } from "lodash";

export default {
  mixins: [FieldMixin],
  components: {
    Field,
  },
  props: {
    label: String,
    placeholder: String,
    required: {
      type: Boolean,
      default: false,
    },
    helpText: {
      type: String,
      default: "",
    },
    options: {
      type: Array,
      default: () => [],
    },
    errors: {
      type: [Array, String],
      default: () => [],
    },
    currentVal: [String, Number],
    disabled: {
      type: Boolean,
      default: false,
    },
    selectorValue: {
      type: String,
      default: "value",
    },
    selectorLabel: {
      type: String,
      default: "label",
    },
    modelValue: [String, Number],
    isCapitalize: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      localValue: this.currentVal ? this.currentVal : this.modelValue,
    };
  },
  computed: {
    allOptions() {
      return this.options
        ? this.options.map((item) => {
          return {
            value: item[this.selectorValue] || item["id"],
            label: item[this.selectorLabel] || item["name"],
          };
        })
        : null;
    },
  },
  watch: {
    value(next) {
      this.localValue = next;
    },
    currentVal(data) {
      this.localValue = data;
    },
    options: {
      handler() {
        this.localValue = this.currentVal;
      },
      deep: true,
    },
  },
  methods: {
    input(e) {
      let value = e.target.value;
      this.localValue = value;
      this.$emit("update:modelValue", value);
    },
  },
};
</script>
