<template>
  <Field v-bind="{ ...$attrs, ...$props }">
    <div class="bg-white flex flex-row rounded w-full" :class="{ 'h-full': fluid }">
      <div v-show="!noPreview && preview"
        class="rounded-t overflow-hidden w-1/3 border-b border-r flex items-center flex-col p-2">
        <img v-if="preview" :src="preview" class=" object-contain rounded w-32 h-32" :alt="alt" />
        <div v-if="modelValue" class="flex items-center justify-between p-2">
          <div v-if="typeof modelValue === 'string'" class="flex-1 pr-1 text-sm text-gray-700">
            {{
              modelValue
                ? cutString(
                  modelValue
                    .split("/")
                    .slice(
                      modelValue.split("/").length - 1,
                      modelValue.split("/").length
                    )
                    .join("/")
                )
                : "..."
            }}
          </div>
          <div v-if="showFileName" class="flex-1 pr-1 text-sm">
            {{ cutString(modelValue.name) }}
            <span class="text-gray-500 text-sm">({{ filesize(modelValue.size) }})</span>
          </div>
        </div>
      </div>

      <input :id="id" ref="file" type="file" :accept="accept" @change="change" :class="{
        'border-red-300 text-red-900 placeholder-red-300 focus:border-red-300 focus:shadow-outline-red':
          errors.length,
      }" class="hidden" :multiple="multiple" />

      <div class=" flex flex-col justify-center" :class="modelValue ? 'w-2/3' : 'w-full'">
        <slot :value="modelValue" :preview="preview" :browse="browse" :remove="remove">
          <p class="text-blue-700 text-sm text-center cursor-pointer" @click="browse">
            <span class="icon-[hugeicons--cloud-upload] text-blue-700 text-xl"></span><br />
            Klik untuk upload<br />
            <span class="font-bold" v-html="helpText"></span>
          </p>
        </slot>
      </div>
    </div>
  </Field>
</template>
<script>
import Field from "@/Components/Global/Fields/Field.vue";
import FieldMixin from "./Mixins/FieldMixin.js";

export default {
  mixins: [FieldMixin],
  components: {
    Field,
  },
  props: {
    id: {
      type: String,
      default() {
        return `text-input-${this._uid}`;
      },
    },
    modelValue: [File, String],
    label: String,
    previewUrl: String,
    accept: {
      type: String,
      default: "*",
    },
    errors: {
      type: [Array, String],
      default: () => [],
    },
    helpText: String,
    required: {
      type: Boolean,
      default: false,
    },
    noPreview: {
      type: Boolean,
      default: false,
    },
    fluid: Boolean,
    multiple: Boolean,
  },
  data() {
    return {
      preview: null,
    };
  },
  watch: {
    modelValue: {
      handler(value) {
        try {
          console.log("Setting preview for modelValue:", value);
          if (!value) {
            this.preview = null;
            this.setpreview(null);
            this.$refs.file.value = "";
          } else if (value instanceof File) {
            var fileType = value.name.split(".").pop();
            if (!this.checkTypeImage(fileType)) {
              this.setpreview(fileType);
            } else {
              this.readfile(value);
            }
          } else if (typeof value === "string") {
            this.setpreview(value.split(".").pop());
          }
        } catch (error) {
          console.error("Error setting preview:", error);
        }
      },
    },
  },
  computed: {
    showFileName() {
      return this.modelValue && typeof this.modelValue !== "string";
    },
  },
  mounted() {
    if (typeof this.modelValue === "string") {
      var type = this.modelValue.split(".").pop();
      this.setpreview(type);
    } else if (this.modelValue) {
      this.readfile(this.modelValue);
    }
  },
  methods: {
    filesize(size) {
      var i = Math.floor(Math.log(size) / Math.log(1024));
      return (
        (size / Math.pow(1024, i)).toFixed(2) * 1 +
        " " +
        ["B", "kB", "MB", "GB", "TB"][i]
      );
    },
    browse() {
      this.$refs.file.click();
    },
    setpreview(type) {
      if (this.checkTypeImage(type)) {
        this.preview = this.modelValue;
      } else {
        var url = "/images/icon/file.png";
        if (['xlsx', 'xls'].includes(type)) {
          url = "/images/icon/excel.png";
        } else if (type == 'pdf') {
          url = "/images/icon/pdf.png";
        }
        this.preview = url;
      }
    },
    checkTypeImage(type) {
      return ["jpg", "jpeg", "png", "gif", "svg", 'webp'].includes(type);
    },
    clear() {
      this.$refs["file"].value = "";
    },
    change(e) {
      if (e.target.files && this.multiple) {
        const files = Array.from(e.target.files);
        this.$emit("update:modelValue", files, { clear: this.clear });
      } else {
        this.$emit("update:modelValue", e.target.files[0]);
        this.readfile(e.target.files[0]);
      }
    },
    readfile(file) {
      var reader = new FileReader();
      reader.onload = (val) => {
        if (val.target.result.includes("data:image"))
          this.preview = val.target.result;

        this.$emit("getPreview", val.target.result);
      };
      reader.readAsDataURL(file); // convert to base64 string

    },
    remove() {
      this.preview = "";
      this.$emit("update:modelValue", null);
    },
    cutString(val) {
      if (!val) {
        return;
      }
      const start = val.length > 20 ? val.length - 20 : 0;
      const dot = val.length > 20 ? "..." : "";
      return dot + val.substring(start, val.length);
    },
  },
};
</script>
