<template>
  <div :id="`field-wrapper-${id}`" class="col-span-1 mb-3">
    <field-label classes="mb-2" :label="label" :required="required || requiredLabelOnly"></field-label>
    <div class="flex space-x-2 text-sm">
      <div class="flex-1">
        <div class="relative flex rounded-sm border border-gray-200 items-center field-container" :class="classes">
          <select :id="`year-field-${id}`"
            class="form-input block w-full transition duration-150 ease-in-out border-0 bg-transparent py-1.5 text-sm"
            placeholder="Tahun" v-model="localValueYear">
            <option value="">- Tahun -</option>
            <option v-for="(item, index) in yearOptions" :key="index" :value="item.value">
              {{ item.label }}
            </option>
          </select>
        </div>
      </div>
      <div class="flex-1">
        <div class="relative flex rounded-sm border border-gray-200 items-center field-container" :class="classes">
          <select :id="`month-field-${id}`"
            class="form-input block w-full transition duration-150 ease-in-out border-0 bg-transparent py-1.5 text-sm"
            placeholder="Bulan" v-model="localValueMonth">
            <option value="">- Bulan -</option>
            <option v-for="(item, index) in monthOptions" :key="index" :value="item.value">
              {{ item.label }}
            </option>
          </select>
        </div>
      </div>
      <div class="flex-1">
        <div class="relative flex rounded-sm border border-gray-200 items-center field-container" :class="classes">
          <input :id="`date-field-${id}`" placeholder="Tanggal" type="number" min="1" :max="maxDate"
            v-model="localValueDate" class="text-sm py-1.5 px-4 rounded-sm w-full border-0 bg-transparent" />
        </div>
      </div>
    </div>
    <p class="text-red-600 text-xs float-left error-field mb-2" v-show="!!errors && errors.length"
      :id="`field-errors-${id}`">
      {{ errors }}
    </p>
  </div>
</template>

<script>
import FieldLabel from "@/Components/Global/Fields/Label.vue";
import FieldMixin from "./Mixins/FieldMixin.js";

export default {
  mixins: [FieldMixin],
  components: {
    FieldLabel
  },
  props: {
    id: {
      type: String,
      default: "id-text-field1",
    },
    label: String,
    subLabel: {
      type: String,
      default: "",
    },
    placeholder: String,
    type: String,
    modelValue: [String, Number],
    disabled: {
      type: Boolean,
      default: false,
    },
    required: {
      type: Boolean,
      default: false,
    },
    requiredTextOnly: {
      type: Boolean,
      default: false,
    },
    classes: {
      type: String,
      default: "mb-3",
    },
    helpText: {
      type: String,
      default: "",
    },
    errors: {
      type: [Array, String],
      default: () => [],
    },
  },
  data() {
    return {
      localValue: this.modelValue,
      localValueDate: null,
      localValueMonth: null,
      localValueYear: null,
    };
  },
  computed: {
    monthOptions() {
      return [
        { value: 1, label: "Januari" },
        { value: 2, label: "Februari" },
        { value: 3, label: "Maret" },
        { value: 4, label: "April" },
        { value: 5, label: "Mei" },
        { value: 6, label: "Juni" },
        { value: 7, label: "Juli" },
        { value: 8, label: "Agustus" },
        { value: 9, label: "September" },
        { value: 10, label: "Oktober" },
        { value: 11, label: "November" },
        { value: 12, label: "Desember" },
      ];
    },
    yearOptions() {
      let years = [];
      for (let i = 1985; i <= new Date().getFullYear(); i++) {
        years.push({
          value: i,
          label: i,
        });
      }
      return years
    },
    maxDate() {
      if (this.localValueYear && this.localValueMonth) {
        let maxDate = new Date(this.localValueYear, this.localValueMonth, 0).getDate();
        return maxDate;
      }
    }
  },
  watch: {
    modelValue(next) {
      this.localValue = next;
    },
    localValue(next) {
      this.splitValues(next)
    },
    localValueDate(next) {
      this.localValue = `${this.localValueYear}-${this.localValueMonth}-${next}`;
      this.$emit("update:modelValue", this.localValue);
    },
    localValueMonth(next) {
      this.localValue = `${this.localValueYear}-${next}-${this.localValueDate}`;
      this.$emit("update:modelValue", this.localValue);
    },
    localValueYear(next) {
      this.localValue = `${next}-${this.localValueMonth}-${this.localValueDate}`;
      this.$emit("update:modelValue", this.localValue);
    },
  },
  methods: {
    splitValues(val) {
      if (val) {
        let date = val.split("-");
        this.localValueDate = parseInt(date[2]);
        this.localValueMonth = parseInt(date[1]);
        this.localValueYear = date[0];
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.splitValues(this.localValue);
    }, 50)
  },
};
</script>
