<template>
  <div class="flex flex-col" :class="containerClass">
    <div v-show="label" class="mb-2">
      <field-label :classes="labelClass" :label="label" :id="id"
        :required="required || requiredLabelOnly"></field-label>
    </div>
    <div>
      <div class=" rounded" :class="[
        {
          border: !borderless,
        }
      ]">
        <slot></slot>
      </div>
    </div>
    <div>
      <div class="text-gray-500 text-xs" :id="`field-helper-${id}`" v-html="helpText"></div>
      <div class="text-red-600 text-xs error-field mb-2" v-show="!!errors && errors.length" :id="`field-errors-${id}`">
        {{ errors }}
      </div>
    </div>
  </div>
</template>

<script>
import ErrorValidationIcon from "@/Components/Global/Fields/ErrorValidationIcon.vue";
import FieldLabel from "@/Components/Global/Fields/Label.vue";

const HELPER_POSITION = ["bottom", "top"];

export default {
  inheritAttrs: false,
  props: {
    id: {
      type: String,
    },
    type: {
      type: String,
      default: "text",
    },
    label: String,
    errors: {
      type: [Array, String],
      default: () => [],
    },
    helpText: String,
    helperPosition: {
      type: String,
      default: "bottom",
      validator(val) {
        return HELPER_POSITION.includes(val);
      },
    },
    required: {
      type: Boolean,
      default: false,
    },
    requiredLabelOnly: {
      type: Boolean,
      default: false,
    },
    labelPosition: {
      type: String,
      default: "top",
    },
    cols: {
      type: Number,
      default: 1,
    },
    inline: Boolean,
    borderless: {
      type: Boolean,
      default: false,
    },
    labelClass: String,
    containerClass: String,
    classes: String,
    errorClass: String,
    fluid: Boolean,
  },
  components: {
    ErrorValidationIcon,
    FieldLabel,
  },
};
</script>

<style scoped>
.font-9 {
  font-size: 9pt;
}

.field-width {
  width: 120px;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px white inset;
  box-shadow: 0 0 0px 1000px white inset;
}
</style>
