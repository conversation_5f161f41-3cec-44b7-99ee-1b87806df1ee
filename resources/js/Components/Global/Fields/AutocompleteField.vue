<template>
  <Field v-bind="{ ...$attrs, ...$props }">
    <slot name="prepend"></slot>
    <div class="autocomplete w-full">
      <input
        class="flex-1 form-input block w-full transition duration-150 ease-in-out border-0 bg-transparent"
        type="text"
        @input="onChange"
        v-model="localValue"
        :placeholder="placeholder"
        @keydown.enter="onEnter"
      />
      <ul
        id="autocomplete-results"
        v-show="isOpen"
        class="autocomplete-results"
      >
        <li class="loading" v-if="isLoading">Loading results...</li>
        <li
          v-else
          v-for="(result, i) in results"
          :key="i"
          @click="setResult(result)"
          class="cursor-pointer hover:bg-blue-500 hover:text-white active active:bg-blue-500 active:text-white active p-2"
        >
          {{ result }}
        </li>
      </ul>
    </div>
    <slot name="append"></slot>
  </Field>
</template>

<script>
import Field from "@/Components/Global/Fields/Field.vue";
import FieldMixin from "./Mixins/FieldMixin.js";

export default {
  mixins: [FieldMixin],
  components: {
    Field,
  },
  props: {
    modelValue: [String, Number],
    items: {
      type: Array,
      required: false,
      default: () => [],
    },
    isAsync: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      isOpen: false,
      results: [],
      localValue: this.modelValue,
      isLoading: false,
    };
  },
  watch: {
    items: function (value, oldValue) {
      if (value.length !== oldValue.length) {
        this.results = value;
        this.isLoading = false;
      }
    },
  },
  mounted() {
    document.addEventListener("click", this.handleClickOutside);
  },
  destroyed() {
    document.removeEventListener("click", this.handleClickOutside);
  },
  methods: {
    setResult(result) {
      this.localValue = result;
      this.isOpen = false;
    },
    filterResults() {
      this.results = this.items.filter((item) => {
        return item.toLowerCase().indexOf(this.localValue.toLowerCase()) > -1;
      });
    },
    onChange() {
      this.$emit("update:modelValue", this.localValue);

      if (this.isAsync) {
        this.isLoading = true;
      } else {
        this.filterResults();
        this.isOpen = true;
      }
    },
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.isOpen = false;
      }
    },
    onEnter() {
      this.isOpen = false;
    },
  },
};
</script>

<style>
.autocomplete {
  position: relative;
}
/* 
.autocomplete-results {
  padding: 0;
  margin: 0;
  border: 1px solid #eeeeee;
  height: 120px;
  overflow: auto;
}

.autocomplete-result {
  list-style: none;
  text-align: left;
  padding: 4px 2px;
  cursor: pointer;
} */

.autocomplete-result.is-active,
.autocomplete-result:hover {
  background-color: #4aae9b;
  color: white;
}
</style>