export default {
    methods: {
        sort(sort, order) {
            this.query.sort = sort;
            this.query.order = order;
            this.handleQueryChange();
        },
        handleQueryChange() {
            if (this.key > 0) {
                this.$inertia.visit(window.location.pathname, {
                    data: this.query,
                    preserveScroll: true,
                    preserveState: true,
                    replace: true,
                });
            }
        },
        changePerPage(perPage) {
            this.query.per_page = perPage;
            this.handleQueryChange();
        },
        changeSearch(search) {
            this.query.search = search;
            this.handleQueryChange();
        },
        rowNumber(index) {
            let page = this.requestQuery?.page || 1;
            const newIndex = Number(index);
            return (
                page * this.query.per_page - this.query.per_page + 1 + newIndex
            );
        },
        clear() {
            for (let i in this.filters) {
                if (this.get(this.query, this.filters[i].paramName) != null) {
                    delete this.query[this.filters[i].paramName];
                }
            }
            this.query.search = null;
        },
        get(object, key) {
            return object[key] ?? null;
        },
    },
};
