export default {
    methods: {
        getStatusBgColor(status) {
            switch (status) {
                case "Inactive":
                    return "bg-red-100";
                case "Active":
                    return "bg-green-100";
                default:
                    return "bg-gray-100";
            }
        },
        getStatusColor(status) {
            switch (status) {
                case "Inactive":
                    return "text-red-600";
                case "Active":
                    return "text-green-600";
                default:
                    return "text-gray-600";
            }
        },
        getRegStatusBgColor(status) {
            if (status === "WAITING") {
                return "bg-lime-100";
            } else if (
                status === "NEED_REVIEW" ||
                status === "RAB_RECEIVED" ||
                status === "TERTIARY" ||
                status === "PAYMENT" ||
                status === "PAYMENT_REVIEW" ||
                status === "PAYMENT_DECLINED" ||
                status === "PAID" ||
                status === "VALIDATED" ||
                status === "INSTALL"
            ) {
                return "bg-blue-100";
            } else if (status === "CANCELED") {
                return "bg-red-100";
            } else if (status === "FINISHED") {
                return "bg-green-100";
            } else {
                return "bg-gray-100";
            }
        },
        getRegStatusColor(status) {
            if (status === "WAITING") {
                return "text-lime-600";
            } else if (
                status === "NEED_REVIEW" ||
                status === "RAB_RECEIVED" ||
                status === "TERTIARY" ||
                status === "PAYMENT" ||
                status === "PAYMENT_REVIEW" ||
                status === "PAYMENT_DECLINED" ||
                status === "PAID" ||
                status === "VALIDATED" ||
                status === "INSTALL"
            ) {
                return "text-blue-600";
            } else if (status === "CANCELED") {
                return "text-red-600";
            } else if (status === "FINISHED") {
                return "text-green-600";
            } else {
                return "text-gray-600";
            }
        },
    },
};
