export default {
    methods: {
        getActStatusBgColor(status) {
            if (
                status === "WAITING" ||
                status === "DRAFT" ||
                status === "NEED_REVIEW"
            ) {
                return "bg-lime-100";
            } else if (
                status === "PROGRESS" ||
                status === "PAYMENT" ||
                status === "PAYMENT_REVIEW" ||
                status === "PAID" ||
                status === "RAB_RECEIVED" ||
                status === "TERTIARY" ||
                status === "VALIDATED" ||
                status === "INSTALL"
            ) {
                return "bg-blue-100";
            } else if (
                status === "PAYMENT_DECLINED" ||
                status === "CANCELED" ||
                status === "CANCELED_BY_CUSTOMER" ||
                status === "CANCELED_BY_ADMIN"
            ) {
                return "bg-red-100";
            } else if (status === "DONE" || status === "FINISHED") {
                return "bg-green-100";
            } else {
                return "bg-gray-100";
            }
        },
        getActStatusColor(status) {
            if (
                status === "WAITING" ||
                status === "DRAFT" ||
                status === "NEED_REVIEW"
            ) {
                return "text-lime-600";
            } else if (
                status === "PROGRESS" ||
                status === "PAYMENT" ||
                status === "PAYMENT_REVIEW" ||
                status === "PAID" ||
                status === "RAB_RECEIVED" ||
                status === "TERTIARY" ||
                status === "VALIDATED" ||
                status === "INSTALL"
            ) {
                return "text-blue-600";
            } else if (
                status === "PAYMENT_DECLINED" ||
                status === "CANCELED" ||
                status === "CANCELED_BY_CUSTOMER" ||
                status === "CANCELED_BY_ADMIN"
            ) {
                return "text-red-600";
            } else if (status === "DONE" || status === "FINISHED") {
                return "text-green-600";
            } else {
                return "text-gray-600";
            }
        },
    },
};
