export default {
    props: {
        currentUrl: {
            type: String,
            required: false,
        },
    },
    data() {
        return {
            activeUrl: "",
        };
    },
    computed: {
        navItems() {
            return this.menus.map((item) => {
                return {
                    authorized: item.children
                        ?.map((children) => {
                            return children.authorized;
                        })
                        .find((children) => children),
                    ...item,
                    children: item.children?.map((children) => ({
                        ...children,
                        isActive:
                            this.getCurrentMenu(this.activeUrl, 1) ===
                            this.getCurrentMenu(
                                route(children.routeName).toString(),
                                1
                            ),
                        children: children.children?.map((children) => ({
                            ...children,
                            isActive:
                                this.getCurrentMenu(this.activeUrl, 2) ===
                                this.getCurrentMenu(
                                    route(children.routeName).toString(),
                                    2
                                ),
                        })),
                    })),
                    isActive: item.children
                        ? this.getCurrentMenu(this.activeUrl) === item.name ||
                          this.isChildActive(item.children)
                        : this.getCurrentMenu(this.activeUrl) ===
                          this.getCurrentMenu(route(item.routeName).toString()),
                };
            });
        },
    },
    watch: {
        $page: {
            immediate: true,
            handler(val) {
                this.activeUrl = window.location.href;
            },
        },
    },
    methods: {
        getCurrentMenu(url, count = 1, param = false) {
            const indexBasePath = 3;
            const menuPathArray = url.split("/");
            const slicedPath = menuPathArray
                .slice(indexBasePath, indexBasePath + count)
                .join("/");
            const params = slicedPath.split("?");
            var removedParams = params[0];
            if (params[1] && param) {
                removedParams = removedParams + "?" + params[1];
            }
            return removedParams;
        },
        isChildActive(item) {
            let parentActive = false;
            item?.map((children) => {
                if (
                    this.getCurrentMenu(this.activeUrl, 1) ===
                    this.getCurrentMenu(route(children.routeName).toString(), 1)
                ) {
                    parentActive = true;
                }
            });
            return parentActive;
        },
    },
};
