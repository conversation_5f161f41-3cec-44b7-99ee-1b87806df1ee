<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <link rel="icon" href="/images/favicon.png">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title inertia>{{ config('app.name', 'Laravel') }}</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Sistem Informasi Manajemen Pelanggan - Perumdam Tirta Pandalungan" />
    <meta name="keywords" content="tiketvisa,onward ticket" />
    <meta name="robots" content="index, follow, max-snippet:-1, max-video-preview:-1, max-image-preview:large" />

    <meta itemprop="name" content="Perumdam Tirta Pandalungan" />
    <meta itemprop="description" content="Sistem Informasi Manajemen Pelanggan - Perumdam Tirta Pandalungan" />
    <meta itemprop="image" content="{{ asset('images/banner.png') }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Perumdam Tirta Pandalungan" />
    <meta property="og:description" content="Sistem Informasi Manajemen Pelanggan - Perumdam Tirta Pandalungan" />
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}" />
    <meta property="og:image" content="{{ asset('images/banner.png') }}">
    <meta property="og:site_name" content="{{ config('app.name', 'TiketVisa') }}" />
    <meta property="og:locale" content="{{ str_replace('_', '-', app()->getLocale()) }}" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Perumdam Tirta Pandalungan" />
    <meta name="twitter:description" content="Sistem Informasi Manajemen Pelanggan - Perumdam Tirta Pandalungan" />
    <meta name="twitter:image" content="{{ asset('images/banner.png') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @routes
    @vite(['resources/js/app.js', "resources/js/Pages/{$page['component']}.vue"])
    @inertiaHead
</head>

<body class="antialiased bg-gray-100">
    @inertia
</body>

</html>