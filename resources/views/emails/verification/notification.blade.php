<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $subject }}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7fafc;
            line-height: 1.5;
        }

        .container {
            max-width: 580px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .header {
            background: linear-gradient(135deg, #1E73BE 0%, #2A93D5 100%);
            padding: 24px 20px;
            text-align: center;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
        }

        .logo {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
            letter-spacing: 0.5px;
        }

        .header-subtitle {
            color: #e3f2fd;
            font-size: 12px;
            font-weight: 400;
        }

        .content {
            padding: 24px 20px;
        }

        .greeting {
            font-size: 16px;
            color: #2d3748;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .message {
            font-size: 14px;
            color: #718096;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 600;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-verified {
            background-color: #f0fff4;
            color: #38a169;
            border: 1px solid #9ae6b4;
        }

        .status-rejected {
            background-color: #fed7d7;
            color: #e53e3e;
            border: 1px solid #feb2b2;
        }

        .status-waiting {
            background-color: #fef5e7;
            color: #d69e2e;
            border: 1px solid #f6e05e;
        }

        .status-unverified {
            background-color: #edf2f7;
            color: #4a5568;
            border: 1px solid #cbd5e0;
        }

        .card {
            background-color: #f8fafc;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #e2e8f0;
        }

        .card-title {
            font-size: 14px;
            font-weight: 600;
            color: #1E73BE;
            margin: 0 0 12px 0;
        }

        .detail-row {
            margin-bottom: 8px;
        }

        .detail-label {
            font-size: 12px;
            font-weight: 500;
            color: #718096;
            margin-bottom: 2px;
        }

        .detail-value {
            font-size: 13px;
            color: #2d3748;
            word-break: break-word;
        }

        .bonus-highlight {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 6px;
            padding: 12px;
            margin: 16px 0;
            text-align: center;
        }

        .bonus-text {
            color: #38a169;
            font-weight: 600;
            font-size: 14px;
        }

        .footer {
            background-color: #f7fafc;
            padding: 16px 20px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .footer-text {
            color: #a0aec0;
            font-size: 11px;
            line-height: 1.5;
        }

        .footer-brand {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 4px;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }

        @media (max-width: 600px) {
            .container {
                margin: 0 10px;
            }

            .content {
                padding: 20px 16px;
            }

            .header {
                padding: 20px 16px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo">Perumdam Tirta Pandalungan</div>
            <div class="header-subtitle">Kabupaten Jember</div>
        </div>

        <div class="content">
            <div class="greeting">{{ $subject }}</div>
            <div class="message">{{ $msg }}</div>

            <div class="status-badge status-{{ strtolower($verificationStatus) }}">
                {{ \App\Models\Customer\Enum\VerificationStatus::getDescription($verificationStatus) }}
            </div>

            @if($verificationStatus === 'VERIFIED')
            <div class="bonus-highlight">
                <div class="bonus-text">🎉 Bonus Poin Telah Ditambahkan!</div>
            </div>
            @endif

            <div class="card">
                <div class="card-title">Detail Akun</div>

                <div class="detail-row">
                    <div class="detail-label">Nama Lengkap</div>
                    <div class="detail-value">{{ $customer->name }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Email</div>
                    <div class="detail-value">{{ $customer->email }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Nomor Telepon</div>
                    <div class="detail-value">{{ $customer->phone ?? '-' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Nomor KTP</div>
                    <div class="detail-value">{{ $customer->no_ktp ?? '-' }}</div>
                </div>

                <div class="detail-row">
                    <div class="detail-label">Status Verifikasi</div>
                    <div class="detail-value">{{ \App\Models\Customer\Enum\VerificationStatus::getDescription($verificationStatus) }}</div>
                </div>

                @if($customer->verified_at)
                <div class="detail-row">
                    <div class="detail-label">Waktu Verifikasi</div>
                    <div class="detail-value">{{ \Carbon\Carbon::parse($customer->verified_at)->format('d M Y H:i') }} WIB</div>
                </div>
                @endif
            </div>

            @if($verificationStatus === 'REJECTED')
            <div class="card">
                <div class="card-title">Langkah Selanjutnya</div>
                <div class="detail-value">
                    Silakan periksa kembali data yang Anda kirimkan dan pastikan:
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>Data diri terisi dengan benar</li>
                        <li>Foto KTP jelas dan dapat dibaca</li>
                        <li>Nomor KTP valid</li>
                    </ul>
                    Anda dapat mengajukan verifikasi ulang melalui aplikasi.
                </div>
            </div>
            @endif
        </div>

        <div class="footer">
            <div class="footer-brand">Perumdam Tirta Pandalungan</div>
            <div class="footer-text">
                Kabupaten Jember<br>
                Email otomatis - mohon tidak membalas
            </div>
        </div>
    </div>
</body>

</html>