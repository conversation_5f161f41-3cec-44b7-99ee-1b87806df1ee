<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Data User</title>
</head>
<style>
    .table {
        border-collapse: collapse;
        width: 100%;
    }

    .table td,
    .table th {
        text-align: left;
        padding: 6px;
    }

    @media print {
        button {
            display: none;
        }
    }
</style>

<body>
    @if ($type == 'print')
        <button onclick="window.print()">Print</button>
    @endif
    <h3>Data User</h3>
    @if ($request['filter-role'])
        Role: {{ $request['filter-role'] }}
    @endif
    <table class="table" border='1' aria-label="student-table">
        <thead>
            <tr>
                <th>No</th>
                <th>NIP</th>
                <th>Nama</th>
                <th>Email</th>
                <th>Fungsional</th>
                <th>Departemen</th>
                <th>Unit</th>
                <th>Role</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($resources as $item)
                <tr>
                    <td>{{ $loop->iteration }}</td>
                    <td>{{ $item->nip }}</td>
                    <td>{{ $item->name}}</td>
                    <td>{{ $item->email }}</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>
                        {{-- @foreach ($item->roles as $role)
                            {{ $role->name }}
                        @endforeach --}}
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    <script>
        window.print();
    </script>
</body>

</html>
