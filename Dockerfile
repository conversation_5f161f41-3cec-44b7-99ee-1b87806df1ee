# Use the official PHP image with a lightweight variant
FROM php:8.3-fpm-alpine

# Install dependencies, including zip
RUN apk add --no-cache \
    openssh-client \
    git \
    curl \
    bash \
    zip \
    && docker-php-ext-install pdo_mysql

# Install Node.js 22.x
RUN apk add --no-cache nodejs-current npm

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install SCP (provided by OpenSSH)
RUN apk add --no-cache openssh-client

# Set up SSH (optional)
RUN ssh-keygen -A

# Set the working directory
WORKDIR /var/www/html

# Expose port 9000 for PHP-FPM
EXPOSE 9000

# Command to start PHP-FPM
CMD ["php-fpm"]
