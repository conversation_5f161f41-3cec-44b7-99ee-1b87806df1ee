#!/bin/sh
# change correct php version
rm -f /usr/bin/php
ln -s /www/server/php/83/bin/php /usr/bin/php
php -v

# artisan:config:cache
php artisan config:clear

# artisan:view:clear
php artisan view:clear

# artisan:route:clear
php artisan route:clear

# artisan:optimize:clear
php artisan optimize:clear

# copy .env.example to .env
cp .env.example .env

# run key generate
php artisan key:generate

#artisan:storage:link
php artisan storage:link

# run migrations
php artisan migrate

# run seeders
# php artisan db:seed

# artisan:config:cache
php artisan config:cache

# artisan:view:cache
php artisan view:cache

# artisan:route:cache
php artisan route:cache

php artisan log-viewer:publish

# artisan:optimize
php artisan optimize

# print end message with green color with robot emoji
echo -e "\e[32m🤖 Lapor! Wes mari gan.. \e[0m"
