<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT', 'http://localhost:8000/api/v1/auth/login/google/callback'),
    ],

    'simbio' => [
        'url' => env('SIMBIO_URL', 'http://localhost:8000'),
        'username' => env('SIMBIO_USERNAME', 'admin'),
        'password' => env('SIMBIO_PASSWORD', 'admin'),
    ],

    'simpeg' => [
        'url' => env('SIMPEG_URL', 'http://localhost:8000'),
        'username' => env('SIMPEG_USERNAME', 'admin'),
        'password' => env('SIMPEG_PASSWORD', 'admin'),
    ],

];
